#! /bin/bash

###
 # @Description:
 # @version:
 # @Author: shiqi.li
 # @Date: 2025-07-09 16:26:27
 # @LastEditTime: 2025-07-09 16:26:28
 # @LastEditors: shiqi.li
 # @FilePath: /hv_percep_base/ci/scripts/push_generic.sh
###


# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 显示帮助信息
show_help() {
    echo "Usage: $0 <file_path> [target_path]"
    echo "  file_path: 要上传的本地文件路径（可以是相对路径或绝对路径）"
    echo "  target_path: (可选) 在仓库中的目标路径，默认为文件名"
    echo "Example: $0 ./dummy.tar.gz https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/dummy.tar.gz"
    exit 1
}

# 检查参数
if [ $# -lt 1 ] || [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
    show_help
fi

# 获取认证信息
SECRETS_FILE="$SCRIPT_DIR/../secrets/ci_agent"
if [ ! -f "$SECRETS_FILE" ]; then
    echo "Error: Secrets file not found at $SECRETS_FILE"
    exit 1
fi

# 读取认证信息
CREDENTIALS=$(cat "$SECRETS_FILE")
if [ -z "$CREDENTIALS" ]; then
    echo "Error: Empty credentials file"
    exit 1
fi

# 获取参数并转换为绝对路径
FILE_PATH="$1"
if [[ ! "$FILE_PATH" = /* ]]; then
    # 如果是相对路径，转换为绝对路径
    FILE_PATH="$(cd "$(dirname "$FILE_PATH")" && pwd)/$(basename "$FILE_PATH")"
fi

TARGET_PATH="${2:-$(basename "$FILE_PATH")}"

# 检查文件是否存在
if [ ! -f "$FILE_PATH" ]; then
    echo "Error: File '$FILE_PATH' does not exist"
    exit 1
fi

# 构建完整的URL
REPO_URL="$TARGET_PATH"

echo "Uploading $FILE_PATH to $REPO_URL"

# 上传文件
curl -v --user "$CREDENTIALS" \
    --upload-file "$FILE_PATH" \
    "$REPO_URL"
