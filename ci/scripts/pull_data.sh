#!/bin/bash
###
 # @Description: Simple script to pull and extract test data from Nexus
 # @version: 1.0.0
 # @Author: shiqi.li
 # @Date: 2025-07-09 20:27:47
 # @LastEditTime: 2025-07-28 14:02:00
 # @LastEditors: <EMAIL> <EMAIL>
 # @FilePath: /hv_percep_perceptron/hv_percep_base/ci/scripts/pull_data.sh
###

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
NEXUS_URL="https://nexus3.hellobike.cn"
REPOSITORY="hv-generic-dev"
BASE_PATH="hv-percep/test_data"
OUTPUT_DIR="data"
FORCE=false
VERBOSE=false
DRY_RUN=false

# 数据版本配置 - 添加新版本时请更新此列表
# 格式: "版本号:文件名"（按版本从新到旧排序）
#
# 如何添加新版本：
# 1. 在VERSION_MAP中添加新的版本映射
# 2. 在VERSION_ORDER数组的开头添加新版本号
# 3. 脚本会自动使用最新版本（VERSION_ORDER中的第一个）
#
# 示例：添加v0.0.4版本
# VERSION_MAP["v0.0.4"]="data_v0.0.4_20250801_120000.tar.gz"
# VERSION_ORDER=("v0.0.4" "v0.0.3" "v0.0.2" "v0.0.1")
declare -A VERSION_MAP=(
    ["v0.0.3"]="data_v0.0.3_20250721_191712.tar.gz"
    ["v0.0.2"]="data_v0.0.2_20250721_180442.tar.gz"
    ["v0.0.1"]="data_v0.0.1_20250709_164553.tar.gz"
)

# 版本顺序（从新到旧）- 新版本请添加到数组开头
VERSION_ORDER=("v0.0.3" "v0.0.2" "v0.0.1")

# 日志函数
log() {
    local level=$1
    shift
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $*"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $*"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $*"
            ;;
        "DEBUG")
            if [ "$VERBOSE" = true ]; then
                echo -e "${BLUE}[DEBUG]${NC} $*"
            fi
            ;;
    esac
}

# 显示使用方法
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Smart script to pull and extract test data from Nexus (no authentication required)"
    echo "Automatically discovers and downloads the latest version if no version is specified."
    echo ""
    echo "OPTIONS:"
    echo "  -v, --version <version>  Specific version to download (v0.0.1, v0.0.2, v0.0.3)"
    echo "  -f, --force              Force overwrite existing data directory"
    echo "  --verbose                Verbose output"
    echo "  -n, --dry-run            Show what would be downloaded without actual download"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Available versions:"
    for i in "${!VERSION_ORDER[@]}"; do
        local version="${VERSION_ORDER[$i]}"
        local file="${VERSION_MAP[$version]}"
        if [ $i -eq 0 ]; then
            echo "  $version - $file (latest)"
        else
            echo "  $version - $file"
        fi
    done
    echo ""
    echo "Examples:"
    echo "  $0                       # Auto-download latest version (recommended)"
    echo "  $0 -v v0.0.3            # Download specific version v0.0.3"
    echo "  $0 --dry-run            # Show what would be downloaded without downloading"
    echo "  $0 --verbose            # Download with detailed logging"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                log "ERROR" "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                if [ -z "$VERSION" ]; then
                    VERSION="$1"
                else
                    log "ERROR" "Unexpected argument: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# 检查文件是否存在于 Nexus (无需认证)
check_file_exists() {
    local file_path="$1"
    local url="${NEXUS_URL}/repository/${REPOSITORY}/${BASE_PATH}/${file_path}"

    log "DEBUG" "Checking if file exists: $url"

    local response_code
    response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$url")

    if [ "$response_code" = "200" ]; then
        return 0
    else
        return 1
    fi
}

# 查找最新版本的数据文件
find_latest_data_file() {
    log "INFO" "Searching for latest data file..."

    # 首先尝试查找可能的新版本（v0.0.4, v0.0.5等）
    log "DEBUG" "Checking for potential newer versions..."

    # 尝试查找v0.0.4到v0.0.6的可能版本（减少检查数量）
    for minor in {4..6}; do
        # 尝试最可能的时间戳模式
        local recent_dates=("20250728" "20250729")
        local common_times=("191712" "180442")

        for date in "${recent_dates[@]}"; do
            for time in "${common_times[@]}"; do
                local candidate="data_v0.0.${minor}_${date}_${time}.tar.gz"
                log "DEBUG" "Checking potential version: $candidate"

                if check_file_exists "$candidate"; then
                    log "INFO" "🎉 Found newer version: $candidate"
                    echo "$candidate"
                    return 0
                fi
            done
        done
    done

    log "DEBUG" "No newer versions found, checking known versions..."

    # 按版本顺序检查已知版本
    for version in "${VERSION_ORDER[@]}"; do
        local file="${VERSION_MAP[$version]}"
        log "DEBUG" "Checking known version: $file"
        if check_file_exists "$file"; then
            log "INFO" "Found latest available version: $file"
            echo "$file"
            return 0
        fi
    done

    log "ERROR" "No data files found"
    return 1
}

# 根据版本号查找对应的数据文件
find_version_file() {
    local version="$1"

    # 检查版本是否存在于映射中
    if [[ -n "${VERSION_MAP[$version]}" ]]; then
        echo "${VERSION_MAP[$version]}"
        return 0
    else
        log "ERROR" "Unknown version: $version"
        log "ERROR" "Available versions: ${VERSION_ORDER[*]}"
        return 1
    fi
}

# 下载文件 (无需认证)
download_file() {
    local file_path="$1"
    local output_file="$2"
    local url="${NEXUS_URL}/repository/${REPOSITORY}/${BASE_PATH}/${file_path}"

    log "INFO" "Downloading: $file_path"
    log "DEBUG" "URL: $url"
    log "DEBUG" "Output: $output_file"

    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY-RUN] Would download: $url -> $output_file"
        return 0
    fi

    # 创建输出目录
    local output_dir
    output_dir=$(dirname "$output_file")
    mkdir -p "$output_dir"

    # 下载文件
    if curl -L -o "$output_file" "$url"; then
        log "INFO" "Downloaded successfully: $output_file"
        return 0
    else
        log "ERROR" "Failed to download: $url"
        return 1
    fi
}

# 解压文件
extract_file() {
    local archive_file="$1"
    local extract_dir="$2"

    log "INFO" "Extracting: $archive_file to $extract_dir"

    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY-RUN] Would extract: $archive_file -> $extract_dir"
        return 0
    fi

    # 创建目标目录
    mkdir -p "$extract_dir"

    # 解压并处理目录结构
    if tar -xzf "$archive_file" -C "$extract_dir"; then
        # 检查是否解压出了嵌套的 data 目录
        if [ -d "$extract_dir/data" ]; then
            log "INFO" "Detected nested data directory, flattening structure..."
            # 将嵌套的 data 目录内容移动到上层
            mv "$extract_dir/data"/* "$extract_dir/" 2>/dev/null || true
            # 删除空的嵌套 data 目录
            rmdir "$extract_dir/data" 2>/dev/null || true
            log "INFO" "Flattened directory structure successfully"
        fi
        log "INFO" "Extracted successfully"
        return 0
    else
        log "ERROR" "Failed to extract: $archive_file"
        return 1
    fi
}

# 清理临时文件
cleanup() {
    if [ -n "$TEMP_FILE" ] && [ -f "$TEMP_FILE" ]; then
        log "DEBUG" "Cleaning up temporary file: $TEMP_FILE"
        rm -f "$TEMP_FILE"
    fi
}

# 主函数
main() {
    # 设置清理trap
    trap cleanup EXIT

    # 解析参数
    parse_arguments "$@"

    local file_name

    # 如果指定了版本，使用指定版本
    if [ -n "$VERSION" ]; then
        log "INFO" "Version specified: $VERSION"

        file_name=$(find_version_file "$VERSION")
        if [ $? -ne 0 ] || [ -z "$file_name" ]; then
            log "ERROR" "Failed to find file for version $VERSION"
            exit 1
        fi

        log "INFO" "Found file for version $VERSION: $file_name"

        # 验证文件是否存在
        if ! check_file_exists "$file_name"; then
            log "ERROR" "File not found in Nexus: $file_name"
            exit 1
        fi
    else
        # 自动查找最新版本
        log "INFO" "No version specified, searching for latest version..."

        file_name=$(find_latest_data_file)
        if [ $? -ne 0 ] || [ -z "$file_name" ]; then
            log "ERROR" "Failed to find latest data file"
            exit 1
        fi

        log "INFO" "Auto-selected latest file: $file_name"
    fi

    # 检查输出目录
    local root_data_dir="$(pwd)/$OUTPUT_DIR"
    if [ -d "$root_data_dir" ] && [ "$(ls -A "$root_data_dir" 2>/dev/null)" ] && [ "$FORCE" != true ]; then
        log "ERROR" "Output directory '$root_data_dir' is not empty. Use -f/--force to overwrite"
        exit 1
    fi

    # 创建临时文件
    TEMP_FILE=$(mktemp)

    # 下载文件
    if ! download_file "$file_name" "$TEMP_FILE"; then
        log "ERROR" "Failed to download $file_name"
        exit 1
    fi

    # 清理并创建输出目录
    if [ "$DRY_RUN" != true ]; then
        rm -rf "$root_data_dir" 2>/dev/null || true
        mkdir -p "$root_data_dir"
    fi

    # 解压文件到根目录下的data目录
    if ! extract_file "$TEMP_FILE" "$root_data_dir"; then
        log "ERROR" "Failed to extract data"
        exit 1
    fi

    log "INFO" "✅ Successfully pulled and extracted test data to: $root_data_dir"

    # 显示提取的内容
    if [ "$VERBOSE" = true ] && [ "$DRY_RUN" != true ]; then
        log "INFO" "Extracted contents:"
        ls -la "$root_data_dir"
    fi
}

# 运行主函数
main "$@"
