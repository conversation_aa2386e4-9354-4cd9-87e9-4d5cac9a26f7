#!/bin/bash
###
 # @Description: Simple script to upload test data
 # @version: 1.0.0
 # @Author: shiqi.li
 # @Date: 2025-07-09 15:59:21
 # @LastEditTime: 2025-07-09 20:19:16
 # @LastEditors: shiqi.li
 # @FilePath: /hv_percep_base/ci/scripts/upload_data.sh
###

set -e

# 默认配置
NEXUS_URL="https://nexus3.hellobike.cn"
REPOSITORY="hv-generic-dev"
BASE_PATH="hv-percep/test_data"
USERNAME="hv-admin"

# 显示使用方法
show_usage() {
    echo "Usage: $0 <file_or_directory>"
    echo ""
    echo "Simple script to upload test data to Nexus"
    echo ""
    echo "Examples:"
    echo "  $0 test_data.tar.gz"
    echo "  $0 /path/to/test_folder"
    echo ""
    echo "Environment variables:"
    echo "  NEXUS_PASSWORD - Password for Nexus (required)"
    echo "  NEXUS_USERNAME - Username (default: admin)"
    echo "  -b, --base-path <path>   Base path in repository (default: $DEFAULT_BASE_PATH)"
    echo "  --nexus-url <url>        Nexus server URL (default: $DEFAULT_NEXUS_URL)"
    echo "  -f, --force              Overwrite existing files"
    echo "  -v, --verbose            Verbose output"
    echo "  -n, --dry-run            Show what would be uploaded without actual upload"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  NEXUS_URL               Nexus server URL"
    echo "  NEXUS_USERNAME          Nexus username"
    echo "  NEXUS_PASSWORD          Nexus password"
    echo "  NEXUS_REPOSITORY        Repository name"
    echo ""
    echo "Examples:"
    echo "  $0 test_data.tar.gz"
    echo "  $0 test_data.tar.gz custom/path/"
    echo "  $0 -f -v test_directory/"
    echo "  $0 --dry-run test_data/*"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -u|--username)
                USERNAME="$2"
                shift 2
                ;;
            -p|--password)
                PASSWORD="$2"
                shift 2
                ;;
            -r|--repository)
                REPOSITORY="$2"
                shift 2
                ;;
            -b|--base-path)
                BASE_PATH="$2"
                shift 2
                ;;
            --nexus-url)
                NEXUS_URL="$2"
                shift 2
                ;;
            -f|--force)
                OVERWRITE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                echo -e "${RED}Error: Unknown option $1${NC}"
                show_usage
                exit 1
                ;;
            *)
                if [ -z "$INPUT_PATH" ]; then
                    INPUT_PATH="$1"
                elif [ -z "$REMOTE_PATH" ]; then
                    REMOTE_PATH="$1"
                else
                    echo -e "${RED}Error: Too many arguments${NC}"
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# 验证配置
validate_config() {
    if [ -z "$INPUT_PATH" ]; then
        echo -e "${RED}Error: Input file or directory is required${NC}"
        show_usage
        exit 1
    fi

    if [ ! -e "$INPUT_PATH" ]; then
        echo -e "${RED}Error: Input path '$INPUT_PATH' does not exist${NC}"
        exit 1
    fi

    if [ -z "$PASSWORD" ]; then
        if [ -n "$NEXUS_PASSWORD" ]; then
            PASSWORD="$NEXUS_PASSWORD"
        else
            echo -e "${YELLOW}Warning: No password provided${NC}"
            read -s -p "Enter Nexus password: " PASSWORD
            echo
        fi
    fi

    if [ -z "$PASSWORD" ]; then
        echo -e "${RED}Error: Password is required${NC}"
        exit 1
    fi
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# 检查文件是否存在于远程仓库
check_remote_exists() {
    local remote_url="$1"

    log_verbose "Checking if remote file exists: $remote_url"

    local response_code=$(curl -s -o /dev/null -w "%{http_code}" \
        --user "$USERNAME:$PASSWORD" \
        --head "$remote_url")

    if [ "$response_code" = "200" ]; then
        return 0  # File exists
    else
        return 1  # File does not exist
    fi
}

# 上传单个文件
upload_file() {
    local local_file="$1"
    local remote_path="$2"

    # 构建完整的远程URL
    local remote_url="$NEXUS_URL/repository/$REPOSITORY/$remote_path"

    log_info "Uploading: $(basename "$local_file") -> $remote_path"

    # 检查远程文件是否存在
    if check_remote_exists "$remote_url"; then
        if [ "$OVERWRITE" = false ]; then
            log_warning "File already exists: $remote_path (use -f to overwrite)"
            return 1
        else
            log_warning "Overwriting existing file: $remote_path"
        fi
    fi

    # 执行上传
    if [ "$DRY_RUN" = true ]; then
        echo -e "${YELLOW}[DRY-RUN]${NC} Would upload: $local_file -> $remote_url"
        return 0
    fi

    log_verbose "Uploading to: $remote_url"

    local curl_options=()
    if [ "$VERBOSE" = true ]; then
        curl_options+=("-v")
    else
        curl_options+=("-s")
    fi

    curl_options+=(
        "--user" "$USERNAME:$PASSWORD"
        "--upload-file" "$local_file"
        "--fail"
        "--location"
    )

    if curl "${curl_options[@]}" "$remote_url"; then
        log_success "Successfully uploaded: $(basename "$local_file")"
        return 0
    else
        log_error "Failed to upload: $(basename "$local_file")"
        return 1
    fi
}

# 处理目录上传
upload_directory() {
    local local_dir="$1"
    local remote_base="$2"

    log_info "Uploading directory: $local_dir"

    local success_count=0
    local total_count=0

    # 查找所有文件（排除目录）
    while IFS= read -r -d '' file; do
        total_count=$((total_count + 1))

        # 计算相对路径
        local rel_path="${file#$local_dir/}"
        local remote_file_path="$remote_base/$rel_path"

        if upload_file "$file" "$remote_file_path"; then
            success_count=$((success_count + 1))
        fi

    done < <(find "$local_dir" -type f -print0)

    log_info "Directory upload completed: $success_count/$total_count files uploaded"

    if [ "$success_count" -eq "$total_count" ]; then
        return 0
    else
        return 1
    fi
}

# 主函数
main() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}Test Data Upload Script${NC}"
    echo -e "${BLUE}================================================${NC}"

    # 解析参数
    parse_arguments "$@"

    # 验证配置
    validate_config

    # 显示配置信息
    log_info "Configuration:"
    echo "  Nexus URL: $NEXUS_URL"
    echo "  Repository: $REPOSITORY"
    echo "  Base Path: $BASE_PATH"
    echo "  Username: $USERNAME"
    echo "  Input: $INPUT_PATH"
    if [ -n "$REMOTE_PATH" ]; then
        echo "  Remote Path: $REMOTE_PATH"
    fi
    echo "  Overwrite: $OVERWRITE"
    echo "  Dry Run: $DRY_RUN"

    # 确定远程路径
    if [ -z "$REMOTE_PATH" ]; then
        if [ -f "$INPUT_PATH" ]; then
            REMOTE_PATH="$BASE_PATH/$(basename "$INPUT_PATH")"
        elif [ -d "$INPUT_PATH" ]; then
            REMOTE_PATH="$BASE_PATH/$(basename "$INPUT_PATH")"
        fi
    else
        # 确保远程路径以base_path开头
        if [[ ! "$REMOTE_PATH" == "$BASE_PATH"* ]]; then
            REMOTE_PATH="$BASE_PATH/$REMOTE_PATH"
        fi
    fi

    echo "  Final Remote Path: $REMOTE_PATH"
    echo ""

    # 执行上传
    if [ -f "$INPUT_PATH" ]; then
        # 上传单个文件
        if upload_file "$INPUT_PATH" "$REMOTE_PATH"; then
            log_success "File upload completed successfully"
            exit 0
        else
            log_error "File upload failed"
            exit 1
        fi
    elif [ -d "$INPUT_PATH" ]; then
        # 上传目录
        if upload_directory "$INPUT_PATH" "$REMOTE_PATH"; then
            log_success "Directory upload completed successfully"
            exit 0
        else
            log_error "Directory upload failed"
            exit 1
        fi
    else
        log_error "Input path is neither a file nor a directory"
        exit 1
    fi
}

# 执行主函数
main "$@"
