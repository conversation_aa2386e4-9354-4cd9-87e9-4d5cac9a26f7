#!/bin/bash
# pack_directory.sh - Enhanced Directory Packer with Version Management
# Usage: ./pack_directory.sh <directory_path> [options]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示使用方法
show_usage() {
    echo "Usage: $0 <directory_path> [options]"
    echo ""
    echo "Parameters:"
    echo "  directory_path        Path to the directory to be packed"
    echo ""
    echo "Options:"
    echo "  -n, --name <name>     Custom name for output file (without extension)"
    echo "  -v, --version <ver>   Version mode: auto|major|minor|patch|X.Y.Z"
    echo "  -m, --message <msg>   Change message for this version"
    echo "  -s, --skip-version    Skip version file processing"
    echo "  --no-timestamp        Don't add timestamp to filename"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 /path/to/project"
    echo "  $0 /path/to/project -v auto -m \"Bug fixes\""
    echo "  $0 /path/to/project -n release --no-timestamp"
}

# 解析命令行参数
parse_arguments() {
    INPUT_DIR=""
    CUSTOM_NAME=""
    VERSION_MODE="auto"
    CHANGE_MESSAGE=""
    SKIP_VERSION=false
    ADD_TIMESTAMP=true

    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--name)
                CUSTOM_NAME="$2"
                shift 2
                ;;
            -v|--version)
                VERSION_MODE="$2"
                shift 2
                ;;
            -m|--message)
                CHANGE_MESSAGE="$2"
                shift 2
                ;;
            -s|--skip-version)
                SKIP_VERSION=true
                shift
                ;;
            --no-timestamp)
                ADD_TIMESTAMP=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                echo -e "${RED}Error: Unknown option $1${NC}"
                show_usage
                exit 1
                ;;
            *)
                if [ -z "$INPUT_DIR" ]; then
                    INPUT_DIR="$1"
                else
                    echo -e "${RED}Error: Multiple directory paths specified${NC}"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    if [ -z "$INPUT_DIR" ]; then
        echo -e "${RED}Error: Directory path is required${NC}"
        show_usage
        exit 1
    fi
}

# 读取当前版本
read_version() {
    local version_file="$1/VERSION"
    if [ -f "$version_file" ]; then
        cat "$version_file" | tr -d '\n\r' | tr -d ' '
    else
        echo "0.0.0"
    fi
}

# 增加版本号
increment_version() {
    local version="$1"
    local mode="$2"

    if [[ $version =~ ^([0-9]+)\.([0-9]+)\.([0-9]+)$ ]]; then
        local major="${BASH_REMATCH[1]}"
        local minor="${BASH_REMATCH[2]}"
        local patch="${BASH_REMATCH[3]}"
    else
        echo "0.0.1"
        return
    fi

    case $mode in
        "major")
            echo "$((major + 1)).0.0"
            ;;
        "minor")
            echo "${major}.$((minor + 1)).0"
            ;;
        "patch"|"auto")
            echo "${major}.${minor}.$((patch + 1))"
            ;;
        *)
            if [[ $mode =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                echo "$mode"
            else
                echo -e "${RED}Error: Invalid version format '$mode'${NC}"
                exit 1
            fi
            ;;
    esac
}

# 更新VERSION文件
update_version_file() {
    local dir="$1"
    local new_version="$2"
    local version_file="$dir/VERSION"

    echo "$new_version" > "$version_file"

    if [ -f "$version_file" ]; then
        local written_version=$(cat "$version_file" | tr -d '\n\r' | tr -d ' ')
        if [ "$written_version" = "$new_version" ]; then
            echo -e "${GREEN}✓ Updated VERSION file to $new_version${NC}"
            return 0
        else
            echo -e "${RED}✗ Failed to update VERSION file${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ Failed to create VERSION file${NC}"
        return 1
    fi
}

# 更新CHANGELOG文件
update_changelog() {
    local dir="$1"
    local version="$2"
    local message="$3"
    local changelog_file="$dir/CHANGELOG.md"
    local date=$(date +%Y-%m-%d)

    if [ ! -f "$changelog_file" ]; then
        cat > "$changelog_file" << EOF
# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

EOF
    fi

    local new_entry="## [$version] - $date"
    if [ -n "$message" ]; then
        new_entry="$new_entry"$'\n'"- $message"
    else
        new_entry="$new_entry"$'\n'"- Version update"
    fi
    new_entry="$new_entry"$'\n'

    local temp_file=$(mktemp)

    if grep -q "^## \[" "$changelog_file"; then
        awk -v new_entry="$new_entry" '
        BEGIN { inserted = 0 }
        /^## \[/ && !inserted {
            print new_entry
            inserted = 1
        }
        { print }
        ' "$changelog_file" > "$temp_file"
    else
        cp "$changelog_file" "$temp_file"
        echo "" >> "$temp_file"
        echo "$new_entry" >> "$temp_file"
    fi

    if mv "$temp_file" "$changelog_file"; then
        echo -e "${GREEN}✓ Updated CHANGELOG.md${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to update CHANGELOG.md${NC}"
        rm -f "$temp_file"
        return 1
    fi
}

# 显示git状态
show_git_status() {
    local dir="$1"
    cd "$dir"

    if git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${BLUE}Git repository detected:${NC}"
        echo "Current branch: $(git branch --show-current 2>/dev/null || echo "unknown")"
        echo "Last commit: $(git log -1 --oneline 2>/dev/null || echo "none")"

        if [ -n "$(git status --porcelain 2>/dev/null)" ]; then
            echo -e "${YELLOW}Warning: There are uncommitted changes${NC}"
            git status --short

            read -p "Continue with uncommitted changes? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                echo "Operation cancelled"
                exit 1
            fi
        fi
    fi
}

# 主函数
main() {
    parse_arguments "$@"

    # 检查目录是否存在
    if [ ! -d "$INPUT_DIR" ]; then
        echo -e "${RED}Error: Directory '$INPUT_DIR' does not exist${NC}"
        exit 1
    fi

    # 获取绝对路径
    INPUT_DIR=$(realpath "$INPUT_DIR")
    DIR_NAME=$(basename "$INPUT_DIR")
    PARENT_DIR=$(dirname "$INPUT_DIR")

    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}Enhanced Directory Packer Script${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo "Input directory: $INPUT_DIR"
    echo "Parent directory: $PARENT_DIR"

    # 显示git状态
    show_git_status "$INPUT_DIR"

    # 处理版本文件
    VERSION_SUFFIX=""
    if [ "$SKIP_VERSION" = false ]; then
        echo -e "\n${BLUE}Version Management:${NC}"

        current_version=$(read_version "$INPUT_DIR")
        echo "Current version: $current_version"

        if [ -z "$CHANGE_MESSAGE" ]; then
            read -p "Enter change message (optional): " CHANGE_MESSAGE
        fi

        new_version=$(increment_version "$current_version" "$VERSION_MODE")
        echo "New version: $new_version"

        if [ "$current_version" != "$new_version" ]; then
            read -p "Update version from $current_version to $new_version? (Y/n): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Nn]$ ]]; then
                if update_version_file "$INPUT_DIR" "$new_version" && \
                   update_changelog "$INPUT_DIR" "$new_version" "$CHANGE_MESSAGE"; then
                    echo -e "${GREEN}✓ Version files updated successfully${NC}"
                else
                    echo -e "${RED}✗ Failed to update version files${NC}"
                    exit 1
                fi
            else
                new_version="$current_version"
            fi
        fi

        VERSION_SUFFIX="v${new_version}"
    fi

    # 确定输出文件名
    if [ -n "$CUSTOM_NAME" ]; then
        OUTPUT_NAME="$CUSTOM_NAME"
    else
        OUTPUT_NAME="$DIR_NAME"
    fi

    if [ -n "$VERSION_SUFFIX" ]; then
        OUTPUT_NAME="${OUTPUT_NAME}_${VERSION_SUFFIX}"
    fi

    if [ "$ADD_TIMESTAMP" = true ]; then
        TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        OUTPUT_NAME="${OUTPUT_NAME}_${TIMESTAMP}"
    fi

    OUTPUT_FILE="$PARENT_DIR/${OUTPUT_NAME}.tar.gz"

    echo -e "\n${BLUE}Packaging Information:${NC}"
    echo "Output file: $OUTPUT_FILE"

    # 检查输出文件是否已存在
    if [ -f "$OUTPUT_FILE" ]; then
        echo -e "${YELLOW}Warning: Output file already exists${NC}"
        read -p "Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Operation cancelled"
            exit 1
        fi
    fi

    # 开始打包
    echo -e "\n${BLUE}Starting compression...${NC}"

    if command -v find >/dev/null 2>&1; then
        file_count=$(find "$INPUT_DIR" -type f | wc -l)
        echo "Files to compress: $file_count"
    fi

    # 使用tar命令打包
    if tar --version 2>/dev/null | grep -q "GNU tar"; then
        tar -czf "$OUTPUT_FILE" -C "$PARENT_DIR" \
            --checkpoint=1000 \
            --checkpoint-action=echo='Processed %{read}T files...' \
            "$DIR_NAME"
    else
        tar -czf "$OUTPUT_FILE" -C "$PARENT_DIR" "$DIR_NAME"
    fi

    # 检查打包结果
    if [ $? -eq 0 ]; then
        echo -e "\n${GREEN}================================================${NC}"
        echo -e "${GREEN}✓ Compression completed successfully!${NC}"
        echo -e "${GREEN}================================================${NC}"

        # 显示文件信息
        if command -v du >/dev/null 2>&1; then
            ORIGINAL_SIZE=$(du -sh "$INPUT_DIR" | cut -f1)
            COMPRESSED_SIZE=$(du -sh "$OUTPUT_FILE" | cut -f1)
            echo "Original size:   $ORIGINAL_SIZE"
            echo "Compressed size: $COMPRESSED_SIZE"
        fi

        echo "Output file: $OUTPUT_FILE"

        # 验证压缩文件完整性
        echo -e "\n${BLUE}Verifying archive integrity...${NC}"
        if tar -tzf "$OUTPUT_FILE" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ Archive integrity verified${NC}"
        else
            echo -e "${RED}✗ Warning: Archive may be corrupted${NC}"
            exit 1
        fi

        # 显示压缩文件内容摘要
        echo -e "\n${BLUE}Archive contents preview:${NC}"
        tar -tzf "$OUTPUT_FILE" | head -10
        total_files=$(tar -tzf "$OUTPUT_FILE" | wc -l)
        if [ "$total_files" -gt 10 ]; then
            echo "... and $((total_files - 10)) more files"
        fi

    else
        echo -e "${RED}✗ Error: Compression failed${NC}"
        exit 1
    fi

    echo -e "\n${GREEN}================================================${NC}"
    echo -e "${GREEN}Done!${NC}"
    echo -e "${GREEN}================================================${NC}"
}

# 执行主函数
main "$@"
