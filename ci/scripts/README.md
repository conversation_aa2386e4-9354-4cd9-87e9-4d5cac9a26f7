# CI脚本工具集

## 概述

本目录包含用于CI/CD流程的实用脚本工具：

- `pull_data.sh` - 智能测试数据拉取脚本
- `pull_generic.sh` - 通用制品下载和解压脚本

## pull_generic.sh - 通用制品下载脚本

`pull_generic.sh` 是一个功能强大的脚本，用于从Nexus仓库下载和自动解压制品文件。

### 功能特性

- ✅ **自动解压缩**：支持多种压缩格式的自动解压
- ✅ **自定义目录名**：可指定解压后的目录名
- ✅ **灵活选项**：支持只下载、保留压缩包等选项
- ✅ **进度显示**：实时显示下载进度
- ✅ **无需认证**：直接从公开的Nexus仓库下载

### 支持的压缩格式

- `.tar.gz`, `.tgz`
- `.tar.bz2`
- `.tar.xz`
- `.zip`

### 使用方法

```bash
# 基本用法：下载并解压到默认目录
./pull_generic.sh https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/package.tar.gz

# 指定解压目录名
./pull_generic.sh https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/package.tar.gz my_folder

# 只下载不解压
./pull_generic.sh https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/package.tar.gz --no-extract

# 解压后保留压缩包
./pull_generic.sh https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/package.tar.gz my_folder --keep-archive
```

### 命令行选项

- `--no-extract`: 只下载不解压缩
- `--keep-archive`: 解压后保留压缩包
- `-h, --help`: 显示帮助信息

---

## pull_data.sh - 测试数据拉取脚本

`pull_data.sh` 是一个智能脚本，用于从Nexus仓库自动拉取和解压测试数据。脚本支持自动发现最新版本，也可以指定特定版本下载。

## 功能特性

- ✅ **自动发现最新版本**：默认下载最新可用的测试数据
- ✅ **版本指定**：支持下载特定版本的数据
- ✅ **智能检查**：自动检查可能的新版本
- ✅ **干运行模式**：支持预览下载内容而不实际下载
- ✅ **详细日志**：提供详细的调试信息
- ✅ **无需认证**：直接从公开的Nexus仓库下载

## 使用方法

### 基本用法

```bash
# 自动下载最新版本（推荐）
./pull_data.sh

# 下载特定版本
./pull_data.sh -v v0.0.3

# 预览下载内容（不实际下载）
./pull_data.sh --dry-run

# 详细日志模式
./pull_data.sh --verbose

# 强制覆盖现有数据
./pull_data.sh --force
```

### 命令行选项

- `-v, --version <version>`: 指定要下载的版本（如：v0.0.1, v0.0.2, v0.0.3）
- `-f, --force`: 强制覆盖现有的数据目录
- `--verbose`: 显示详细的调试信息
- `-n, --dry-run`: 预览模式，显示将要下载的内容但不实际下载
- `-h, --help`: 显示帮助信息

## 当前可用版本

- **v0.0.3** - `data_v0.0.3_20250721_191712.tar.gz` (最新)
- **v0.0.2** - `data_v0.0.2_20250721_180442.tar.gz`
- **v0.0.1** - `data_v0.0.1_20250709_164553.tar.gz`

## 输出目录

数据将被解压到当前目录下的 `data/` 文件夹中。

## 添加新版本

当有新的测试数据版本时，请按以下步骤更新脚本：

1. 编辑 `pull_data.sh` 文件
2. 在 `VERSION_MAP` 中添加新版本映射：
   ```bash
   ["v0.0.4"]="data_v0.0.4_YYYYMMDD_HHMMSS.tar.gz"
   ```
3. 在 `VERSION_ORDER` 数组开头添加新版本：
   ```bash
   VERSION_ORDER=("v0.0.4" "v0.0.3" "v0.0.2" "v0.0.1")
   ```
4. 脚本会自动使用新版本作为默认的最新版本

## 智能版本发现

脚本还会自动尝试发现可能的新版本（v0.0.4, v0.0.5等），通过检查常见的时间戳模式来查找未在配置中列出的新版本。

## 故障排除

### 网络连接问题
如果遇到网络连接问题，脚本会自动超时（10秒）并继续检查其他版本。

### 文件不存在
如果指定的版本不存在，脚本会显示可用版本列表。

### 权限问题
确保脚本有执行权限：
```bash
chmod +x pull_data.sh
```

## 示例输出

```bash
$ ./pull_data.sh
[INFO] No version specified, searching for latest version...
[INFO] Found latest available version: data_v0.0.3_20250721_191712.tar.gz
[INFO] Auto-selected latest file: data_v0.0.3_20250721_191712.tar.gz
[INFO] Downloading: data_v0.0.3_20250721_191712.tar.gz
[INFO] Extracting: /tmp/tmp.xxx -> ./data
[INFO] ✅ Successfully pulled and extracted test data to: ./data
```
