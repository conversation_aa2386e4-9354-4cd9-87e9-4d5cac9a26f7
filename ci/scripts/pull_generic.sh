#! /bin/bash
###
 # @Author: <EMAIL> <EMAIL>
 # @Date: 2025-07-28 15:25:50
 # @LastEditors: <EMAIL> <EMAIL>
 # @LastEditTime: 2025-07-28 15:31:12
 # @FilePath: /hv_percep_workspace/ci/scripts/pull_generic.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 显示帮助信息
show_help() {
    echo "Usage: $0 <target_url> [extract_dir] [options]"
    echo ""
    echo "Generic script to download and extract artifacts from public Nexus repository"
    echo "(no authentication required)"
    echo ""
    echo "Parameters:"
    echo "  target_url:  要下载的文件在仓库中的完整URL"
    echo "  extract_dir: (可选) 解压缩后的目录名，默认为文件名去掉扩展名"
    echo ""
    echo "Options:"
    echo "  --no-extract  只下载不解压缩"
    echo "  --keep-archive 解压后保留压缩包"
    echo "  -h, --help    显示此帮助信息"
    echo ""
    echo "支持的压缩格式:"
    echo "  .tar.gz, .tgz, .tar.bz2, .tar.xz, .zip"
    echo ""
    echo "Examples:"
    echo "  $0 https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/dummy.tar.gz"
    echo "  $0 https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/dummy.tar.gz my_folder"
    echo "  $0 https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/dummy.tar.gz --no-extract"
    echo "  $0 https://nexus3.hellobike.cn/repository/hv-generic-dev/hv-base/dummy.tar.gz my_folder --keep-archive"
    echo ""
    echo "Note: This script downloads from public repositories and does not require authentication."
    exit 1
}

# 初始化变量
EXTRACT=true
KEEP_ARCHIVE=false
REPO_URL=""
EXTRACT_DIR=""

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            ;;
        --no-extract)
            EXTRACT=false
            shift
            ;;
        --keep-archive)
            KEEP_ARCHIVE=true
            shift
            ;;
        -*)
            echo "Unknown option: $1"
            show_help
            ;;
        *)
            if [ -z "$REPO_URL" ]; then
                REPO_URL="$1"
            elif [ -z "$EXTRACT_DIR" ]; then
                EXTRACT_DIR="$1"
            else
                echo "Too many arguments"
                show_help
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [ -z "$REPO_URL" ]; then
    echo "Error: target_url is required"
    show_help
fi

# 注意：此脚本不需要认证信息，直接从公开仓库下载

# 获取文件名和扩展名
FILENAME=$(basename "$REPO_URL")
TEMP_FILE="/tmp/${FILENAME}"

# 设置解压目录
if [ -z "$EXTRACT_DIR" ]; then
    # 默认解压目录：去掉扩展名
    EXTRACT_DIR=$(echo "$FILENAME" | sed -E 's/\.(tar\.gz|tgz|tar\.bz2|tar\.xz|zip)$//')
fi

# 检测压缩格式的函数
detect_archive_type() {
    local file="$1"
    case "$file" in
        *.tar.gz|*.tgz)
            echo "tar.gz"
            ;;
        *.tar.bz2)
            echo "tar.bz2"
            ;;
        *.tar.xz)
            echo "tar.xz"
            ;;
        *.zip)
            echo "zip"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 解压缩函数
extract_archive() {
    local archive_file="$1"
    local target_dir="$2"
    local archive_type=$(detect_archive_type "$archive_file")

    echo "🗜️  Extracting archive..."
    echo "📦 Archive: $archive_file"
    echo "📁 Target directory: $target_dir"
    echo "🔍 Archive type: $archive_type"

    # 创建目标目录
    mkdir -p "$target_dir"

    case "$archive_type" in
        "tar.gz")
            tar -xzf "$archive_file" -C "$target_dir" --strip-components=1
            ;;
        "tar.bz2")
            tar -xjf "$archive_file" -C "$target_dir" --strip-components=1
            ;;
        "tar.xz")
            tar -xJf "$archive_file" -C "$target_dir" --strip-components=1
            ;;
        "zip")
            unzip -q "$archive_file" -d "$target_dir"
            ;;
        "unknown")
            echo "⚠️  Unknown archive format, skipping extraction"
            return 1
            ;;
    esac

    if [ $? -eq 0 ]; then
        echo "✅ Successfully extracted to: $target_dir"
        return 0
    else
        echo "❌ Failed to extract archive"
        return 1
    fi
}

# 主要执行逻辑
echo "📥 Downloading from: $REPO_URL"
echo "📁 Saving to: $TEMP_FILE"

# 下载文件（无需认证，显示进度）
curl --progress-bar \
     --fail \
     --location \
     --create-dirs \
     -o "$TEMP_FILE" \
     "$REPO_URL"

# 检查下载是否成功
if [ $? -eq 0 ]; then
    echo "✅ Successfully downloaded to: $TEMP_FILE"

    # 显示文件信息
    if [ -f "$TEMP_FILE" ]; then
        FILE_SIZE=$(du -h "$TEMP_FILE" | cut -f1)
        echo "📊 File size: $FILE_SIZE"
    fi

    # 解压缩处理
    if [ "$EXTRACT" = true ]; then
        if extract_archive "$TEMP_FILE" "$EXTRACT_DIR"; then
            echo "📂 Contents extracted to: $(pwd)/$EXTRACT_DIR"

            # 显示解压后的内容
            if [ -d "$EXTRACT_DIR" ]; then
                echo "📋 Extracted contents:"
                ls -la "$EXTRACT_DIR" | head -10
            fi

            # 是否保留压缩包
            if [ "$KEEP_ARCHIVE" = false ]; then
                rm -f "$TEMP_FILE"
                echo "🗑️  Removed temporary archive file"
            else
                # 移动压缩包到当前目录
                mv "$TEMP_FILE" "./$FILENAME"
                echo "📦 Archive saved as: ./$FILENAME"
            fi
        else
            echo "⚠️  Extraction failed, keeping archive file"
            mv "$TEMP_FILE" "./$FILENAME"
        fi
    else
        # 不解压，直接移动到当前目录
        mv "$TEMP_FILE" "./$FILENAME"
        echo "📦 Archive saved as: ./$FILENAME"
    fi
else
    echo "❌ Failed to download from: $REPO_URL"
    echo "💡 Please check if the URL is correct and accessible"
    exit 1
fi
