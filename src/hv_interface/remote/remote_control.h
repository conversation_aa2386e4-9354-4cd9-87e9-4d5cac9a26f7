/**
 * @file remote_control.h
 * @brief 远程车身控制相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的远程车身控制功能定义
 */

#pragma once

#include "../common/header.h"
#include "../vehicle_io/mcu_service.h"
#include <cstdint>
#include <string>
#include <vector>

namespace hv {
namespace interface {

struct RemoteHeader {
    double global_timestamp;
    double local_timestamp;
    uint32_t frame_sequence;   // 帧序列号
};

// 远程控制命令类型
enum class RemoteCommandType : uint8_t {
    CHASSIS_CONTROL = 0, // 底盘控制
    EMERGENCY_STOP = 1,  // 紧急停止
};

struct RemoteChassisControl {
    Header header;                  // 数据头
    RemoteHeader meta_header;
    RemoteCommandType command_type; // 命令类型
    uint8_t gear_position;      // 档位控制
    uint8_t brake_position;     // 制动控制
    uint8_t throttle_position;  // 油门控制
    double target_steering_angle; // 目标转向角
};

// 灯光控制
struct RemoteLightingControl {
    Header header;                  // 数据头
    RemoteHeader meta_header;
    uint8_t front_light;           // 前车灯控制
    uint8_t hazard_light;          // 危险警告灯控制
    uint8_t width_light;           // 示宽灯控制
    uint8_t fog_light;             // 雾灯控制
    uint8_t turn_light;            // 转向灯控制
    uint8_t dome_light;            // 车内顶灯控制
};

// 车门控制
struct RemoteDoorControl {
    Header header;                  // 数据头
    RemoteHeader meta_header;
    uint8_t fl_door_open;          // 主驾车门控制
    uint8_t fr_door_open;          // 副驾车门控制
    uint8_t rl_door_open;          // 左后车门控制
    uint8_t rr_door_open;          // 右后车门控制
    uint8_t hood_open;             // 前舱盖控制
    uint8_t trunk_open;            // 后备箱控制
    uint8_t charging_door_open;    // 充电口控制
};

// 车窗控制
struct RemoteWindowControl {
    Header header;                  // 数据头
    RemoteHeader meta_header;
    uint8_t fl_window_position;    // 主驾车窗控制
    uint8_t fr_window_position;    // 副驾车窗控制
    uint8_t rl_window_position;    // 左后车窗控制
    uint8_t rr_window_position;    // 右后车窗控制
    uint8_t sun_roof_position;     // 天窗控制
};

// 座椅控制
struct RemoteSeatControl {
    Header header;                  // 数据头
    RemoteHeader meta_header;
    uint8_t fl_seat_position;      // 主驾座椅位置控制
    uint8_t fr_seat_position;      // 副驾座椅位置控制
    uint8_t rl_seat_position;      // 左后座椅位置控制
    uint8_t rr_seat_position;      // 右后座椅位置控制
    uint8_t rm_seat_position;      // 中后座椅位置控制
    uint8_t fl_seatback_position;  // 主驾座椅靠背控制
    uint8_t fr_seatback_position;  // 副驾座椅靠背控制
    uint8_t rl_seatback_position;  // 左后座椅靠背控制
    uint8_t rr_seatback_position;  // 右后座椅靠背控制
    uint8_t rm_seatback_position;  // 中后座椅靠背控制
};

// 座椅舒适性控制
struct RemoteComfortControl {
    Header header;                  // 数据头
    RemoteHeader meta_header;
    uint8_t fl_seat_heating;       // 主驾座椅加热控制
    uint8_t fr_seat_heating;       // 副驾座椅加热控制
    uint8_t rl_seat_heating;       // 左后座椅加热控制
    uint8_t rr_seat_heating;       // 右后座椅加热控制
    uint8_t rm_seat_heating;       // 中后座椅加热控制
    uint8_t fl_seat_cooling;       // 主驾座椅通风控制
    uint8_t fr_seat_cooling;       // 副驾座椅通风控制
    uint8_t rl_seat_cooling;       // 左后座椅通风控制
    uint8_t rr_seat_cooling;       // 右后座椅通风控制
    uint8_t rm_seat_cooling;       // 中后座椅通风控制
};

// 空调控制
struct RemoteAirConditioningControl {
    Header header;                  // 数据头
    RemoteHeader meta_header;
    uint8_t ac_function_status;    // 空调开关控制
    uint8_t ac_mode;               // 空调模式控制
    uint8_t ac_temp;               // 空调温度控制
    uint8_t ac_fanspeed;           // 空调风速控制
    uint8_t ac_air_circulation_mode; // 空气循环模式控制
};
} // namespace interface
} // namespace hv

