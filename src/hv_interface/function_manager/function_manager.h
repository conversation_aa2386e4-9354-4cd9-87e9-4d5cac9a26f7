/**
 * @file function_manager.h
 * @brief 功能管理器头文件定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库通用数据类型的相关功能定义
 */

#pragma once

#include <cstdint> // For int8_t, uint8_t
#include "../common/header.h"

namespace hv {
namespace interface {

enum class FunctionStatus : int8_t {
    INIT = 0,
    INHIBIT = 1,
    STANDBY = 2,
    ACTIVE = 3,
    LONGI_OVERRIDE = 4,
    LAT_OVERRIDE = 5,
    FAULT = 6,
};

enum class TourStatus : int8_t {
    UNKNOWN = 0,
    OPERABLE = 1,
    CRUISE_WAIT = 2,
    CRUISE_RUN = 3,
    OPERATING = 4,
    OUT_OF_SERVICE = 5,
};

enum class OperaterType : int8_t {
    UNKNOWN = 0,
    MANUAL = 1,
    AUTO = 2,
    REMOTE =3,
};

enum class TakeOverRequest : int8_t {
    NO_REQUEST = 0,
    REQUEST_L1 = 1,
    REQUEST_L2 = 2,
    REQUEST_L3 = 3,
};

enum class MrmStatus : int8_t {
    NO_MRM = 0,
    MRM_REMOTE_STOP_IN_LANE = 1,
    MRM_REMOTE_PULL_OVER = 2,
    MRM_STOP_IN_LANE = 3,
    MRM_PULL_OVER = 4,
};

struct SettingConfig{
    uint8_t setting_speed; // 30-120, km/h
    uint8_t setting_headway; // 1-3, 1: 1s, 2: 1.5s, 3: 2s
};
struct FunctionManager {
    Header header;
    FunctionStatus function_status;
    TourStatus tour_status;
    TakeOverRequest take_over_request;
    OperaterType operater_type;
    SettingConfig setting_config;
};

} // namespace interface
} // namespace hv
