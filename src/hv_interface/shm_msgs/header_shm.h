/**
 * @file header_shm.h
 * @brief 共享内存消息头定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的激光雷达点云相关功能定义
 */

#pragma once

#include <cstdint>
#include <vector>
#include <array>

namespace hv {
namespace interface {

struct HeaderShm {
  double global_timestamp; // 自Unix纪元的秒数,单位为秒
  double local_timestamp;  // 本地算法时间戳，单位为秒
  uint32_t frame_sequence;   // 帧序列号
  char frame_id[256];      // 帧ID
};

}    // namespace interface
}    // namespace hv
