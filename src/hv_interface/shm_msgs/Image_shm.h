/**
 * @file Image_shm.h
 * @brief 图像数据定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的激光雷达点云相关功能定义
 */

#pragma once

#include <cstdint>
#include <vector>
#include <array>
#include "header_shm.h"

namespace hv {
namespace interface {

enum class ImageEncodingType : uint8_t {
  UNKNOWN = 0,           // 未知帧类型
  YUV420P = 1,          // YUV420P帧
  YUV420SP = 2,         // YUV420SP帧
  YUV422P = 3,         // YUV422P帧
  YUV422SP = 4,        // YUV422SP帧
  YUV444P = 5,         // YUV444P帧
  YUYV = 6,            // YUYV帧

  CUSTOM = 255           // 自定义帧类型
};

struct ImageShm8m {
  HeaderShm header; // Header timestamp should be acquisition time of image
                             // Header frame_id should be optical frame of camera
                             // origin of frame should be optical center of cameara
                             // +x should point to the right in the image
                             // +y should point down in the image
                             // +z should point into to plane of the image
                             // If the frame_id here and the frame_id of the CameraInfo
                             // message associated with the image conflict
                             // the behavior is undefined

  uint32_t height; // image height, that is, number of rows
  uint32_t width; // image width, that is, number of columns

// The legal values for encoding are in file src/image_encodings.cpp
// If you want to standardize a new char[256] format, join
// <EMAIL> and send an email proposing a new encoding.

    ImageEncodingType encoding; // Encoding of pixels -- channel meaning, ordering, size
                      // taken from the list of char[256]s in include/sensor_msgs/image_encodings.hpp

  uint8_t is_bigendian; // is this data bigendian?
  uint32_t step; // Full row length in bytes
  uint8_t data[8388608]; // actual matrix data, size is (step * rows)
};

}    // namespace interface
}    // namespace hv
