/**
 * @file lidar_pointcloud.h
 * @brief 激光雷达点云数据定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的激光雷达点云相关功能定义
 */

#pragma once

#include <cstdint>
#include <vector>
#include <array>
#include "header_shm.h"

namespace hv {
namespace interface {

struct PointxyzitShm {
  double x;               // X坐标 (米)
  double y;               // Y坐标 (米)
  double z;               // Z坐标 (米)
  double intensity;       // 反射强度
  double time_offset;     // 时间偏移 (ms)
};

enum class PointFieldDataTypeShm : uint8_t {
  INT8 = 1,
  UINT8 = 2,
  INT16 = 3,
  UINT16 = 4,
  INT32 = 5,
  UINT32 = 6,
  FLOAT32 = 7,
  FLOAT64 = 8,
};

struct PointFieldShm {
  // Common PointField names are x, y, z, intensity, rgb, rgba
  char name[256];      // Name of field
  uint32_t offset;    // Offset from start of point struct
  uint8_t  datatype;  // Datatype enumeration, see above
  uint32_t count;     // How many elements in the fiel
};

// 点云数据结构
struct PointCloud1m {
  HeaderShm header;   // 点云头信息
  uint32_t height; // 2D structure of the point cloud. If the cloud is unordered, height is
  uint32_t width; // 1 and width is the length of the point cloud.
  PointFieldShm fields[8]; // 8个点云字段
  uint8_t fields_size; // 点云字段数量
  bool is_bigendian; // Is this data bigendian?
  uint32_t point_step; // Length of a point in bytes
  uint32_t row_step; // Length of a row in bytes
  double last_frame_timestamp; // 基准点云时间戳，单位:秒
  PointxyzitShm points[1048576];      // XYZ格式点云
  bool is_dense; // True if there are no invalid points
};

}    // namespace interface
}    // namespace hv
