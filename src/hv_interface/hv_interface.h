/**
 * @file hv_interface.h
 * @brief HV Interface - 统一的头文件包含
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的所有公共头文件
 * 用户只需要包含此文件即可使用所有HV Interface功能
 */

#pragma once

// ============================================================================
// 通用组件 (Common)
// ============================================================================
#include "common/header.h"
#include "common/geometry/geometry.h"
#include "common/geometry/matrix.h"
#include "common/geometry/transform.h"

// ============================================================================
// 传感器相关 (Sensor)
// ============================================================================
#include "sensor/sensor_common.h"
#include "sensor/gnss_types.h"
#include "sensor/gnss_rtk.h"
#include "sensor/gnss_imu.h"
#include "sensor/gnss_heading.h"
#include "sensor/lidar_pointcloud.h"
#include "sensor/lidar_recoder.h"
#include "sensor/camera_recoder.h"

// ============================================================================
// 感知相关 (Perception)
// ============================================================================
#include "perception/perception_common.h"
#include "perception/perception_obstacle.h"
#include "perception/fusion_obstacle.h"
#include "perception/lidar_oppcupancy.h"
#include "perception/camera_scene.h"

// ============================================================================
// 定位相关 (Localization)
// ============================================================================
#include "localization/landmark.h"
#include "localization/localization.h"

// ============================================================================
// 地图相关 (Map)
// ============================================================================
#include "map/map.h"

// ============================================================================
// 规划相关 (Planning)
// ============================================================================
#include "planning/trajectory.h"

// ============================================================================
// 预测相关 (Prediction)
// ============================================================================
#include "prediction/prediction.h"

// ============================================================================
// 路由相关 (Routing)
// ============================================================================
#include "routing/routing.h"

// ============================================================================
// 控制相关 (Control)
// ============================================================================
#include "control/control.h"

// ============================================================================
// 车辆IO相关 (Vehicle IO)
// ============================================================================
#include "vehicle_io/vehicle_io.h"
#include "vehicle_io/mcu_service.h"
#include "vehicle_io/raw_can.h"
#include "vehicle_io/vehicle_io_diagnosis_bitmask.h"

// ============================================================================
// 远程控制相关 (Remote Control)
// ============================================================================
#include "remote/remote_control.h"

// ============================================================================
// 记录器相关 (Recoder)
// ============================================================================
#include "recoder/recoder.h"

// ============================================================================
// 功能管理器相关 (Function Manager)
// ============================================================================
#include "function_manager/function_manager.h"

// ============================================================================
// 命名空间别名（可选）
// ============================================================================
// 如果需要，可以在这里定义命名空间别名
// namespace hv = interface;

