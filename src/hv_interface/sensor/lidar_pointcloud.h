/**
 * @file lidar_pointcloud.h
 * @brief 激光雷达点云数据定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的激光雷达点云相关功能定义
 */

#pragma once

#include <cstdint>
#include <vector>
#include <array>
#include "../common/header.h"
#include "sensor_common.h"

namespace hv {
namespace interface {

struct Pointxyzit {
  double x;               // X坐标 (米)
  double y;               // Y坐标 (米)
  double z;               // Z坐标 (米)
  double intensity;       // 反射强度
  double time_offset;     // 时间偏移 (ms)
};

// 点云数据结构
struct PointCloud {
  Header header;   // 点云头信息
  SensorHeader meta_header; // 点云元数据头信息
  double last_frame_timestamp; // 基准点云时间戳，单位:秒
  std::vector<Pointxyzit> points;      // XYZ格式点云
};

}    // namespace interface
}    // namespace hv
