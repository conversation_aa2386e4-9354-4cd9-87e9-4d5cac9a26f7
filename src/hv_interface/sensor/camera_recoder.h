/**
 * @file recoder_camera.h
 * @brief 记录仪相机相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的记录仪相机功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <array>
#include "../common/header.h"
#include "sensor_common.h"

namespace hv {
namespace interface {

// 编码方式枚举
enum class EncodingType : uint8_t {
  UNKNOWN = 0,           // 未知编码
  H264 = 1,              // H.264编码
  H265 = 2,              // H.265编码
  JPEG = 3,             // JPEG编码
  CUSTOM = 255           // 自定义编码
};

enum class CameraFrameType : uint8_t {
  UNKNOWN = 0,           // 未知帧类型
  YUV420P = 1,          // YUV420P帧
  YUV420SP = 2,         // YUV420SP帧
  YUV422P = 3,         // YUV422P帧
  YUV422SP = 4,        // YUV422SP帧
  YUV444P = 5,         // YUV444P帧
  YUYV = 6,            // YUYV帧

  CUSTOM = 255           // 自定义帧类型
};

// 记录仪相机帧结构
struct RecoderCameraFrame {
  Header header;                             // 数据头
  SensorHeader meta_header;              // 元数据头
  EncodingType encoding_type;                // 编码方式
  double exposure_start_time;              // 曝光开始时间，单位:秒
  double exposure_end_time;                // 曝光结束时间，单位:秒
  uint32_t height;                         // 行数
  uint32_t width;                          // 列数
  uint32_t frame_payload_size;             // 帧载菏字节数
  std::vector<uint8_t> frame_payload;        // 帧载荷数据
};

}    // namespace interface
}    // namespace hv
