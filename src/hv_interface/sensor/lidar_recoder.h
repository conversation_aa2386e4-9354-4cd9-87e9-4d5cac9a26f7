/**
 * @file recoder_lidar.h
 * @brief 记录仪激光雷达相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的记录仪激光雷达功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <array>
#include "../common/header.h"
#include "sensor_common.h"

namespace hv {
namespace interface {

// 记录仪激光雷达帧结构
struct RecoderLidarFrame {
  Header header;                             // 数据头
  SensorHeader meta_header;               // 元数据头
  PointCloudFormat point_cloud_format;       // 点云格式
  uint32_t height;                           // 行数,如果点云是无序的,高度是1,宽度是点云数量
  uint32_t width;                            // 列数
  uint32_t point_step;                       // 点步长,一个点占用的字节数
  uint32_t row_step;                         // 行步长,一行占用的字节数
  std::vector<uint8_t> frame_payload;        // 帧载荷数据
};

}    // namespace interface
}    // namespace hv
