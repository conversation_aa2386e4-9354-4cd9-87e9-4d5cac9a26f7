/**
 * @file gnss_types.h
 * @brief GNSS类型定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint>
namespace hv {
namespace interface {
enum class SatelliteSystem : uint8_t { GPS, GLONASS, BDS, GALILEO };

enum class SolutionStatus : uint8_t {
  SOL_COMPUTED = 0,          // 解算成功
  INSUFFICIENT_OBS = 1,      // 观测值不足
  NO_CONVERGENCE = 2,        // 未收敛
  SINGULARITY = 3,           // 奇异性
  COV_TRACE = 4,             // 协方差迹过大
  TEST_DIST = 5,             // 测试距离过大
  COLD_START = 6,            // 冷启动
  V_H_LIMIT = 7,             // 速度/高度限制
  VARIANCE = 8,              // 方差过大
  RESIDUALS = 9,             // 残差过大
  DELTA_POS = 10,            // 位置变化过大
  NEGATIVE_VAR = 11,         // 负方差
  INTEGRITY_WARNING = 13,    // 完整性警告
  PENDING = 18,              // 待处理
  INVALID_FIX = 19,          // 无效固定解
  UNAUTHORIZED = 20,         // 未授权
  INVALID_RATE = 22          // 无效速率
};

enum class PositionVelocityType : uint8_t {
  NONE = 0,                // 无解
  FIXEDPOS = 1,            // 固定位置
  FIXEDHEIGHT = 2,         // 固定高度
  FLOATCONV = 4,           // 浮点收敛
  WIDELANE = 5,            // 宽巷
  NARROWLANE = 6,          // 窄巷
  DOPPLER_VELOCITY = 8,    // 多普勒速度
  SINGLE = 16,             // 单点
  PSRDIFF = 17,            // 伪距差分
  WAAS = 18,               // WAAS
  PROPOGATED = 19,         // 传播
  OMNISTAR = 20,           // OmniSTAR
  L1_FLOAT = 32,           // L1浮点
  IONOFREE_FLOAT = 33,     // 消电离层浮点
  NARROW_FLOAT = 34,       // 窄巷浮点
  L1_INT = 48,             // L1整数
  WIDE_INT = 49,           // 宽巷整数
  NARROW_INT = 50,         // 窄巷整数
  RTK_DIRECT_INS = 51,     // RTK直接INS
  INS_SBAS = 52,           // INS SBAS
  INS_PSRSP = 53,          // INS PSRSP
  INS_PSRDIFF = 54,        // INS PSRDIFF
  INS_RTKFLOAT = 55,       // INS RTK浮点
  INS_RTKFIXED = 56,       // INS RTK固定
  INS_OMNISTAR = 57,       // INS OmniSTAR
  INS_OMNISTAR_HP = 58,    // INS OmniSTAR HP
  INS_OMNISTAR_XP = 59,    // INS OmniSTAR XP
  OMNI_HP = 64,            // OmniSTAR HP
  OMNI_XP = 65,            // OmniSTAR XP
  PPP_CONVERGING = 68,     // PPP收敛中
  PPP = 69,                // PPP
  OPERATIONAL = 70,        // 运行中
  WARNING = 71,            // 警告
  OUT_OF_BOUNDS = 72,      // 超出边界
  INS_CONVERGING = 73,     // INS收敛中
  INS = 74,                // INS
  RTK_CONVERGING = 75,     // RTK收敛中
  RTK = 76,                // RTK
  RTK_DGPS = 77,           // RTK DGPS
  RTK_FLOAT = 78,          // RTK浮点
  RTK_FIXED = 79,          // RTK固定
  DGPS = 80,               // DGPS
  SBAS = 81,               // SBAS
  PSR = 82,                // PSR
  UNKNOWN = 255            // 未知
};
}    // namespace interface
}    // namespace hv
