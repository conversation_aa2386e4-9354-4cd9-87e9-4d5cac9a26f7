/**
 * @file gnss_imu.h
 * @brief GNSS IMU测量定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */
#pragma once

#include "../common/geometry/transform.h"
#include "../common/header.h"
#include "sensor_common.h"

namespace hv {
namespace interface {

struct ImuMeasurement {
  Header header;
  SensorHeader meta_header;
  Vector3d acceleration_vcs;        // 加速度（m/s^2）
  Vector3d angular_velocity_vcs;    // 角速度（rad/s）
  float accelerometer_temperature;                 // 加速度计温度（℃）unit: ℃
  float gyroscope_temperature;                     // 陀螺仪温度（℃）unit: ℃
};

}    // namespace interface
}    // namespace hv
