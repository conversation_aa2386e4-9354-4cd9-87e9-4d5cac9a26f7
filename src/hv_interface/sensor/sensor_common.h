/**
 * @file sensor_common.h
 * @brief 传感器通用定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的传感器通用功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <array>
#include "../common/header.h"

namespace hv {
namespace interface {

// 传感器类型枚举
enum class SensorType : uint8_t {
  UNKNOWN = 0,           // 未知传感器
  LIDAR = 1,             // 激光雷达
  CAMERA = 2,            // 相机
  RADAR = 3,             // 雷达
  IMU = 4,               // 惯性测量单元
  GNSS = 5,              // 全球导航卫星系统
  ODOMETRY = 6,          // 里程计
  ULTRASONIC = 7,        // 超声波传感器
  THERMAL = 8,           // 热成像传感器
  EVENT_CAMERA = 9,      // 事件相机
  CUSTOM = 255           // 自定义传感器
};

enum class SensorId : uint8_t {
    UNKNOWN = 0, 
    FRONT_CAMERA_WIDE = 1,
    FRONT_CAMERA_FAR = 2,
    FRONT_LEFT_CAMERA = 3,
    FRONT_RIGHT_CAMERA = 4,
    REAR_CAMERA = 5,
    REAR_LEFT_CAMERA = 6,
    REAR_RIGHT_CAMERA = 7,
    FRONT_LIDAR = 20,
    REAR_LIDAR = 21,
    LEFT_LIDAR = 22,
    RIGHT_LIDAR = 23,
    FRONT_LEFT_SHORT_LIDAR = 24,
    FRONT_RIGHT_SHORT_LIDAR = 25,
    REAR_LEFT_SHORT_LIDAR = 26,
    REAR_RIGHT_SHORT_LIDAR = 27,
    FRONT_FISHEYE = 30,
    REAR_FISHEYE = 31,
    LEFT_FISHEYE = 32,
    RIGHT_FISHEYE = 33,
    FRONT_RADAR = 40,
    FRONT_LEFT_RADAR = 41,
    FRONT_RIGHT_RADAR = 42,
    REAR_LEFT_RADAR = 43,
    REAR_RIGHT_RADAR = 44,
    IMU = 50,
    GNSS = 60,
    CUSTOM = 255,
};

struct SensorHeader {
  double global_timestamp; // 自Unix纪元的秒数,单位为秒
  double local_timestamp;  // 本地算法时间戳，单位为秒
  SensorId sensor_id;
  uint32_t frame_sequence;
};

// 点云格式枚举
enum class PointCloudFormat : uint8_t {
  UNKNOWN = 0,           // 未知格式
  XYZ = 1,               // XYZ格式
  XYZI = 2,              // XYZ + 强度
  XYZIR = 3,             // XYZ + 强度 + Ring
  XYZIRT = 4,            // XYZ + 强度 + Ring + timeoffset
  XYZIT = 5,             // XYZ + 强度 + timeoffset
  RAW = 6,               // 原始数据
  CUSTOM = 255           // 自定义格式
};

}    // namespace interface
}    // namespace hv

