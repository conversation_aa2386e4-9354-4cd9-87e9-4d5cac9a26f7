/**
 * @file gnss_heading.h
 * @brief GNSS航向测量定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include "../common/header.h"
#include "gnss_types.h"
#include "sensor_common.h"

namespace hv {
namespace interface {

struct HeadingMeasurement {
  Header header;                                      // publish 时间戳
  SensorHeader meta_header;                             // 元时间戳
  SolutionStatus solution_status = SolutionStatus::SOL_COMPUTED;     // 解算状态
  PositionVelocityType heading_type = PositionVelocityType::NONE;    // 航向类型
  float antenna_distance;                                            // 天线距离（4字节）unit: m, [0 ~ +100]
  float heading;                                                     // 航向角（4字节）unit: degree, [0 ~ +360]
  float heading_stddev;         // 航向角标准差（4字节）unit: degree, [0 ~ +10]
  int32_t num_sat_tracked;      // 跟踪卫星数（4字节）unit: 个, [0 ~ +50]
  int32_t num_sat_used;         // 使用卫星数（4字节）unit: 个, [0 ~ +50]
  uint32_t galileo_bd_mask;     // Galileo北斗掩码（4字节）
  uint32_t gps_glonass_mask;    // GPS GLONASS掩码（4字节）
};

}    // namespace interface
}    // namespace hv
