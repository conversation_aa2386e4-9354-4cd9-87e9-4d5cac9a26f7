/**
 * @file gnss_rtk.h
 * @brief GNSS RTK测量定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include "../common/geometry/geometry.h"
#include "../common/geometry/transform.h"
#include "../common/header.h"
#include "gnss_types.h"
#include "sensor_common.h"
#include <cstdint>
#include <string>

namespace hv {
namespace interface {
struct GnssMeasurement {
  Header header;                                              // publish 时间戳
  SensorHeader meta_header;                                     // 元时间戳
  SolutionStatus position_solution_status = SolutionStatus::SOL_COMPUTED;    // 定位状态
  PositionVelocityType position_type = PositionVelocityType::NONE;           // POS类型
  Pointllh position;                                          // wgs84
  Vector3d position_stddev;                                   // 位置标准差（m）
  float undulation = 0.0f;                                                   // 大地水准面高程（m）
  int8_t num_sat_tracked = 0;                                                // 跟踪卫星数
  int8_t num_sat_used = 0;                                                   // 使用卫星数
  float position_diff_age = 0.0f;                                            // 位置差分龄期（秒）
  float solution_age = 0.0f;                                                 // 解算龄期（秒）
  SolutionStatus velocity_solution_status = SolutionStatus::SOL_COMPUTED;    // 速度状态
  PositionVelocityType velocity_type = PositionVelocityType::NONE;           // VEL类型
  Vector3d velocity;                                          // 速度（ENU坐标系）
  Vector3d velocity_stddev;                                   // 速度标准差（m/s）
  float velocity_latency = 0.0f;                                             // 速度延迟（秒）
  float velocity_diff_age = 0.0f;                                            // 速度差分龄期（秒）
  std::string base_station_id;                                               // 基站ID
  int32_t datum_id = 0;                                                      // 大地基准ID
  uint32_t galileo_bd_mask = 0;                                              // Galileo掩码
  uint32_t gps_glonass_mask = 0;                                             // GPS/GLONASS掩码
  float gdop = 0.0f;                                                         // 几何精度因子
  float pdop = 0.0f;                                                         // 位置精度因子
  float hdop = 0.0f;                                                         // 水平精度因子
  float htdop = 0.0f;                                                        // 高程精度因子
  float tdop = 0.0f;                                                         // 时间精度因子
  float elev_mask = 0.0f;                                                    // 仰角掩码（度）
  int32_t dops_num_sat_tracked = 0;                                          // DOP跟踪卫星数
  uint8_t signal_to_noise_ratio = 0;                                         // 信号噪声比
};
}    // namespace interface
}    // namespace hv
