/**
 * @file prediction.h
 * @brief 预测障碍物相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include "../common/geometry/geometry.h"
#include "../common/header.h"
#include "../perception/perception_common.h"

namespace hv {
namespace interface {

struct PredictionHeader {
    double global_timestamp;
    double local_timestamp;  // 从融合拿到目标的时间戳,单位s
    uint8_t major_version;
    uint8_t minor_version;
    uint8_t num_valid_angents;     
};

enum class PredictionTrajectoryIntent: uint8_t{
    UNKNOWN = 0,  //intent会给出除了行人之外的所有目标意图,行人给unknown
    LANE_FOLLOW =1,  //如果目标车辆在车道内逆行,则赋予lane_follow意图,如果目标车辆预测会压线,则依然是lanefllow
    LANE_CHANGE_LEFT =2, //合流车道,给出lane change行为预测
    LANE_CHANGE_RIGHT =3,
    STRAIGHT =4,   //小路口横穿,只要轨迹进入自由空间加直行,就给straight
    TURN_LEFT =5,  //前方豁口进入自车左侧对向车道,给出turn left意图
    TURN_RIGHT =6,
    U_TURN_LEFT =7,
    U_TURN_RIGHT =8,    //右转掉头较为少见，可以和右转合并
    REVERSE =9,  //倒车
};

enum class PredictionTrajectoryType: uint8_t{
    UNKNOWN = 0,    //置信度不高,
    STATIONARY = 1, //静止轨迹
    CV = 2, //横速度速度模型
    CTRA = 3,//横曲率加速度模型
    MARGINAL = 4,//不考虑目标交互的模型轨迹
    INTERACT = 9,//考虑目标交互的模型轨迹
};

struct PredictionTrajectoryPoint {
    Point3d point; // 位置，单位为m,坐标系为boot
    float velocity; // 速度，单位为m/s 
    float heading;  // 速度角度，单位为rad,相对boot x方向角度
    float sigma;    //轨迹点位置的概率分布sigma，应该会随着点索引的增加而增大,无sigma给-1,单位m
};

struct PredictionTrajectory {
    float confidence; // 置信度，范围为0-1,所有轨迹概率总和为1
    uint8_t num_valid_points; // 有效点数,若需要轨迹穿墙,则实际发送轨迹可能达不到8s,此时用
                              // 此字段表示实际发送的轨迹点数
    uint8_t num_spaese_index; // 高置信度轨迹点索引,在此索引后的轨迹点为低置信度轨迹点
    float dense_sample_interval; // 高置信度轨迹点采样间隔，单位为s
    float sparse_sample_interval; // 低置信度轨迹点采样间隔，单位为s
    uint32_t target_lane_id; //目标车道，通常用于障碍物车过路口匹配出口车道,0表示无目标车道
    PredictionTrajectoryIntent intent; // 意图
    PredictionTrajectoryType type; // 轨迹类型
    std::vector<PredictionTrajectoryPoint> points; // 轨迹点,最多60个点
};

struct PredictionAgent {
    uint32_t agent_id; //id 为0表示自车
    std::vector<PredictionTrajectory> prediction_trajectories; //最多6条轨迹,0index是置信度最高轨迹
};

// 障碍物列表结构体
struct PredictionAgentList {
    Header header; 
    PredictionHeader meta_header;
    std::vector<PredictionAgent> prediction_agents; // 最多100个预测目标
};

}    // namespace interface
}    // namespace hv
