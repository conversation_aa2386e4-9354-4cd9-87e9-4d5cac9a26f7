#pragma once
#include <string>
#include <vector>
#include <cstdint>
#include "../common/geometry/geometry.h"
#include "../common/header.h"

namespace hv {
namespace interface {

struct PlanningHeader {
    double global_timestamp;
    double local_timestamp;
    std::string module_name;
    uint32_t frame_sequence;
    uint32_t version;
    std::string frame_id;
};

struct CriticalRegion {
    std::vector<Polygon> region;
};

struct PathPoint {
    double x = 0.0;
    double y = 0.0;
    double z = 0.0;
    double theta = 0.0;
    double kappa = 0.0;
    double s = 0.0;
    double dkappa = 0.0;
    double ddkappa = 0.0;
    std::string lane_id;
    double x_derivative = 0.0;
    double y_derivative = 0.0;
};

struct TrajectoryPoint {
    PathPoint path_point;
    double v = 0.0;
    double a = 0.0;
    double relative_time = 0.0;
    double da = 0.0;
    double steer = 0.0; 
};

struct Estop {
    bool is_estop = false;
    std::string reason;
};

enum class GearPosition : int8_t {
    GEAR_NEUTRAL = 0,
    GEAR_DRIVE = 1,
    GEAR_REVERSE = 2,
    GEAR_PARKING = 3,
    GEAR_LOW = 4,
    GEAR_INVALID = 5,
    GEAR_NONE = 6
};

enum class Task: int8_t {
    NOT_SET = 0,
    CRUISE = 1,
    STOP = 2,
    ESTOP = 3,
    CHANGE_LANE = 4,
    MISSION_COMPLETE = 5,
    NOT_READY = 7,
    PARKING = 8
};

struct TargetLane {
    std::string id;
    double start_s = 0.0;
    double end_s = 0.0;
    double speed_limit = 0.0;
};

struct MainDecision {
    Task task;
    std::vector<TargetLane> target_lane;
};

enum class ObjectTag: int8_t {
    NOT_SET = 0, 
    IGNORE = 1, //横纵向都可以忽略的
    STOP = 2, //刹车到目标前停止
    FOLLOW = 3, //跟随,跟随加减速,保持时距
    YIELD = 4, //让行,主要针对cut in活merge车辆,进行纯纵向让行
    OVERTAKE = 5, //超车,纵向,针对cut in或merge车辆,抢行
    NUDGE = 6, //横向,静态侵占目标进行横向避让
    AVOID = 7,//未使用
    SIDE_PASS = 8,//未使用
};

struct ObjectDecisionType {
    ObjectTag object_tag;
};

struct ObjectDecision {
    std::string id;
    int32_t perception_id = 0;
    std::vector<ObjectDecisionType> object_decisions;
};

struct ObjectDecisions {
    std::vector<ObjectDecision> decision;
};

enum class TurnSignal: int8_t {
    TURN_NONE = 0,
    TURN_LEFT = 1,
    TURN_RIGHT = 2
};

struct VehicleSignal {
    TurnSignal turn_signal;
    bool high_beam = false;
    bool low_beam = false;
    bool horn = false;
    bool emergency_light = false;
};

enum class Decision: int32_t {
    LANE_CHANGE_LEFT = 1,
    LANE_CHANGE_RIGHT = 2,
    LANE_BORROW_LEFT = 4,
    LANE_BORROW_RIGHT = 8,
    INTERSECT_GO_STRAIGHT = 16,
    INTERSECT_TURN_LEFT = 32,
    INTERSECT_TURN_RIGHT = 64,
    INTERSECT_U_TURN = 128,
    LANE_BORROW_IN_NON_MOTORIZED_LANE = 256,
    LANE_KEEP = 512,
    PULL_OVER = 1024,
    OPEN_SPACE_PASS = 2048
};

enum class Scene: uint64_t {
    NORMAL_ROAD = 1,
    INTERSECT = 2
};

struct VehicleDecisionStatus {
    double timestamp = 0.0;
    Scene scene;
    Decision decision;
};

struct DecisionResult {
    MainDecision main_decision;
    ObjectDecisions object_decision;
    VehicleSignal vehicle_signal;
};

struct TaskStats {
    std::string name;
    double time_ms = 0.0;
};

struct LatencyStats {
    double total_time_ms = 0.0;
    std::vector<TaskStats> task_stats;
    double init_frame_time_ms = 0.0;
};

enum class RightOfWayStatus: int8_t {
    UNPROTECTED = 0,
    PROTECTED = 1
};

enum class Advice: int8_t {
    UNKNOWN = 0,
    DISALLOW_ENGAGE = 1,
    READY_TO_ENGAGE = 2,
    KEEP_ENGAGED = 3,
    PREPARE_DISENGAGE = 4
};

struct EngageAdvice {
    Advice advice;
    std::string reason;
};

enum class TrajectoryType: int8_t {
    UNKNOWN = 0,
    NORMAL = 1,
    PATH_FALLBACK = 2,
    SPEED_FALLBACK = 3,
    PATH_REUSED = 4
};

struct LaneSpeedLimit {
    double speed_limit = 0.0;
};

struct RouteHorizon {
    double timestamp = 0.0;
    std::vector<std::string> lane_ids;
};

enum class LateralIntent : uint8_t {
  NONE = 0,
  LEFT_U_TURN = 1,
  LEFT_TURN = 2,
  LEFT_LANE_CHANGE = 3,
  KEEP = 4,
  RIGHT_LAEN_CHANGE = 5,
  RIGHT_TURN = 6,
  RIGHT_U_TURN = 7
};
enum class LongitudinalIntent : uint8_t {
  NONE = 0,
  LARGE_DEC = 1,
  DEC = 2,
  KEEP = 3,
  ACC = 4,
  LARGE_ACC = 5
};

struct EgoIntent {
  std::vector<LateralIntent> lateral_intents;
  std::vector<uint32_t> lateral_index; // lateral intent changes from
                                       // this traj point.
  std::vector<LongitudinalIntent> longitudinal_intens;
  std::vector<uint32_t> longitudinal_index; // longitudinal intent
                                            // changes from this traj
                                            // point.
};

struct Trajectory {
    Header header;
    PlanningHeader planning_header;
    double total_path_length = 0.0;
    double total_path_time = 0.0;
    std::vector<TrajectoryPoint> trajectory_point;
    Estop estop;
    std::vector<PathPoint> path_point;
    bool is_replan = false;
    std::string replan_reason;
    bool is_uturn = false;
    GearPosition gear;
    DecisionResult decision;
    LatencyStats latency_stats;
    PlanningHeader routing_header;
    RightOfWayStatus right_of_way_status;
    std::vector<std::string> lane_id;
    EngageAdvice engage_advice;
    CriticalRegion critical_region;
    TrajectoryType trajectory_type;
    std::vector<std::string> target_lane_id;
    EgoIntent ego_intent;
};

} // namespace interface
} // namespace hv
