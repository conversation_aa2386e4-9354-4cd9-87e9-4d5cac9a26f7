/**
 * @file map.h
 * @brief 地图相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的地图功能定义
 */

#pragma once
#include <string>
#include <vector>
#include "../common/geometry/geometry.h"
#include "../common/header.h"

namespace hv {
namespace interface {

// 地图头部信息
struct MapHeader {
    std::vector<uint8_t> version;
    std::vector<uint8_t> date;
    std::string projection;
    std::vector<uint8_t> district;
    std::vector<uint8_t> generation;
    std::vector<uint8_t> rev_major;
    std::vector<uint8_t> rev_minor;
    double left;
    double top;
    double right;
    double bottom;
    std::vector<uint8_t> vendor;
};

struct MapCurveSegment {
    Polylineenu line_segment;
    double s;                    // 起始位置(s坐标)
    Pointenu start_position;
    double heading;              // 起始方向
    double length;
};

struct MapCurve {
    std::vector<MapCurveSegment> segments;
};

// 车道边界类型
enum class MapLaneBoundaryType: uint8_t {
    UNKNOWN = 0,
    DOTTED_YELLOW = 1,
    DOTTED_WHITE = 2,
    SOLID_YELLOW = 3,
    SOLID_WHITE = 4,
    DOUBLE_YELLOW = 5,
    CURB = 6
};

struct MapLaneBoundaryTypeInfo {
    double s;                                    // 相对于边界起点的偏移
    std::vector<MapLaneBoundaryType> types;     // 支持多种类型
};

struct MapLaneBoundary {
    MapCurve curve;
    double length;
    bool virtual_boundary;                       // 指示车道边界是否在现实世界中存在
    std::vector<MapLaneBoundaryTypeInfo> boundary_types;  // 按s升序排列
};

// 中心点到最近边界的关联
struct MapLaneSampleAssociation {
    double s;
    double width;
};

// 车道类型枚举
enum class MapLaneType: uint8_t {
    NONE = 1,
    CITY_DRIVING = 2,
    BIKING = 3,
    SIDEWALK = 4,
    PARKING = 5,
    SHOULDER = 6,
    SHARED = 7
};

// 车道转向枚举
enum class MapLaneTurn: uint8_t {
    NO_TURN = 1,
    LEFT_TURN = 2,
    RIGHT_TURN = 3,
    U_TURN = 4
};

// 车道方向枚举
enum class MapLaneDirection: uint8_t {
    FORWARD = 1,
    BACKWARD = 2,
    BIDIRECTION = 3
};

// 车道结构
struct MapLane {
    std::string id;
    MapCurve central_curve;                      // 中心车道作为参考轨迹
    MapLaneBoundary left_boundary;               // 左边界曲线
    MapLaneBoundary right_boundary;              // 右边界曲线
    double length;                               // 长度，单位米
    double speed_limit;                          // 速度限制，单位米/秒
    std::vector<std::string> overlap_ids;              // 重叠ID
    std::vector<std::string> predecessor_ids;          // 前驱车道ID
    std::vector<std::string> successor_ids;            // 后继车道ID
    std::vector<std::string> left_neighbor_forward_lane_ids;    // 同向左侧邻居车道ID
    std::vector<std::string> right_neighbor_forward_lane_ids;   // 同向右侧邻居车道ID
    MapLaneType type;                            // 车道类型
    MapLaneTurn turn;                            // 车道转向
    std::vector<std::string> left_neighbor_reverse_lane_ids;    // 反向左侧邻居车道ID
    std::vector<std::string> right_neighbor_reverse_lane_ids;   // 反向右侧邻居车道ID
    std::string junction_id;                           // 路口ID
    std::vector<MapLaneSampleAssociation> left_samples;   // 中心点到左边界最近点的关联
    std::vector<MapLaneSampleAssociation> right_samples;  // 中心点到右边界最近点的关联
    MapLaneDirection direction;                  // 车道方向
    std::vector<MapLaneSampleAssociation> left_road_samples;   // 中心点到道路左边界最近点的关联
    std::vector<MapLaneSampleAssociation> right_road_samples;  // 中心点到道路右边界最近点的关联
    std::vector<std::string> self_reverse_lane_ids;    // 自身反向车道ID
};

// 道路边界边缘类型
enum class MapBoundaryEdgeType: uint8_t {
    UNKNOWN = 0,
    NORMAL = 1,
    LEFT_BOUNDARY = 2,
    RIGHT_BOUNDARY = 3
};

struct MapBoundaryEdge {
    MapCurve curve;
    MapBoundaryEdgeType type;
};

struct MapBoundaryPolygon {
    std::vector<MapBoundaryEdge> edges;
};

// 带洞的边界
struct MapRoadBoundary {
    MapBoundaryPolygon outer_polygon;            // 外多边形
    std::vector<MapBoundaryPolygon> holes;       // 如果没有洞，则为空
};

struct MapRoadROIBoundary {
    std::string id;
    std::vector<MapRoadBoundary> road_boundaries;
};

// 道路路段
struct MapRoadSection {
    std::string id;
    std::vector<std::string> lane_ids;           // 该路段包含的车道
    MapRoadBoundary boundary;                    // 路段边界
};

// 道路类型枚举
enum class MapRoadType: uint8_t {
    UNKNOWN = 0,
    HIGHWAY = 1,
    CITY_ROAD = 2,
    PARK = 3
};

// 道路结构
struct MapRoad {
    std::string id;
    std::vector<MapRoadSection> sections;        // 道路路段
    std::string junction_id;                     // 如果车道不在路口内，则为空
    MapRoadType type;                            // 道路类型
};

// 路口结构
struct MapJunction {
    std::string id;
    Polygonenu polygon;                          // 路口多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
};

// 重叠结构
struct MapOverlap {
    std::string id;
    std::vector<std::string> object_ids;               // 重叠对象ID
};

// 信号灯结构
struct MapSignal {
    std::string id;
    Polygonenu polygon;                          // 信号灯多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
    std::vector<std::string> sub_signal_ids;           // 子信号灯ID
};

// 停车标志结构
struct MapStopSign {
    std::string id;
    std::vector<std::string> overlap_ids;              // 重叠ID
    Polygonenu polygon;                          // 停车标志多边形
    std::vector<std::string> stop_line_ids;            // 停车线ID
};

// 让行标志结构
struct MapYieldSign {
    std::string id;
    std::vector<std::string> overlap_ids;              // 重叠ID
    Polygonenu polygon;                          // 让行标志多边形
    std::vector<std::string> stop_line_ids;            // 停车线ID
};

// 人行横道结构
struct MapCrosswalk {
    std::string id;
    Polygonenu polygon;                          // 人行横道多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
};

// 停车位结构
struct MapParkingSpace {
    std::string id;
    Polygonenu polygon;                          // 停车位多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
    double heading;                              // 停车位朝向
};

// PNC路口结构
struct MapPNCJunction {
    std::string id;
    Polygonenu polygon;                          // PNC路口多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
    std::vector<std::string> passage_group_ids;        // 通道组ID
};

// RSU结构
struct MapRSU {
    std::string id;
    Pointenu position;                        // RSU位置
};

// 区域结构
struct MapArea {
    std::string id;
    Polygonenu polygon;                          // 区域多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
};

// 限速带结构
struct MapSpeedBump {
    std::string id;
    std::vector<std::string> overlap_ids;              // 重叠ID
    std::vector<std::string> position_ids;             // 位置ID
};

// 限速控制结构
struct MapSpeedControl {
    std::string id;
    std::vector<std::string> overlap_ids;              // 重叠ID
    std::vector<std::string> position_ids;             // 位置ID
    double speed_limit;                          // 速度限制，单位米/秒
};

// 清除区域结构
struct MapClearArea {
    std::string id;
    Polygonenu polygon;                          // 清除区域多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
};

// 道闸结构
struct MapBarrierGate {
    std::string id;
    Polygonenu polygon;                          // 道闸多边形
    std::vector<std::string> overlap_ids;              // 重叠ID
};

// 主地图结构
struct Map {
    Header header;
    MapHeader metaheader;                        // 地图头部信息
    std::vector<MapCrosswalk> crosswalks;        // 人行横道
    std::vector<MapJunction> junctions;          // 路口
    std::vector<MapLane> lanes;                  // 车道
    std::vector<MapStopSign> stop_signs;         // 停车标志
    std::vector<MapSignal> signals;              // 信号灯
    std::vector<MapYieldSign> yield_signs;       // 让行标志
    std::vector<MapOverlap> overlaps;            // 重叠
    std::vector<MapClearArea> clear_areas;       // 清除区域
    std::vector<MapSpeedBump> speed_bumps;       // 限速带
    std::vector<MapRoad> roads;                  // 道路
    std::vector<MapParkingSpace> parking_spaces; // 停车位
    std::vector<MapPNCJunction> pnc_junctions;   // PNC路口
    std::vector<MapRSU> rsus;                    // RSU
    std::vector<MapArea> areas;                  // 区域
    std::vector<MapBarrierGate> barrier_gates;   // 道闸
};

} // namespace interface
} // namespace hv

