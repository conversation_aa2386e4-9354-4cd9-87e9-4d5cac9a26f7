/**
 * @file localization.h
 * @brief 定位相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */
#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include "../common/header.h"
#include "../common/geometry/transform.h"
#include "../common/geometry/geometry.h"
#include "landmark.h"

namespace hv {
namespace interface {

// 定位器更新状态位掩码
namespace LocalizerUpdateMask {
constexpr uint32_t RTK_POSITION_UPDATE = (1U << 0);                  // RTK位置更新
constexpr uint32_t PSR_VELOCITY_UPDATE = (1U << 1);                  // PSR速度更新
constexpr uint32_t DUAL_ANTENNA_HEADING_UPDATE = (1U << 2);          // 双天线航向更新
constexpr uint32_t SCAN_MATCH_POSITION_UPDATE = (1U << 3);           // 扫描匹配位置更新
constexpr uint32_t SCAN_MATCH_ORIENTATION_UPDATE = (1U << 4);        // 扫描匹配姿态更新
constexpr uint32_t WHEEL_SENSOR_SPEED_UPDATE = (1U << 5);            // 轮速传感器更新
constexpr uint32_t ZERO_VELOCITY_UPDATE = (1U << 6);                 // 零速更新
constexpr uint32_t STATIONARY_IMU_CALIBRATION_UPDATE = (1U << 7);    // 静止IMU校准
constexpr uint32_t ONBOARD_LOCALIZATION_UPDATE = (1U << 8);          // 车载定位更新
}    // namespace LocalizerUpdateMask

// 初始对准状态位掩码
namespace InitialAlignmentMask {
constexpr uint32_t HAS_COARSE_POSITION = (1U << 0);        // 粗略位置已知
constexpr uint32_t HAS_PRECISE_POSITION = (1U << 1);       // 精确位置已知
constexpr uint32_t HAS_VELOCITY = (1U << 2);               // 速度已知
constexpr uint32_t HAS_HORIZONTAL_ATTITUDE = (1U << 3);    // 水平姿态已知
constexpr uint32_t HAS_AZIMUTH = (1U << 4);                // 方位角已知
constexpr uint32_t HAS_IMU_DATA = (1U << 5);               // IMU数据有效
constexpr uint32_t IMU_ALIGN_AZIMUTH = (1U << 7);          // IMU方位角对准完成
constexpr uint32_t IMU_ALIGN_VELOCITY = (1U << 8);         // IMU速度对准完成
constexpr uint32_t IMU_ALIGN_POSITION = (1U << 9);         // IMU位置对准完成
}    // namespace InitialAlignmentMask

// 定位结果状态位掩码
namespace LocalizerSolutionMask {
constexpr uint32_t UNKNOWN = (1U << 0);                       // 未知状态
constexpr uint32_t ALIGNING = (1U << 1);                      // 正在对准
constexpr uint32_t ALIGN_COMPLETED = (1U << 2);               // 对准完成
constexpr uint32_t SOLUTION_GOOD = (1U << 3);                 // 解算质量良好
constexpr uint32_t HIGH_VARIANCE = (1U << 4);                 // 高方差警告
constexpr uint32_t PROPAGATED = (1U << 5);                    // 预测状态
constexpr uint32_t BAD_MISCLOSURE = (1U << 6);                // 闭合差异常
constexpr uint32_t LONGITUDINAL_HIGH_VARIANCE = (1U << 7);    // 纵向高方差
constexpr uint32_t WARNING_MISCLOSURE = (1U << 8);            // 闭合差警告
constexpr uint32_t INITIALIZE_TURN_ON_BIAS = (1U << 9);       // 初始化开机偏置
}    // namespace LocalizerSolutionMask

struct GlobalLocalization {
  Pointllh position;            // 全局位置,经纬高坐标
  Quaterniond orientation;      // 全局姿态四元数,ENU坐标轴
  EulerAnglesd euler_angles;    // 全局欧拉角,ENU坐标系,单位:弧度
  Vector3denu velocity;         // 全局速度,ENU坐标系,单位:米/秒
  Vector3denu acceleration;     // 全局加速度,ENU坐标系,单位:米/秒^2

  Vector3denu position_stddev;         // 全局位置标准差,ENU坐标系,单位:米
  EulerAnglesd euler_angles_stddev;    // 全局欧拉角标准差,ENU坐标系,单位:弧度
  Vector3denu velocity_stddev;         // 全局速度标准差,ENU坐标系,单位:米/秒
  Vector3denu acceleration_stddev;     // 全局加速度标准差,ENU坐标系,单位:米/秒^2

  uint32_t localizer_update_status;    // 定位更新事件类型,bit mask, 0-31,bit位对应LocalizerUpdateMask中定义
  uint32_t initial_alignment_status;     // 初始对准状态,bit mask, 0-31,bit位对应InitialAlignmentMask中定义
  uint32_t localizer_solution_status;    // 定位结果状态,bit mask, 0-31,bit位对应LocalizerSolutionMask中定义
};

struct LocalLocalization {
  Vector3d position;            // 局部位置,boot坐标系,单位:米
  Quaterniond orientation;      // 局部姿态四元数,boot坐标轴
  EulerAnglesd euler_angles;    // 局部欧拉角,boot坐标系,单位:弧度
  Vector3d velocity;            // 局部速度,boot坐标系,单位:米/秒
  Vector3d acceleration;        // 局部加速度,boot坐标系,单位:米/秒^2

  Vector3d position_stddev;            // 局部位置标准差,boot坐标系,单位:米
  EulerAnglesd euler_angles_stddev;    // 局部欧拉角标准差,boot坐标系,单位:弧度
  Vector3d velocity_stddev;            // 局部速度标准差,boot坐标系,单位:米/秒
  Vector3d acceleration_stddev;        // 局部加速度标准差,boot坐标系,单位:米/秒^2

  Vector3d angular_velocity;           // 局部角速度,vcs坐标系,单位:弧度/秒
  Vector3d angular_velocity_stddev;    // 局部角速度标准差,单位:弧度/秒
  float speed;                                        // 标量速度,单位:米/秒

  uint32_t localizer_update_status;    // 定位更新事件类型,bit mask, 0-31,bit位对应LocalizerUpdateMask中定义
  uint32_t initial_alignment_status;     // 初始对准状态,bit mask, 0-31,bit位对应InitialAlignmentMask中定义
  uint32_t localizer_solution_status;    // 定位结果状态,bit mask, 0-31,bit位对应LocalizerSolutionMask中定义
};

struct LocalizationTransform {
  Rigid3d transform_llh_to_boot;        // 全局到boot坐标系的变换
  Rigid3d transform_vcs_to_boot;        // vcs到boot坐标系的变换
  Rigid3d transform_avp_map_to_boot;    // avp_map到boot坐标系的变换
  Pointllh transform_center;            // transform enu center, only used for transform_llh_to_boot
};

struct LocalizationStatus {
  uint8_t quality;      // localization quality score \value_min{0.0} \value_max{100.0}
  uint32_t common;      // common status, detail status of localization
                        // 0 - error status
                        // 1 - ddpf initializing
                        // 2 - cp/apa/adas mode, localization map loading
                        // 3 - cp/apa/adas mode, localization initializing
                        // 4 - np/cp/apa/adas mode, rtk fix
                        // 5 - np/cp/apa/adas mode, rtk float
                        // 6 - np/cp/apa/adas mode, gnss spp
                        // 7 - np/cp/apa/adas mode, gnss lost
                        // 8 - np/cp/apa/adas mode, gnss lost, hdmap-perception lost,
  uint32_t extended;    // extend status
                        // extended & 0x0001 = imu_available
                        // extended & 0x0002 = wheel_speed_available
                        // extended & 0x0004 = wheel_encoder_available
                        // extended & 0x0008 = gps_available
                        // extended & 0x0010 = camera_available
                        // extended & 0x0020 = perception_available
                        // extended & 0x0040 = lidar_available
                        // extended & 0x0080 = wheel_direction_available
                        // extended & 0x0100 = map_available
  uint8_t type;         // current egopose mode
                        // 0 - error type, egopose invalid
                        // 1 - pilot
                        // 2 - apa
};

struct RoadLaneId {
  TrackId lane_id;
  TrackId road_id;
  uint32_t offset;    // in the unit of centimeter
  uint16_t road_class;
  uint16_t status;    // status of road id & lane id info 0-15, 0 - invalid, 1 - valid
  uint16_t reserved;
};

struct LinkOffset {
  std::string source;    // mlni/sdpro/tbt/adasis
  TrackId link_id;
  uint32_t offset;    // in the unit of centimeter
  uint16_t status;    // status of link offset info 0-15, 0 - invalid, 1 - valid
  uint16_t reserved;
};

struct LocalizationHeader {
  double global_timestamp; // 自Unix纪元的秒数,单位为秒
  double local_timestamp;  // 本地算法时间戳，单位为秒
  uint32_t frame_sequence;   // 帧序列号
  std::string frame_id;      // 帧ID
};

struct Localization {
  Header header;                      // publish数据头
  LocalizationHeader meta_header;             // 元数据头
  GlobalLocalization global_localization;            // 全局定位
  LocalLocalization local_localization;              // 局部定位
  Rigid3d transform;                  // 坐标系变换
  LocalizationStatus status;                         // 定位状态
  RoadLaneId road_lane_id;                           // 道路和车道ID
  std::vector<LinkOffset> link_offset_infos;         // link and offset information
                                                     // [0]: from source mlni
                                                     // [1]: from source sdpro
                                                     // [2]: from source tbt
                                                     // [3]: from source adasis v2
  int32_t num_landmarks;                             // 地标数量
  std::vector<Landmark> landmarks;    // 地标列表
};

}    // namespace interface
}    // namespace hv
