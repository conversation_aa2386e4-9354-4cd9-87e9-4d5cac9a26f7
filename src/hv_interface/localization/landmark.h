/**
 * @file landmark.h
 * @brief 地标相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include "../common/geometry/geometry.h"
#include "../common/geometry/transform.h"
#include <string>

namespace hv {
namespace interface {

struct TrackId {
  bool is_visible;
  uint8_t count;
  uint32_t id;
  uint32_t linked_id;
  uint32_t map_id;
  uint32_t area_id;
};

struct Landmark {
  Point3d position;     // 地标位置
  Vector3d orientation; // 地标方向
  std::string type;     // 地标类型
  double confidence;    // 置信度
};

} // namespace interface
} // namespace hv
