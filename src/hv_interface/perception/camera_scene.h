/**
 * @file camera_sence.h
 * @brief 静态场景感知相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的静态场景感知功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include "../common/geometry/geometry.h"
#include "../common/header.h"
#include "perception_common.h"

namespace hv {
namespace interface {

// ==================== 交通标志相关 ====================

// 交通标志类型枚举
enum class TrafficSignType : uint8_t {
    SIGN_UNKNOWN = 0,               // 未知标志
    SIGN_STOP = 1,                  // 停止标志
    SIGN_YIELD = 2,                 // 让行标志
    SIGN_SPEED_LIMIT = 3,           // 限速标志
    SIGN_NO_ENTRY = 4,              // 禁止通行标志
    SIGN_ONE_WAY = 5,               // 单行道标志
    SIGN_NO_PARKING = 6,            // 禁止停车标志
    SIGN_PEDESTRIAN_CROSSING = 7,   // 人行横道标志
    SIGN_TRAFFIC_LIGHT = 8,         // 交通信号灯标志
    SIGN_ROUNDABOUT = 9,            // 环岛标志
    SIGN_CONSTRUCTION = 10,         // 施工标志
    SIGN_OTHER = 11                 // 其他标志
};

// 交通标志状态枚举
enum class TrafficSignStatus : uint8_t {
    STATUS_UNKNOWN = 0,             // 未知状态
    STATUS_VISIBLE = 1,             // 完全可见
    STATUS_PARTIALLY_VISIBLE = 2,   // 部分可见
    STATUS_OCCLUDED = 3,            // 被遮挡
    STATUS_DAMAGED = 4              // 损坏
};

// 交通标志结构体
struct TrafficSign {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t sign_id;                    // 标志ID
    TrafficSignType sign_type;           // 标志类型
    TrafficSignStatus sign_status;       // 标志状态
    uint8_t confidence;                  // 置信度, 0-100
    Point2d position;                    // 标志位置，boot坐标系
    float heading;                      // 标志朝向，单位:弧度，[-pi,pi]
    float width;                        // 标志宽度，单位:米
    float height;                       // 标志高度，单位:米
    std::string sign_text;               // 标志文字内容（如限速值）
    uint8_t sign_value;                  // 标志数值（如限速值）
    bool is_illuminated;                 // 是否被照亮
    float distance;                     // 距离自车的距离，单位:米
};

// ==================== 车道线相关 ====================

// 车道线类型枚举
enum class LaneLineType : uint8_t {
    LINE_UNKNOWN = 0,           // 未知类型
    LINE_SOLID = 1,             // 实线
    LINE_DASHED = 2,            // 虚线
    LINE_DOUBLE_SOLID = 3,      // 双实线
    LINE_DOUBLE_DASHED = 4,     // 双虚线
    LINE_SOLID_DASHED = 5,      // 实虚线（实线在左）
    LINE_DASHED_SOLID = 6,      // 虚实线（虚线在左）
    LINE_CURB = 7,              // 路沿线
};

// 车道线颜色枚举
enum class LaneLineColor : uint8_t {
    COLOR_UNKNOWN = 0,          // 未知颜色
    COLOR_WHITE = 1,            // 白色
    COLOR_YELLOW = 2,           // 黄色
    COLOR_RED = 3,              // 红色
    COLOR_BLUE = 4,             // 蓝色
    COLOR_GREEN = 5             // 绿色
};

// 车道线点结构体
struct LaneLinePoint {
    Point2d position;                    // 车道线点位置，boot坐标系
    float curvature;                    // 曲率，单位:1/米
    float heading;                      // 朝向，单位:弧度，[-pi,pi]
    uint8_t quality;                     // 点质量，0-100
};

// 车道线结构体
struct LaneLine {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t line_id;                    // 车道线ID
    LaneLineType line_type;              // 车道线类型
    LaneLineColor line_color;            // 车道线颜色
    uint8_t confidence;                  // 置信度, 0-100
    std::vector<LaneLinePoint> points;   // 车道线点序列
    float width;                        // 车道线宽度，单位:米
    bool is_valid;                       // 是否有效
};

// ==================== 车道箭头相关 ====================

// 车道内箭头类型枚举
enum class LaneArrowType : uint8_t {
    ARROW_UNKNOWN = 0,           // 未知箭头
    ARROW_STRAIGHT = 1,          // 直行箭头
    ARROW_LEFT = 2,              // 左转箭头
    ARROW_RIGHT = 3,             // 右转箭头
    ARROW_LEFT_STRAIGHT = 4,     // 左转+直行箭头
    ARROW_RIGHT_STRAIGHT = 5,    // 右转+直行箭头
    ARROW_U_TURN = 6,            // 掉头箭头
    ARROW_LEFT_U_TURN = 7,       // 左转+掉头箭头
    ARROW_RIGHT_U_TURN = 8,      // 右转+掉头箭头
    ARROW_ALL_DIRECTIONS = 9,     // 全方向箭头
    ARROE_NOT_ALLOW_U_TURN = 10,  // 禁止掉头箭头
    ARROW_MERGE_IN = 11,          // 汇入箭头
};

// 车道内箭头结构体
struct LaneArrow {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t arrow_id;                   // 箭头ID
    LaneArrowType arrow_type;            // 箭头类型
    uint8_t confidence;                  // 置信度, 0-100
    Point2d position;                    // 箭头位置，boot坐标系
    float heading;                      // 箭头朝向，单位:弧度，[-pi,pi]
    float width;                        // 箭头宽度，单位:米
    float length;                       // 箭头长度，单位:米
    bool is_visible;                     // 是否可见
};

enum class OtherLandMarkType : uint8_t {
    LAND_MARK_UNKNOWN = 0,        // 未知标志
    LAND_MARK_STOP = 1,           // 停止标志
    LAND_MARK_YIELD = 2,          // 让行标志
    LAND_MARK_SPEED_LOWER_LIMIT = 3,    // 限速下限标志
    LAND_MARK_SPEED_UPPER_LIMIT = 4,    // 限速上限标志
};

// 其他车道内标志结构体
struct OtherLandMark {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t land_mark_id;               // 标志ID
    OtherLandMarkType land_mark_type;    // 标志类型
    Point2d position;                    // 箭头位置，boot坐标系
    float heading;                      // 箭头朝向，单位:弧度，[-pi,pi]
    float width;                        // 箭头宽度，单位:米
    float length;                       // 箭头长度，单位:米
    float speed_limit_lower_bound;      // 限速下限，单位:米/秒
    float speed_limit_upper_bound;      // 限速上限，单位:米/秒
    uint8_t confidence;                  // 置信度, 0-100
};

// ==================== 车道相关 ====================

// 车道结构体
struct Lane {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t lane_id;                    // 车道ID
    std::string lane_type;               // 车道类型（如"driving", "parking", "bicycle", "roundabout(环岛)"）
    uint8_t lane_direction;              // 车道方向，0-未知，1-同向，2-反向，3-双向
    uint8_t confidence;                  // 置信度, 0-100
    Polyline2d center_line;              // 车道中心线点序列
    float lane_width;                   // 车道宽度，单位:米
    uint32_t left_line_id; // 左侧车道线ID，为0则表示没有左侧车道线
    uint32_t right_line_id; // 右侧车道线ID，为0则表示没有右侧车道线
    uint32_t left_curb_id; // 左侧路沿ID，为0则表示没有左侧路沿
    uint32_t right_curb_id; // 右侧路沿ID，为0则表示没有右侧路沿
    std::vector<LaneArrow> lane_arrows;  // 车道内箭头列表
    std::vector<OtherLandMark> other_land_marks; // 其他车道标志列表
    uint8_t speed_limit; // 车道限速，单位:km/h
    bool is_valid;                       // 是否有效
};

// 锥桶流类型枚举
enum class TrafficConeFlowType : uint8_t {
    FLOW_UNKNOWN = 0,            // 未知类型
    FLOW_LANE_CLOSURE = 1,       // 车道封闭
    FLOW_LANE_DIVERSION = 2,     // 车道改道
    FLOW_MERGE = 3,              // 车道合并
    FLOW_SPLIT = 4,              // 车道分流
    FLOW_CONSTRUCTION = 5,       // 施工区域
    FLOW_EMERGENCY = 6,          // 紧急情况
    FLOW_TEMPORARY = 7           // 临时设置
};

// 锥桶流结构体
struct TrafficConeFlow {
    uint64_t setup_timestamp;             // 设置时间戳，单位:毫秒
    uint64_t detection_timestamp;         // 检测时间戳，单位:毫秒
    uint32_t flow_id;                     // 锥桶流ID
    TrafficConeFlowType flow_type;        // 锥桶流类型
    uint8_t confidence;                   // 置信度, 0-100
    Polyline2d flow_boundary;            // 锥桶流边界点序列
    uint8_t cone_count;                   // 锥桶数量
};

// ==================== 路沿相关 ====================

// 路沿结构体
struct Curb {
    uint64_t detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t curb_id;                    // 路沿ID
    uint8_t confidence;                  // 置信度, 0-100
    Polyline2d points;                   // 路沿点序列
    float height;                       // 路沿高度，单位:米
    bool is_passable;                    // 是否可通过
};

// ==================== 人行横道相关 ====================

// 人行横道结构体
struct Crosswalk {
    uint64_t detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t crosswalk_id;               // 人行横道ID
    uint8_t confidence;                  // 置信度, 0-100
    std::vector<Point2d> corners;        // 人行横道四个角点加中心点，boot坐标系,从左上角逆时针方向排列，最后是中心点
    float width;                        // 人行横道宽度，单位:米
    float length;                       // 人行横道长度，单位:米
    float distance;                     // 距离自车的距离，单位:米
};

// ==================== 停车位相关 ====================

// 停车位结构体
struct ParkingSpace {
    uint64_t detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t parking_id;                 // 停车位ID
    uint8_t confidence;                  // 置信度, 0-100
    std::vector<Point2d> corners;        // 停车位四个角点加中心点，boot坐标系,从左上角逆时针方向排列，最后是中心点
    float width;                        // 停车位宽度，单位:米
    float length;                       // 停车位长度，单位:米
    bool is_occupied;                    // 是否被占用
    bool is_available;                   // 是否可用
    uint8_t parking_type;                // 停车位类型，0-未知，1-普通，2-残疾人，3-充电桩
};

// ==================== 收费站相关 ====================

// 收费通道类型枚举
enum class TollLaneType : uint8_t {
    TOLL_LANE_UNKNOWN = 0,               // 未知通道
    TOLL_LANE_MANUAL = 1,                // 人工通道
    TOLL_LANE_ETC = 2,                   // ETC通道
    TOLL_LANE_FAST_PASS = 3,             // 快速通道
    TOLL_LANE_WEIGHT = 4,                // 计重通道
    TOLL_LANE_EMERGENCY = 5,             // 紧急通道
    TOLL_LANE_VIP = 6,                   // VIP通道
    TOLL_LANE_TRUCK = 7,                 // 货车通道
    TOLL_LANE_CAR = 8                    // 小车通道
};

// 收费通道状态枚举
enum class TollLaneState : uint8_t {
    TOLL_LANE_STATE_UNKNOWN = 0,         // 未知状态
    TOLL_LANE_STATE_OPEN = 1,            // 开放
    TOLL_LANE_STATE_CLOSED = 2,          // 关闭
    TOLL_LANE_STATE_BUSY = 3,            // 繁忙
    TOLL_LANE_STATE_MAINTENANCE = 4,     // 维护中
    TOLL_LANE_STATE_EMERGENCY = 5        // 紧急状态
};

// 收费通道结构体
struct TollLane {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t toll_lane_id;               // 收费通道ID
    TollLaneType lane_type;              // 通道类型
    TollLaneState lane_state;            // 通道状态
    uint8_t confidence;                  // 置信度, 0-100
    std::vector<Point2d> left_laneline_points;  // 通道左侧边界点序列,数量为0则表示没有左侧边界
    std::vector<Point2d> right_laneline_points; // 通道右侧边界点序列,数量为0则表示没有右侧边界
    std::vector<Point2d> left_curb_points;  // 通道左侧路沿点序列,数量为0则表示没有左侧路沿
    std::vector<Point2d> right_curb_points; // 通道右侧路沿点序列,数量为0则表示没有右侧路沿
    float lane_width;                   // 通道宽度，单位:米
    float lane_length;                  // 通道长度，单位:米
    bool has_barrier;                    // 是否有闸机抬杆
    bool barrier_is_opened;                // 抬杆是否开放,具体的抬杆状态需要与感知同学确认
    float distance_to_barrier;          // 自车距离护栏，单位:米
    bool is_valid;                       // 是否有效
};

// 收费站结构体
struct TollStation {
    uint64_t detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t toll_station_id;            // 收费站ID
    uint8_t confidence;                  // 置信度, 0-100
    std::vector<Point2d> corners;        // 收费站四个角点加中心点，boot坐标系,从左上角逆时针方向排列，最后是中心点
    float station_width;                // 收费站宽度，单位:米
    float station_length;               // 收费站长度，单位:米
    float station_heading;              // 收费站朝向，单位:弧度，[-pi,pi]    
    std::vector<TollLane> toll_lanes; // 收费通道列表
    uint8_t total_lanes;                 // 总通道数
    uint8_t open_lanes;                  // 开放通道数
    float distance;              // 距离自车的距离，单位:米
    bool is_valid;                       // 是否有效
};

// ==================== 交通灯相关 ====================

// 交通灯状态枚举
enum class TrafficLightState : uint8_t {
    LIGHT_UNKNOWN = 0,     // 未知状态
    LIGHT_RED = 1,         // 红灯
    LIGHT_YELLOW = 2,      // 黄灯
    LIGHT_GREEN = 3,       // 绿灯
    LIGHT_BLACK = 4        // 熄灭
};

// 交通灯组件类型枚举
enum class TrafficLightComponentType : uint8_t {
    COMPONENT_CIRCLE = 0,          // 圆饼
    COMPONENT_ARROW_LEFT = 1,      // 左转
    COMPONENT_ARROW_RIGHT = 2,     // 右转
    COMPONENT_ARROW_STRAIGHT = 3,  // 直行
    COMPONENT_ARROW_U_TURN = 4,    // 掉头
};

// 交通灯组件结构体
struct TrafficLightComponent {
    double detection_timestamp;        // 检测时间戳，单位:秒
    double state_change_timestamp;     // 状态变化时间戳，单位:秒
    uint32_t component_id;                // 组件ID
    TrafficLightComponentType component_type; // 组件类型
    TrafficLightState current_state;     // 当前状态
    uint8_t remaining_time;              // 剩余时间，单位:秒（倒计时组件）
    bool is_blinking;                     // 是否闪烁
};

// 交通灯结构体
struct TrafficLight {
    double detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t light_id;                   // 交通灯ID
    std::vector<TrafficLightComponent> traffic_light_components; // 交通灯组件列表
    float distance;                     // 距离自车的距离，单位:米
    bool is_working;                     // 是否正常工作
};

// ==================== 减速带相关 ====================

// 减速带类型枚举
enum class SpeedBumpType : uint8_t {
    SPEED_BUMP_UNKNOWN = 0,        // 未知减速带
    SPEED_BUMP_STANDARD = 1,       // 标准减速带
    SPEED_BUMP_HIGH = 2,           // 高减速带

};

// 减速带结构体
struct SpeedBump {
    uint64_t detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t speed_bump_id;              // 减速带ID
    SpeedBumpType speed_bump_type;       // 减速带类型
    uint8_t confidence;                  // 置信度, 0-100
    Polygon2d points;                   // 减速带边界点序列，boot坐标系
    float width;                        // 减速带宽度，单位:米
    float height;                       // 减速带高度，单位:米
    float length;                       // 减速带长度，单位:米
    float distance;                     // 距离自车的距离，单位:米
    bool is_valid;                       // 是否有效
};

struct KeepClearArea {
    uint64_t detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t keep_clear_area_id;         // 禁停区ID
    uint8_t confidence;                  // 置信度, 0-100
    Polygon2d points;                    // 禁停区边界点序列，boot坐标系
    float width;                        // 禁停区宽度，单位:米
    float length;                       // 禁停区长度，单位:米
    float distance;                     // 距离自车的距离，单位:米
    bool is_valid;                       // 是否有效
};

// ==================== 停止线相关 ====================

// 停止线结构体
struct StopLine {
    uint64_t detection_timestamp;        // 检测时间戳，单位:毫秒
    uint32_t stop_line_id;               // 停止线ID
    uint8_t confidence;                  // 置信度, 0-100
    Polyline2d points;         // 停止线点序列，boot坐标系
    float width;                        // 停止线宽度，单位:米
    float distance;                     // 距离自车的距离，单位:米
    bool is_valid;                       // 是否有效
    bool has_traffic_light;              // 是否有关联的交通灯
    uint32_t associated_traffic_light_id; // 关联的交通灯ID
    uint8_t stop_line_type;              // 停止线类型，0-未知，1-普通停止线，2-让行线，3-检查线
};

// ==================== 待行区相关 ====================

// 待行区车道类型枚举
enum class WaitingAreaType : uint8_t {
    WAITING_AREA_UNKNOWN = 0,           // 未知类型
    WAITING_AREA_LEFT_TURN = 1,         // 左转待行区
    WAITING_AREA_RIGHT_TURN = 2,        // 右转待行区
    WAITING_AREA_STRAIGHT = 3,          // 直行待行区
    WAITING_AREA_U_TURN = 4,            // U型转弯待行区
    WAITING_AREA_MIXED = 5              // 混合待行区
};

// 路口内待行区车道结构体
struct WaitingAreaLane {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t waiting_area_id;            // 待行区ID
    WaitingAreaType area_type;           // 待行区类型
    uint8_t confidence;                  // 置信度, 0-100
    uint32_t left_laneline_id; // 待行区左侧边界点序列,为0则表示没有左侧边界
    uint32_t right_laneline_id; // 待行区右侧边界点序列,为0则表示没有右侧边界
    std::vector<Point2d> center_laneline_points; // 待行区中心线点序列,数量为0则表示没有中心线
    uint32_t associated_traffic_light_id; // 关联的交通灯ID，如果为0，则表示没有关联交通灯
    uint32_t associated_stop_line_id;    // 关联的停止线ID，如果为0，则表示没有关联停止线
    std::string direction_indicator;     // 方向指示（如"左转"、"直行"等）
    bool is_valid;                       // 是否有效
};

// ==================== 路口相关 ====================

// 路口类型枚举
enum class JunctionType : uint8_t {
    JUNCTION_UNKNOWN = 0,            // 未知路口
    JUNCTION_T_INTERSECTION = 1,     // T型路口
    JUNCTION_CROSS_INTERSECTION = 2, // 十字路口
    JUNCTION_Y_INTERSECTION = 3,     // Y型路口
    JUNCTION_ROUNDABOUT = 4,         // 环岛路口
    JUNCTION_MULTI_LEG = 5,          // 多岔路口
    JUNCTION_MERGE = 6,              // 合流路口
    JUNCTION_DIVERGE = 7,            // 分流路口
    JUNCTION_COMPLEX = 8             // 复杂路口
};

// 路口连接类型枚举
enum class JunctionConnectionType : uint8_t {
    CONNECTION_UNKNOWN = 0,          // 未知连接
    CONNECTION_INCOMING = 1,         // 进入路口
    CONNECTION_OUTGOING = 2,         // 离开路口
    CONNECTION_INTERNAL = 3,         // 路口内部
    CONNECTION_CROSSING = 4          // 穿越路口
};

// 路口连接结构体
struct JunctionConnection {
    double detection_timestamp;        // 检测时间戳，单位
    uint32_t connection_id;              // 连接ID
    JunctionConnectionType connection_type; // 连接类型
    uint8_t confidence;                  // 置信度, 0-100
    std::vector<Point2d> center_line;    // 连接中心线点序列
    double connection_width;             // 连接宽度，单位:米
    std::vector<uint32_t> left_line_ids; // 左侧车道线ID列表
    std::vector<uint32_t> right_line_ids; // 右侧车道线ID列表
    std::vector<uint32_t> arrow_ids;     // 车道内箭头ID列表
    Point2d entry_point;                 // 入口点，boot坐标系
    Point2d exit_point;                  // 出口点，boot坐标系
    float entry_heading;                // 入口朝向，单位:弧度，[-pi,pi]
    float exit_heading;                 // 出口朝向，单位:弧度，[-pi,pi]
    uint32_t incoming_lane_id;           // 关联的进入车道ID
    uint32_t outgoing_lane_id;           // 关联的离开车道ID
    bool has_traffic_light;              // 是否有交通信号灯控制
    uint32_t associated_traffic_light_id; // 关联的交通灯ID
    bool has_stop_line;                  // 是否有停止线
    uint32_t associated_stop_line_id;    // 关联的停止线ID
    bool is_valid;                       // 是否有效
};

// 路口结构体
struct Junction {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t junction_id;                // 路口ID
    JunctionType junction_type;          // 路口类型
    uint8_t confidence;                  // 置信度, 0-100
    Point2d center_position;             // 路口中心位置，boot坐标系
    float junction_length;              // 路口长度，单位:米
    float junction_width;               // 路口宽度，单位:米
    float junction_heading;             // 路口朝向，单位:弧度，[-pi,pi]
    float distance;              // 路口中心距离自车的距离，单位:米
    std::vector<JunctionConnection> junction_connections; // 路口连接列表
    std::vector<uint32_t> traffic_light_ids;    // 路口内交通灯ID列表
    std::vector<uint32_t> traffic_sign_ids;     // 路口内交通标志ID列表
    std::vector<uint32_t> stop_line_ids;        // 路口内停止线ID列表
    std::vector<uint32_t> crosswalk_ids;        // 路口内人行横道ID列表
    std::vector<WaitingAreaLane> waiting_area_lanes; // 路口内待行区车道列表
    std::vector<uint32_t> laneline_ids; // 路口内导流线ID列表
    std::vector<uint32_t> curb_ids;            // 路口内路沿ID列表
    std::vector<uint32_t> occupancy_ids;        // 路口内占据网格ID列表
    bool is_valid;                       // 是否有效
};

// 场景列表结构体
struct CameraSceneList {
    // ==================== 消息头 ====================
    Header header;                       // 消息头
    PerceptionHeader meta_header;              // 元数据头
    // ==================== 基础几何元素 ====================
    std::vector<LaneLine> lane_lines;            // 车道线列表
    // ==================== 道路基础设施 ====================
    std::vector<Lane> lanes;                     // 车道列表
    std::vector<Curb> curbs;                     // 路沿列表
    std::vector<StopLine> stop_lines;            // 停止线列表
    std::vector<Crosswalk> crosswalks;           // 人行横道列表
    std::vector<ParkingSpace> parking_spaces;    // 停车位列表
    std::vector<SpeedBump> speed_bumps;          // 减速带列表
    std::vector<KeepClearArea> keep_clear_areas; // 禁停区列表
    // ==================== 路口相关 ====================
    std::vector<Junction> junctions;             // 路口列表  
};

}    // namespace interface
}    // namespace hv

