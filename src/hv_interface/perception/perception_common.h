/**
 * @file perception_common.h
 * @brief 感知头文件定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库通用数据类型的相关功能定义
 */

#pragma once

#include <cstdint> // For int8_t, uint8_t
#include <string>
#include <vector>
#include "../sensor/sensor_common.h"

namespace hv {
namespace interface {

struct PerceptionHeader {
  double global_timestamp; // 自Unix纪元的秒数,单位为秒
  double local_timestamp;  // 本地算法时间戳，单位为秒
  uint32_t frame_sequence;   // 帧序列号
  std::string frame_id;      // 帧ID
  std::vector<SensorHeader> camera_infos;  // 相机帧序列号
  std::vector<SensorHeader> lidar_infos;  // 激光雷达帧序列号
  std::vector<SensorHeader> radar_infos;  // 雷达帧序列号
};

} // namespace interface
} // namespace hv
