/**
 * @file fusion_obstacle.h
 * @brief 感知障碍物相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include "../common/geometry/geometry.h"
#include "../common/geometry/transform.h"
#include "../common/header.h"
#include "perception_common.h"

namespace hv {
namespace interface {

struct VehicleLightStatus {
    uint8_t vehicle_blinker_status; // 车辆转向灯状态,0-未知，1-未亮，2-左转向，3-右转向，4-全亮(双闪)
    uint8_t vehicle_blinker_visible; // 车辆转向灯可见性,0-可见，1-左转向灯不可见，2，右转向灯不可见，3-均不可见
    uint8_t vehicle_brake_light_status; // 车辆刹车灯状态,0-未知，1-未亮，2-亮，3-快闪
    uint8_t vehicle_brake_light_visible; // 车辆刹车灯可见性,0-未知，1-不可见，2-可见
    uint8_t vehicle_corner_lamp_status; // 车辆示廓灯状态,0-未知，1-未亮，2-亮
    uint8_t vehicle_corner_lamp_visible; // 车辆示廓灯可见性,0-未知，1-不可见，2-可见 
};

struct LidarCornerPoint{
    Point2d pos_in_world; // 点坐标，单位:米，boot坐标系
};

struct VehicleDoorOpenStatus {
    uint8_t left_door_open; // 左车门状态,0-未知，1-未开，2-开
    uint8_t right_door_open; // 右车门状态,0-未知，1-未开，2-开
    uint8_t hood_open; // 引擎盖状态,0-未知，1-未开，2-开
    uint8_t trunk_open; // 后备箱状态,0-未知，1-未开，2-开
};

struct VehicleEngineStatus {
    uint8_t engine_status_in_parkinglots; // 引擎状态,0-未知，1-未启动，2-启动
    uint8_t engine_status_out_of_parkinglots; // 引擎状态,0-未知，1-未启动，2-启动
    uint8_t vehicle_engine_at_night; // 引擎状态,0-未知，1-未启动，2-启动
};

// 车辆结构体
struct VehicleContent {
    uint8_t vehicle_classification; // 车辆分类，0-未知，1-小汽车，2-大客车，3-大货车，4-面包车/小型货车,5-汽车拖运板车,6-特殊车辆,7-挂车
    uint8_t number_of_trailers; //挂车数量，可能为大货车挂车，也可能为乘用车拖挂房车等。
    VehicleEngineStatus vehicle_engine_status; // 车辆引擎状态
    VehicleDoorOpenStatus vehicle_door_open_status; // 车辆车门状态
    VehicleLightStatus vehicle_light_status; // 车辆灯光状态
    std::vector<LidarCornerPoint> lidar_corner_points; // 车辆角点，BEV视角,比bounding_box更精确，会包括外后视镜轮毂
    int8_t blocking_ratio; // 遮挡比例，【-0，100】
    int8_t wheel_angle_to_body; // 车轮相对车身角度，单位:度，【-90，90】正值为左转，负值为右转，若无车轮角度，则输出0。
};

enum class PedestrianFacing : uint8_t {
    FACING_UNKNOWN = 0,
    CAN_SEE_NO_EYES = 1,
    CAN_SEE_BOTH_EYES = 2,
    CAN_SEE_RIGHT_EYE = 3,
    CAN_SEE_LEFT_EYE = 4
};

enum class PedestrianIdentity : uint8_t {
    IDENTITY_UNKNOWN = 0,
    IDENTITY_CONSTRUCTION_WORKER = 1,
    IDENTITY_OTHER_WORKER = 2,
    IDENTITY_SCOOTER = 3,
    IDENTITY_POLICE = 4,
    IDENTITY_OTHERS = 5
};

enum class PedestrianHoldSign : uint8_t {
    HOLD_SIGN_UNKNOWN = 0,
    HOLD_SIGN_SLOW = 1,
    HOLD_SIGN_STOP = 2,
    HOLD_SIGN_INACTIVE = 3,
    HOLD_SIGN_NO_SIGN = 4
};

enum class PedestrianPose : uint8_t {
    POSE_UNKNOWN = 0,
    POSE_STANDING = 1,
    POSE_BENDING = 2,
    POSE_SITTING = 3,
    POSE_LYING = 4,
    POSE_WALKING = 5
};

enum class PedestrianAge : uint8_t {
    AGE_UNKNOWN = 0,
    AGE_CHILD = 1,
    AGE_ADULT = 2,
};

// 行人结构体
struct PedestrianContent {    
    PedestrianAge age; // 行人年龄
    PedestrianPose pose; // 行人姿态
    PedestrianFacing facing; // 行人朝向
    PedestrianIdentity identity; // 行人身份
    PedestrianHoldSign hold_sign; // 行人静止标志
    uint8_t covering_body_part; // 行人遮挡部位，bitmask,0-未遮挡，1-遮挡。0bit-头，1bit-上身，2bit-下身，3bit-脚
    bool is_with_attachment; // 行人携带物品,比如手推车或者带着伞或带着麻袋
    bool is_crowed; // 是否拥挤人群
    std::vector<Polygon2d> bev_bounding_box; // 如果是拥挤人群或带着附件物品，则输入2D折线，BEV视角，否则为空，boot坐标系
};

struct BicycleLightStatus{
    uint8_t bicycle_blinker_status; // 车辆转向灯状态,0-未知，1-未亮，2-左转向，3-右转向，4-全亮(双闪)
    uint8_t bicycle_brake_light_status; // 车辆刹车灯状态,0-未知，1-未亮，2-亮，3-快闪
    uint8_t bicycle_corner_lamp_status; // 车辆示廓灯状态,0-未知，1-未亮，2-亮
};

// 自行车结构体
struct BicycleContent {
    uint8_t bicycle_classification; // 自行车等两轮或3轮车分来，0-未知，1-两轮车自动车，2-两轮车摩托车（包括电动车），3-三轮车，4-侉子三轮车
    bool is_bicycle_with_driver; // 是否有驾驶员
    PedestrianFacing driverfacing; // 驾驶员朝向
    BicycleLightStatus bicycle_light_status; // 自行车灯光状态
    std::vector<LidarCornerPoint> lidar_corner_points; // 车辆角点，BEV视角,比bounding_box更精确，会包括外后视镜轮毂，
                                                       // 如果门是开的，则包括开门范围
    int8_t wheel_angle_to_body; // 车轮相对车身角度，单位:度，【-90，90】正值为左转，负值为右转，若无车轮角度，则输出0。   
};

// 动物结构体
struct AnimalContent {
    uint8_t animal_classification; // 动物分类，0-未知，1-狗，2-猫，3-牛，4-羊，5-马，6-猪，7-鸡，8-鸭，9-鹅，10-其他
    uint8_t animal_behavior; // 动物行为，0-未知，1-运动，2-静止，3-其他
    uint8_t animal_size; // 动物大小，0-未知，1-小，2-中，3-大
};

struct UnknownContent {
    uint8_t unknown_behavior; // 未知行为，0-未知，1-运动，2-静止，3-其他
    bool allow_run_over; // 是否允许碾压通过，0-否，1-是
    Polygon2d bev_polygon; // 未知障碍物边缘，BEV坐标系
};

// 静态障碍物类型枚举
enum class StaticObstacleType : uint8_t {
    OBSTACLE_UNKNOWN = 0,        // 未知障碍物
    OBSTACLE_TREE = 1,           // 树木
    OBSTACLE_POLE = 2,           // 电线杆
    OBSTACLE_BARRIER = 3,        // 护栏
    OBSTACLE_WALL = 4,           // 墙壁
    OBSTACLE_BUILDING = 5,       // 建筑物
    OBSTACLE_CONSTRUCTION = 6,   // 施工设施
    OBSTACLE_DEBRIS = 7,         // 杂物
    OBSTACLE_OTHER = 8,          // 其他
    OBSTACLE_SAFETY_BARRIER = 9  // 安全屏障
};

// 静态障碍物结构体
struct StaticObstacleContent {
    uint32_t obstacle_id;                // 障碍物ID
    StaticObstacleType obstacle_type;    // 障碍物类型
    uint8_t confidence;                  // 置信度, 0-100
    Point2d position;                    // 障碍物位置，boot坐标系
    float length;                       // 长度，单位:米
    float width;                        // 宽度，单位:米
    float height;                       // 高度，单位:米
    float heading;                      // 朝向，单位:弧度，[-pi,pi]
    bool is_occluded;                    // 是否被遮挡
    uint8_t occlusion_ratio;             // 遮挡比例，0-100
};

// ==================== 锥桶相关 ====================

// 锥桶类型枚举
enum class TrafficConeType : uint8_t {
    CONE_UNKNOWN = 0,           // 未知锥桶
    CONE_STANDARD = 1,          // 标准锥桶
    CONE_HIGH_VISIBILITY = 2,   // 高可见性锥桶
    CONE_WITH_LIGHT = 3,        // 带灯的锥桶
    CONE_WITH_REFLECTOR = 4,    // 带反光条的锥桶
    CONE_MINI = 5,              // 迷你锥桶
    CONE_HEAVY_DUTY = 6         // 重型锥桶
};

// 锥桶结构体
struct TrafficConeContent {
    uint32_t cone_id;                    // 锥桶ID
    TrafficConeType cone_type;           // 锥桶类型
    uint8_t confidence;                  // 置信度, 0-100
    Point2d position;                    // 锥桶位置，boot坐标系
    float width;                        // 锥桶宽度，单位:米
    float height;                       // 锥桶高度，单位:米
    bool is_fallen;                      // 是否倒下
    bool is_loaded;                      // 是否被装载
    float distance;                     // 距离自车的距离，单位:米
};

struct TypeHistory {
    uint32_t frame_sequence;
    uint8_t type;
};

// 基础障碍物结构体
struct Obstacle {
    uint32_t track_id;
    uint8_t track_status; //0-新出现目标,1-参数收敛中,3-正常跟踪中,4-预测推理中
    uint32_t track_age; // 跟踪帧数
    double track_time; // 跟踪时间,单位:秒
    uint8_t sensor_fusion_source; // 传感器融合源,0-C,1-CL,3-CR,4-CLR,5-L,6-LR,7-R
    std::vector<TypeHistory> type_history; // 类型历史,若存在类型跳变，则保存跳变帧数及跳变类型
    uint8_t confidence; // 置信度,0-100，可用于控制需要大于等于80，可用于报警需要大于等于60
    Point3d position; // 中心点位置坐标，VCS坐标系
    Vector3d velocity; // 速度，VCS坐标系，单位:m/s
    Vector3d acceleration; // 加速度，VCS坐标系，单位:m/s^2
    float abs_speed; // 绝对标量速度，单位:m/s
    float abs_acceleration; // 绝对标量加速度，单位:m/s^2
    float heading_to_ego; // 对自车航向角，单位:弧度，[-pi,pi]
    float heading_rate; // 航向角变化率，单位:弧度/秒
    Point3d position_boot; // 几何中心点位置坐标，boot坐标系，单位:米
    Vector3d position_boot_stddev; // 几何中心点位置坐标标准差，boot坐标系，单位:米
    Vector3d velocity_boot; // 几何中心点速度，boot坐标系，单位:m/s
    Vector3d velocity_boot_stddev; // 几何中心点速度标准差，boot坐标系，单位:m/s
    Vector3d acceleration_boot; // 几何中心点加速度，boot坐标系，单位:m/s^2
    Vector3d acceleration_boot_stddev; // 几何中心点加速度标准差，boot坐标系，单位:m/s^2
    float yaw; // boot航向角，单位:弧度，[-pi,pi]
    float yaw_stddev; // boot航向角标准差，单位:弧度，[-pi,pi]
    float yaw_rate; // boot航向角变化率，单位:弧度/秒
    float yaw_rate_stddev; // boot航向角变化率标准差，单位:弧度/秒
    float length; // 长度，单位:米
    float width; // 宽度，单位:米
    float height; // 高度，单位:米
    uint8_t motion_status; //运动状态，0-未见输出,1-运动,2-曾经运动现在停止，3-静止，4-缓慢运动(同运动区分)
    VehicleContent vehicle_content;
    PedestrianContent pedestrian_content;
    BicycleContent bicycle_content;
    AnimalContent animal_content;
    UnknownContent unknown_content;
    StaticObstacleContent static_obstacle_content;
    TrafficConeContent traffic_cone_content;
};


// 障碍物列表结构体
struct ObstacleList {
    Header header;
    PerceptionHeader meta_header;
    std::vector<Obstacle> obstacles;
};

}    // namespace interface
}    // namespace hv
