/**
 * @file perception_obstacle.h
 * @brief 感知障碍物相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include "../common/header.h"
#include "perception_common.h"


namespace hv {
namespace interface {

struct PerceptionBox3D {
    // Data members
    float center[3] = {0.0f, 0.0f, 0.0f};        // Center point
    float dimensions[3] = {0.0f, 0.0f, 0.0f};    // [l, w, h]
    float rotation[3] = {0.0f, 0.0f, 0.0f};      // [roll, pitch, yaw] in radians
};

enum class PerceptionDetLabel : uint8_t {
    kCAR = 0,
    kBUS = 1,
    kTRUCK = 2,
    kADULT = 3,
    kCHILD = 4,
    kBIKE = 5,
    kBIKE_WITH_PERSON = 6,
    kMOTO = 7,
    kMOTO_WITH_PERSON = 8,
    kTRICYCLE = 9,
    kTRICYCLE_WITH_PERSON = 10,
    kCART = 11,
    kCART_WITH_PERSON = 12,
    kCONE = 13,
    kPOST = 14,
    kWARNING = 15,
    kWATER_BARRIER = 16,
    kCYCLIST_HEAP = 17,
    kTRIANGLE = 18,
    kCLUSTER_OBSTACLE = 99,
    kUNKNOWN = 100,
};


enum class PerceptionSensorId : uint8_t {
    kUNKNOWN = 0,
    kFORWARD_WIDE = 1,
    kFORWARD_MID = 2,
    kFORWARD_FAR = 3,
    kFRONT_LEFT = 4,
    kFRONT_RIGHT = 5,
    kBACK_LEFT = 6,
    kBACK_RIGHT = 7,
    kBACK = 8,
    kLIDAR = 9,
};

enum class PerceptionSource: uint8_t {
    kDNN = 0,
    kCLUSTER = 1,
    kLIDAR = 2,
    kFRONT_CAMERA = 3,
    kREAR_CAMERA = 4,
    kLEFT_CAMERA = 5,
    kRIGHT_CAMERA = 6,
    kALL_CAMERAS = 7,
    kLCFUSION = 8,
    kFRONT_RADAR = 9,
    kUNKNOWN = 100,
};

struct PerceptionObject3D {
    PerceptionBox3D bbox;                                        // 3D bounding box
    float score{0.0f};                                  // Confidence score
    PerceptionDetLabel label{PerceptionDetLabel::kUNKNOWN};               // Detection label
    PerceptionSensorId sensor_id{PerceptionSensorId::kUNKNOWN};            // Sensor ID that detected this object
    float velocity[3] = {0.0f, 0.0f, 0.0f};      // Velocity vector in world coordinates
    PerceptionSource source{PerceptionSource::kUNKNOWN};                 // Source of the detection
    float occrate{0.0f};                               // Percentage of the object that is occluded
    std::vector<float> polygon;                    // 3D polygon points for projection
    int track_id = -1;                               // Track ID
    float yawrate = 0.0f;                           // Yaw rate
    double tracking_time = 0.0;
    double latest_tracked_time = 0.0;
    double timestamp = 0.0;                          // Timestamp
    float confidence = 1.0f;                          // Track confidence
    float acceleration[3] = {0.0f, 0.0f, 0.0f};
    //Dr系下的坐标
    PerceptionBox3D bboxdr;                                        // World 3D bounding box
    float velocitydr[3] = {0.0f, 0.0f, 0.0f};      //World Velocity vector in world coordinates
    float velocity_uncertainty[9] = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f};            
    std::vector<float> polygondr;                    // World 3D polygon points for projection
    float accelerationdr[3] = {0.0f, 0.0f, 0.0f};
    float acceleration_uncertainty[9] = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f};
};

struct InternalPercepObstacleList {
    Header header;
    PerceptionHeader meta_header;
    std::vector<PerceptionObject3D> obstacles;
};

}    // namespace interface
}    // namespace hv

