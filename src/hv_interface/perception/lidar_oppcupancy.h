/**
 * @file lidar_oppcupancy.h
 * @brief 激光雷达占据网格相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include "../common/geometry/geometry.h"
#include "../common/header.h"
#include "perception_common.h"

namespace hv {
namespace interface {
// ==================== 占据网格相关 ====================

// 2D占据网格结构体
struct LidarOccupancyGrid {
    double detection_timestamp;        // 检测时间戳，单位:秒
    uint32_t grid_id;                    // 网格ID
    uint8_t confidence;                  // 置信度, 0-100
    uint16_t grid_row_num;                // 网格行数
    uint16_t grid_col_num;                // 网格列数
    std::vector<uint8_t> grid_packed_data; // 2D占据网格数据
    float grid_resolution;              // 网格分辨率，单位:米/像素
    Point2d grid_origin;                 // 网格原点，boot坐标系
    float grid_width;                   // 网格宽度，单位:米
    float grid_height;                  // 网格高度，单位:米
    bool is_valid;                       // 是否有效
};

struct LidarOccupancyGridList { 
    Header header;
    PerceptionHeader meta_header;
    std::vector<LidarOccupancyGrid> occupancy_grids; // 2D占据网格列表,需要与规控同学确认
};

}    // namespace interface
}    // namespace hv
