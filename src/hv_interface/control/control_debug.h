#pragma once
#include <vector>
#include <cstdint>
#include "../common/header.h"

namespace hv {
namespace interface {
struct ControlDebug {
    Header header;
	double lat_error;
	double lon_error;
	double speed_error;
	double head_error_deg;
	double steer_frontfeed;
	double steer_feedback;
	double steer_item_lat;
	double steer_item_head;
	double steer_item_head_rate;
	double steer_item_total;
	double steer_angle_cmd;
	double steer_angle_real;
	int state_received_loc;
	int state_received_traj;
	int state_received_chassis;
	int state_send_control;
	double curvature;
	double vehicle_yawrate;
	double loc_current_x;
	double loc_current_y;
	double loc_matched_x;
	double loc_matched_y;
	double equal_k1;  // LQR等效最优状态反馈系数
	double equal_k2;
	double equal_k3;
	double longitude_target_speed;
	double longitude_acc_req; // 纵向控制器计算结果
	double frontfeed_normal;
	double frontfeed_kv;
	double frontfeed_e2ss;
	double preview_acceleration_reference;
	double acceleration_cmd_closeloop;
	std::vector<int> reserved0; // 预留字段0
	int reserved0_size;  // 预留100位
	std::vector<double> reserved1; // 预留字段1
	int reserved1_size;  // 预留100位
}; 

} // namespace interface
} // namespace hv
