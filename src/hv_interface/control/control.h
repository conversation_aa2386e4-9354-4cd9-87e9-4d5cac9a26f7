#pragma once
#include <string>
#include <vector>
#include <cstdint>
#include "../common/header.h"

namespace hv {
namespace interface {

struct ControlHeader {
    double global_timestamp;
    double local_timestamp;
    uint32_t frame_sequence;
    uint32_t version;
};

struct ChassisControl {
    bool longitudinal_control_enabled;
    double target_longitudinal_acceleration; // 目标纵向加速度,m/s^2
    bool lateral_control_enabled;
    double target_steering_angle; // 目标转向角,deg
    uint8_t target_gear_position; // 目标档位,0:N,1:D,2:R,3:P
};

struct BodyControl {
    bool open_turn_left_light; // 打开左转向灯,0:关闭,1:打开
    bool open_turn_right_light; // 打开右转向灯,0:关闭,1:打开
    bool open_hazard_light; // 打开双闪,0:关闭,1:打开
};

struct Control {
    Header header;
    ControlHeader control_header;
    ChassisControl chassis_control;
    BodyControl body_control;
};

} // namespace interface
} // namespace hv
