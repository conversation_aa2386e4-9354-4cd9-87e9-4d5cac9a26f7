/**
 * @file routing.h
 * @brief 路由规划相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的路由规划功能定义
 */

#pragma once
#include <string>
#include <vector>
#include "../common/geometry/geometry.h"
#include "../common/header.h"

namespace hv {
namespace interface {

struct RoutingHeader {
    double global_timestamp;
    double local_timestamp;
    uint32_t frame_sequence;
    uint32_t version;
    std::string frame_id;
};
// 车道路径点
struct RoutingLaneWaypoint {
    std::string id;                    // 车道ID
    double s;                          // 沿车道的纵向距离
    Pointenu pose;              // 位置坐标
    double heading;                    // 朝向角度
};

// 车道段
struct RoutingLaneSegment {
    std::string id;                    // 车道ID
    double start_s;                    // 起始纵向距离
    double end_s;                      // 结束纵向距离
};

// 死胡同路由类型
enum class RoutingDeadEndRoutingType: uint8_t {
    ROUTING_OTHER = 0,
    ROUTING_IN = 1,
    ROUTING_OUT = 2
};

// 测量信息
struct RoutingMeasurement {
    double distance;                   // 距离，单位米
};

// 变道类型
enum class RoutingChangeLaneType: uint8_t {
    FORWARD = 0,
    LEFT = 1,
    RIGHT = 2
};

// 通道
struct RoutingPassage {
    std::vector<RoutingLaneSegment> segments;  // 车道段
    bool can_exit;                              // 是否可以退出
    RoutingChangeLaneType change_lane_type;    // 变道类型
};

// 道路段
struct RoutingRoadSegment {
    std::string id;                    // 道路ID
    std::vector<RoutingPassage> passages;  // 通道列表
};

// 路由请求
struct RoutingRequest {
    Header header;
    RoutingHeader routing_header;
    // 至少需要两个点，第一个是起点，最后一个是终点
    // 路由必须经过waypoint中的每个点
    std::vector<RoutingLaneWaypoint> waypoints;  // 路径点
    std::vector<RoutingLaneSegment> blacklisted_lanes;  // 黑名单车道
    std::vector<std::string> blacklisted_roads;  // 黑名单道路
    bool broadcast;                     // 是否广播
    // 如果起始姿态设置为waypoint的第一个点
    bool is_start_pose_set;            // 是否设置起始姿态
};

// 路由响应
struct RoutingResponse {
    Header header;
    RoutingHeader routing_header;
    std::vector<RoutingRoadSegment> roads;  // 道路段
    RoutingMeasurement measurement;         // 测量信息
    RoutingRequest routing_request;         // 路由请求
    std::vector<uint8_t> map_version;       // 用于构建道路图的地图版本
    // 状态信息
    bool status_ok;                         // 状态是否正常
    std::string status_message;             // 状态消息
};

} // namespace interface
} // namespace hv

