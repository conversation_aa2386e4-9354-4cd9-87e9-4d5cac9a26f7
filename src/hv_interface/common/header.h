/**
 * @file header.h
 * @brief 通用头文件定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */


#pragma once

#include <cstdint> // For int8_t, uint8_t
#include <string>

namespace hv {
namespace interface {
struct McuHeader {
  double global_timestamp; // 自Unix纪元的毫秒数
  double local_timestamp;  // 本地算法时间戳，毫秒
  uint8_t rolling_counter;
  uint8_t checksum;
};

struct Header {
  double global_timestamp; // 自Unix纪元的秒数,单位为秒
  double local_timestamp;  // 本地算法时间戳，单位为秒
  uint32_t frame_sequence;   // 帧序列号
  std::string frame_id;      // 帧ID
};

} // namespace interface
} // namespace hv
