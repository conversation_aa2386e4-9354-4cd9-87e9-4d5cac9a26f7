/**
 * @file geometry.h
 * @brief 几何相关类型定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */


#pragma once

#include <cstdint>
#include <limits>
#include <vector>

namespace hv {
namespace interface {

// 枚举类型：单调性类型
enum class MonotonicType : int { GENERAL = 0, INCREASING = 1, DECREASING = 2 };

// 二维点（双精度）
struct Point2d {
  double x = 0.0;
  double y = 0.0;
};

// 二维点（单精度）
struct Point2f {
  float x = 0.0f;
  float y = 0.0f;
};

// 二维点（32位整数）
struct Point2i {
  int32_t x = 0;
  int32_t y = 0;
};

// 二维向量（双精度）
struct Vector2d {
  double x = 0.0;
  double y = 0.0;
};

// 三维点（双精度）
struct Point3d {
  double x = 0.0;
  double y = 0.0;
  double z = 0.0;
};

// 三维点（单精度）
struct Point3f {
  float x = 0.0f;
  float y = 0.0f;
  float z = 0.0f;
};

// 经纬高坐标（双精度）
struct Pointllh {
  double latitude = 0.0;  // 纬度,单位:°,[-90°,90°]
  double longitude = 0.0; // 经度,单位:°,[-180°,180°]
  double height = 0.0;    // 高度,单位:米,[-10000m,10000m]
};

struct Pointenu{
  double east = 0.0;  // 东向分量,单位:米,[-10000m,10000m]
  double north = 0.0; // 北向分量,单位:米,[-10000m,10000m]
  double up = 0.0;    // 上向分量,单位:米,[-10000m,10000m]
};

// 折线（三维点序列）
struct Polyline {
  std::vector<Point3d> data;
};

struct Polyline2d {
  std::vector<Point2d> data;
};

// 折线（经纬高坐标序列）
struct Polylinellh {
  std::vector<Pointllh> data;
};

struct Polylineenu{
  std::vector<Pointenu> data;
};

// 压缩折线（二进制数据）
struct PolylinePacked {
  std::vector<double> data;
};

// 多边形（三维点序列）
struct Polygon {
  std::vector<Point3d> data;
};

// 多边形（经纬高坐标序列）
struct Polygonllh {
  std::vector<Pointllh> data;
};

struct Polygonenu{
  std::vector<Pointenu> data;
};

// 二维多边形（二维点序列）
struct Polygon2d {
  std::vector<Point2d> data;
};

// 压缩二维多边形（二进制数据）
struct Polygon2dPacked {
  std::vector<double> data;
};

// 三维点压缩（一维数组）
struct Point3dPacked {
  std::vector<double> data;
};

// 二维曲线
struct Curve2d {
  double s0 = 0.0;
  double s_step = 0.0;
  std::vector<Point2d> points;
  Point2d start_tangent;
  Point2d end_tangent;
};

// 有向曲线
struct DirectedCurve2d {
  Curve2d curve;
  bool is_reverse = false;
};

// 可逆曲线集合
struct ReversibleCurve2d {
  std::vector<DirectedCurve2d> data;
};

// 分段曲线
struct PiecewiseCurve2d {
  std::vector<Curve2d> data;
};

// 分段三次函数
struct PiecewiseCubicFunction {
  double x0 = 0.0;
  double x_step = 0.0;
  std::vector<double> y_values;
  bool is_non_decreasing = true;
  MonotonicType monotonic_type = MonotonicType::INCREASING;
};

// 均匀分段线性函数
struct UniformPiecewiseLinearFunction {
  double x0 = 0.0;
  double x_step = 0.0;
  std::vector<double> y_values;
};

// 三维棱柱
struct Prism {
  Polygon down_surface;
  double height = 0.0;
};

// 二维盒子
struct Box2d {
  double center_x = 0.0;
  double center_y = 0.0;
  double length = 0.0;
  double width = 0.0;
  double sin_heading = 0.0;
  double cos_heading = 0.0;
};

// 平面方程
struct Plane {
  Point3d normal;
  double offset = 0.0;
};

// 多边形边界切片
struct PolygonBoundarySlice {
  int32_t start_index = 0;
  int32_t end_index = 0;
};

// 二维线段
struct Segment2d {
  Point2d start;
  Point2d end;
};

// 三维线段
struct Segment3d {
  Point3d start;
  Point3d end;
};

// 二维直线
struct Line2d {
  Point2d normal;
  double offset = 0.0;
};

struct ImageBox3d {
    Point2i front_top_left;
    Point2i front_bottom_right;
    Point2i back_top_left;
    Point2i back_bottom_right;
};

struct ImageBox2d {
    Point2i top_left;
    Point2i bottom_right;
};

} // namespace interface
} // namespace hv
