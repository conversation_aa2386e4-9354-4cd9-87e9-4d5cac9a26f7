/**
 * @file transform.h
 * @brief 坐标变换相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint>

namespace hv {
namespace interface {

// 基础向量类型
struct Vector3d {
  double x = 0.0; // X分量
  double y = 0.0; // Y分量
  double z = 0.0; // Z分量
};

struct Vector3f {
  float x = 0.0f; // X分量
  float y = 0.0f; // Y分量
  float z = 0.0f; // Z分量
};

struct Vector3i {
  int32_t x = 0; // X分量
  int32_t y = 0; // Y分量
  int32_t z = 0; // Z分量
};

struct Vector3denu {
  double east = 0.0;  // 东向分量
  double north = 0.0; // 北向分量
  double up = 0.0;    // 上向分量
};

// 四元数类型（单位四元数默认值）
struct Quaterniond {
  double x = 0.0; // X分量
  double y = 0.0; // Y分量
  double z = 0.0; // Z分量
  double w = 1.0; // W分量（默认单位四元数）
};

struct Quaternionf {
  float x = 0.0f; // X分量
  float y = 0.0f; // Y分量
  float z = 0.0f; // Z分量
  float w = 1.0f; // W分量（默认单位四元数）
};

// 欧拉角类型
struct EulerAnglesd {
  double roll = 0.0;  // 绕X轴旋转（弧度）,单位:弧度,[-π,π]
  double pitch = 0.0; // 绕Y轴旋转（弧度）,单位:弧度,[-π/2,π/2]
  double yaw = 0.0;   // 绕Z轴旋转（弧度）,单位:弧度,[-π,π]
};

struct EulerAnglesf {
  float roll = 0.0f;  // 绕X轴旋转（弧度）
  float pitch = 0.0f; // 绕Y轴旋转（弧度）
  float yaw = 0.0f;   // 绕Z轴旋转（弧度）
};

// 刚体变换类型
struct Rigid3d {
  Vector3d translation; // 平移分量
  Quaterniond rotation; // 旋转四元数
};

struct Rigid3f {
  Vector3f translation; // 平移分量
  Quaternionf rotation; // 旋转四元数
};

// 专用类型别名
using Translation3d = Vector3d; // 平移向量
using Translation3f = Vector3f; // 平移向量（单精度）

using RotationQuaterniond = Quaterniond; // 旋转四元数
using RotationQuaternionf = Quaternionf; // 旋转四元数（单精度）

struct Transform {
  Vector3d translation; // 平移向量
  Quaterniond rotation; // 旋转四元数
};

} // namespace interface
} // namespace hv
