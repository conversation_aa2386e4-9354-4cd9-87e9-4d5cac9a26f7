/**
 * @file matrix.h
 * @brief 矩阵运算相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint> // for int32_t
#include <vector>

namespace hv {
namespace interface {

// 双精度浮点数矩阵
struct Matrixd {
  int32_t rows = 0;             // 矩阵行数
  int32_t cols = 0;             // 矩阵列数
  std::vector<double> elements; // 矩阵元素（按行优先存储，packed=true优化）
};

// 单精度浮点数矩阵
struct Matrixf {
  int32_t rows = 0;            // 矩阵行数
  int32_t cols = 0;            // 矩阵列数
  std::vector<float> elements; // 矩阵元素（按行优先存储，packed=true优化）
};

} // namespace interface
} // namespace hv
