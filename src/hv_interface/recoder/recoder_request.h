/**
 * @file recoder_request.h
 * @brief 记录请求相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库的相关功能定义
 */


#pragma once

#include <cstdint> // For int8_t, uint8_t
#include <string>
#include <vector>
#include "../common/header.h"

namespace hv {
namespace interface {

struct RecoderRequestHeader {
    double trigger_timestamp;
    double start_timestamp;
    double end_timestamp;
};

enum class RecoderRequestSource: uint8_t {
    UNKNOWN = 0,
    DISENGAGE = 1,
    MANUAL = 3,
    FILTER = 4,
    PLACEHOLDER = 5,
};

struct RecoderRequestTag {
    std::string name;   // only A-Z a-z 0-9 - _
    std::string detail; // optional 
};

struct RecoderRequest {
        Header header;
        RecoderRequestHeader recoder_request_header;    
        RecoderRequestSource source;        
        std::string details;        
        std::vector<std::string> exclude_topics;
        std::vector<std::string> include_topics;        
        float record_time_before;
        float record_time_after;       
        std::vector<RecoderRequestTag> tags;   
};

} // namespace interface
} // namespace hv
