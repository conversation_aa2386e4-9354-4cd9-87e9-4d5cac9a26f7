/**
 * @file recoder_common.h
 * @brief 记录器头文件定义
 * <AUTHOR> Interface Team
 * @date 2025
 *
 * 此文件包含了HV Interface库通用数据类型的相关功能定义
 */

#pragma once

#include <cstdint> // For int8_t, uint8_t
#include <string>
#include <vector>

namespace hv {
namespace interface {

struct MoudleVersion{
  std::string moudle_name;
  std::string moudle_version;
};

struct RecoderHeader {
  double global_timestamp; // 自Unix纪元的秒数,单位为秒
  double local_timestamp;  // 本地算法时间戳，单位为秒
  std::string interface_version; // 接口版本
  std::vector<MoudleVersion> moudle_version; // 模块版本
  std::string hardware_version; // 硬件版本
  std::string extrinsic_parameters_version; // 外参文件版本
};

enum class JobStatus : int8_t {
    UNKNOWN = 0,
    PENDING = 1,
    STARTING = 2,
    RECORDING = 3,
    DONE = 4,
    ABORT = -1
};

struct SingleJobStatus{
    double start_time;
    uint32_t triger_index;
    std::string filename;
    JobStatus status;
    std::string details;
};

struct RecoderEvent{
    RecoderHeader header;
    std::vector<SingleJobStatus> list_job_status;
};

} // namespace interface
} // namespace hv
