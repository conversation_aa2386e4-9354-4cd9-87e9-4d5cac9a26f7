/**
 * @file raw_can.h
 * @brief 原始CAN数据定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include <cstdint>    // For int8_t, uint8_t
#include <vector>

namespace hv {
namespace interface {
// 基础数据帧结构体
struct CanFrame {
  // 枚举类型定义（需根据实际业务补充完整）
  enum class CanBusType : uint8_t {
    DBW = 0,
    OBD_POWERTRAIN = 1,
    OBD_CHASSIS = 2,
    OBD_COMFORT = 3,
    RADAR_ARS408 = 4,
    ULTRASOUND_SENSOR = 6,
    TRUCK_PONYCAN = 7,
    IMU_MRCC = 8,
    IPC_MRCC = 9,
    DBW_MRCC = 10,
    MRCC_REC = 11,
    UNDEFINED = 12,
  };
  CanBusType can_bus_type;          // 可选字段
  uint32_t can_id = 0;              // 默认值0
  std::vector<uint8_t> data;        // 字节数据
  double ipc_timestamp = 0.0;       // 时间戳
  double origin_timestamp = 0.0;    // 原始时间戳
  uint32_t flag = 0;                // 标志位
  bool is_fd = false;               // FD模式标志
};

// 原始数据容器结构体
struct CanRawData {
  std::vector<CanFrame> frames;    // CAN帧集合
};
}    // namespace interface
}    // namespace hv
