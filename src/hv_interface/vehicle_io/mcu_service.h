/**
 * @file can_service.h
 * @brief CAN服务相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include "../common/header.h"
#include <cstdint>    // For int8_t, uint8_t
#include <vector>

namespace hv {
namespace interface {

struct DiagnosisSystem {
  // 信号位定义在vehicle_io_diagnosis_bitmask.h中
  uint32_t signal_lost_bit_mask1;     // 信号丢失位掩码1,定义的是这个信号在原始can上丢失了
  uint32_t signal_exist_bit_mask1;    // 信号存在位掩码1,定义的是这个信号在原始can上存在,若为0,则表示这个信号原车就没有
  uint32_t signal_crc_error_bit_mask1;    // 信号crc错误位掩码1,定义的是这个信号在原始can上crc错误
};

struct ClusterSystem {
  McuHeader header;
  uint8_t cluster_vehicle_speed;    // 仪表车速（1字节）unit: km/h, [0 ~ +255]
  DiagnosisSystem diagnosis_system;
};

struct DriverInputSystem {
  McuHeader header;
  uint8_t driver_pressed_turn_light;      // 驾驶员按下转向灯（1字节）0-未按下，1-左转，2-右转
  uint8_t driver_pressed_front_light;     // 驾驶员按下前车灯（1字节）0-未按下，1-近光灯，2-远光灯
  uint8_t driver_pressed_hazard_light;    // 驾驶员按下危险警告灯（1字节）0-未按下，1-按下
  uint8_t driver_pressed_fog_light;       // 驾驶员按下雾灯（1字节）0-未按下，1-雾灯
  uint8_t driver_presser_wiper;           // 驾驶员按下雨刷（1字节）0-未按下，1-雨刷1档，2-雨刷2档，3-雨刷3档，4-自动挡
  uint8_t driver_pressed_washer;          // 驾驶员按下玻璃清洗器（1字节）0-未按下，1-按下
  uint8_t driver_pressed_horn;            // 驾驶员按下喇叭（1字节）0-未按下，1-按下
  uint8_t driver_shift_gear;              // 驾驶员换挡（1字节）0-未换挡，1-D档，2-N档，3-R档，4-P档
  DiagnosisSystem diagnosis_system;
};

struct DriverAdInputSystem {
  McuHeader header;
  uint8_t driver_pressed_ad_run; // 驾驶员按下自动驾驶开始（1字节）0-未按下，1-按下
  uint8_t driver_pressed_ad_stop; // 驾驶员按下自动驾驶结束（1字节）0-未按下，1-按下
  uint8_t driver_pressed_ad_accler; // 驾驶员按下自动驾驶加速（1字节）0-未按下，1-按下
  uint8_t driver_pressed_ad_decel; // 驾驶员按下自动驾驶减速（1字节）0-未按下，1-按下
  uint8_t driver_pressed_ad_hy_up; // 驾驶员按下自动驾驶巡航速度上抬（1字节）0-未按下，1-按下
  uint8_t driver_pressed_ad_hy_down; // 驾驶员按下自动驾驶巡航速度下压（1字节）0-未按下，1-按下
  DiagnosisSystem diagnosis_system;
};

struct LightingSystem {
  McuHeader header;
  uint16_t
      lighting_system_status_bitmask;    // 灯光系统状态（1字节）,bit位, 0- normal, 1- abnormal,bit
                                         // mask,0-前车灯，1-危险警告灯，2-示宽灯，3-倒车灯，4-前雾灯，5-后雾灯，6-刹车灯，7-自动驾驶状态灯，8-转向灯，9-车内顶灯
  uint8_t front_light;                     // 前车灯状态（1字节）0-未开，1-近光灯，2-远光灯
  bool hazard_light;                       // 危险警告灯(双闪)状态（1字节）0-关闭，1-打开
  bool width_light;                        // 示宽灯状态（1字节）0-关闭，1-打开
  bool reverse_light;                      // 倒车灯状态（1字节）0-关闭，1-打开
  bool front_fog_light;                    // 前雾灯状态（1字节）0-关闭，1-打开
  bool rear_fog_light;                     // 后雾灯状态（1字节）0-关闭，1-打开
  bool brake_light;                        // 刹车灯状态（1字节）0-关闭，1-打开
  bool autonomous_driving_status_light;    // 自动驾驶状态灯（1字节）0-自动驾驶未开启，1-自动驾驶开启
  uint8_t turn_light;                      // 转向灯状态（1字节）0:无信号, 1:左转, 2:右转
  bool dome_light;                         // 车内顶灯状态（1字节）0-关闭，1-打开
  DiagnosisSystem diagnosis_system;
};

struct HornSystem {
  McuHeader header;
  uint8_t horn_function_status;    // 喇叭功能状态（1字节）0-normal，1-abnormal,2-fault,3-off
  bool horn_active_status;         // 喇叭激活状态（1字节）0-inactive，1-active
  DiagnosisSystem diagnosis_system;
};

struct AirbagSystem {
  McuHeader header;
  uint8_t airbag_function_status;    // 安全气囊状态（1字节）0-normal，1-abnormal,2-fault,3-off
  bool airbag_active_status;         // 安全气囊激活状态（1字节）0-inactive，1-active
  bool is_crash;                     // 是否发生碰撞（1字节）0-否，1-是
  DiagnosisSystem diagnosis_system;
};

struct DoorSystem {
  McuHeader header;
  uint16_t
      door_system_status_bitmask;    // 车门系统状态（1字节）,bit位, 0- normal, 1- abnormal,bit
                                     // mask,0-主驾车门，1-副驾车门，2-左后车门，3-右后车门，4-前舱盖，5-后备箱盖，6-充电口盖,7-充电枪插入,8-防夹状态
  uint8_t door_type;             // 车门类型（1字节）0:手动门 1:电动门
  bool fl_door_open;             // 主驾车门状态（1字节）0-关闭，1-打开
  bool fr_door_open;             // 副驾车门状态（1字节）0-关闭，1-打开
  bool rl_door_open;             // 左后车门状态（1字节）0-关闭，1-打开
  bool rr_door_open;             // 右后车门状态（1字节）0-关闭，1-打开
  bool fl_door_lock;             // 主驾车门锁状态（1字节）0-锁定，1-解锁
  bool fr_door_lock;             // 副驾车门锁状态（1字节）0-锁定，1-解锁
  bool rl_door_lock;             // 左后车门锁状态（1字节）0-锁定，1-解锁
  bool rr_door_lock;             // 右后车门锁状态（1字节）0-锁定，1-解锁
  uint8_t fl_door_position;      // 主驾车门开度（1字节）0-100%
  uint8_t fr_door_position;      // 副驾车门开度（1字节）0-100%
  uint8_t rl_door_position;      // 左后车门开度（1字节）0-100%
  uint8_t rr_door_position;      // 右后车门开度（1字节）0-100%
  bool hood_open;                // 前舱盖状态（1字节）0-关闭，1-打开
  bool trunk_open;               // 后备箱盖状态（1字节）0-关闭，1-打开
  bool charging_door_open;       // 充电口盖状态（1字节）0-关闭，1-打开
  bool charging_gun_inserted;    // 充电枪插入状态（1字节）0-未插入，1-已插入
  bool anti_pinch_status;        // 防夹状态（1字节）0-未触发，1-触发
  DiagnosisSystem diagnosis_system;
};

struct WindowSystem {
  McuHeader header;
  uint8_t window_system_status_bitmask;    // 车窗功能状态（1字节）,bit位, 0- normal, 1- abnormal,bit
                                           // mask,0-主驾车窗，1-副驾车窗，2-左后车窗，3-右后车窗，4-天窗
  uint8_t fl_window_position;              // 主驾车窗开度（1字节）0-100%,0-关闭，100-完全打开
  uint8_t fr_window_position;              // 副驾车窗开度（1字节）0-100%,0-关闭，100-完全打开
  uint8_t rl_window_position;              // 左后车窗开度（1字节）0-100%,0-关闭，100-完全打开
  uint8_t rr_window_position;              // 右后车窗开度（1字节）0-100%,0-关闭，100-完全打开
  uint8_t sun_roof_position;               // 天窗开度（1字节）0-100%,0-关闭，100-完全打开
  DiagnosisSystem diagnosis_system;
};

struct WiperSystem {
  McuHeader header;
  uint8_t wiper_function_status;    // 雨刷功能状态（1字节）0-关闭，1-间歇，2-慢速，3-快速
  uint8_t washer_function_status;   // 玻璃清洗器功能状态（1字节）0-关闭，1-开启
  DiagnosisSystem diagnosis_system;
};

struct SideMirrorSystem {
  McuHeader header;
  uint8_t side_mirror_function_status;    // 后视镜功能状态（1字节）0-展开，1-折叠
  DiagnosisSystem diagnosis_system;
};

struct SeatOccupcySystem {
  McuHeader header;
  uint8_t seat_occupcy_system_status_bitmask;    // 座椅占用系统状态（1字节),bit 位 0-normal,1-abnormal,2-fault
                                                 // 座椅占用系统状态掩码（1字节),bit 位
                                                 // 0-主驾座椅，1-副驾座椅，2-左后座椅，3-右后座椅，4-中后座椅
  bool fl_seat_occupied;                         // 主驾座椅占用状态（1字节）0-未占位，1-占位
  bool fr_seat_occupied;                         // 副驾座椅占用状态（1字节）0-未占位，1-占位
  bool rl_seat_occupied;                         // 左后座椅占用状态（1字节）0-未占位，1-占位
  bool rr_seat_occupied;                         // 右后座椅占用状态（1字节）0-未占位，1-占位
  bool rm_seat_occupied;                         // 中后座椅占用状态（1字节）0-未占位，1-占位
  DiagnosisSystem diagnosis_system;
};

struct SeatPositionSystem {
  McuHeader header;
  uint8_t
      seat_position_system_status_bitmask;    // 座椅位置系统状态（1字节),bit 位 0-normal,1-abnormal
                                              // bit
                                              // mask:0-主驾座椅位置，1-副驾座椅位置，2-左后座椅位置，3-右后座椅位置，4-中后座椅位置，5-主驾座椅靠背位置
  uint8_t fl_seat_position;        // 主驾座椅位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t fr_seat_position;        // 副驾座椅位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t rl_seat_position;        // 左后座椅位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t rr_seat_position;        // 右后座椅位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t rm_seat_position;        // 中后座椅位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t fl_seatback_position;    // 主驾座椅靠背位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t fr_seatback_position;    // 副驾座椅靠背位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t rl_seatback_position;    // 左后座椅靠背位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t rr_seatback_position;    // 右后座椅靠背位置（1字节）0-100%，0表示最靠前，100表示最靠后
  uint8_t rm_seatback_position;    // 中后座椅靠背位置（1字节）0-100%，0表示最靠前，100表示最靠后
  DiagnosisSystem diagnosis_system;
};

struct SeatHeatingSystem {
  McuHeader header;
  uint8_t seat_heating_system_status;    // 座椅加热系统状态（1字节）,bit位, 0- normal, 1- abnormal,bit
                                         // mask,0-主驾座椅，1-副驾座椅，2-左后座椅，3-右后座椅，4-中后座椅
  uint8_t fl_seat_heating;               // 主驾座椅加热状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t fr_seat_heating;               // 副驾座椅加热状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t rl_seat_heating;               // 左后座椅加热状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t rr_seat_heating;               // 右后座椅加热状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t rm_seat_heating;               // 中后座椅加热状态（1字节）0-关闭，1-1档，2-2档，3-3档
  DiagnosisSystem diagnosis_system;
};

struct SeatCoolingSystem {
  McuHeader header;
  uint8_t seat_cooling_system_status;    // 座椅通风系统状态（1字节）,bit位, 0- normal, 1- abnormal,bit
                                         // mask,0-主驾座椅，1-副驾座椅，2-左后座椅，3-右后座椅，4-中后座椅
  uint8_t fl_seat_cooling;               // 主驾座椅通风状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t fr_seat_cooling;               // 副驾座椅通风状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t rl_seat_cooling;               // 左后座椅通风状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t rr_seat_cooling;               // 右后座椅通风状态（1字节）0-关闭，1-1档，2-2档，3-3档
  uint8_t rm_seat_cooling;               // 中后座椅通风状态（1字节）0-关闭，1-1档，2-2档，3-3档
  DiagnosisSystem diagnosis_system;
};

struct SeatBeltSystem {
  McuHeader header;
  uint8_t seat_belt_system_status;    // 安全带系统状态（1字节）,bit位, 0- normal, 1- abnormal,bit
                                      // mask,0-主驾安全带，1-副驾安全带，2-左后安全带，3-右后安全带，4-中后安全带
  bool fl_belt_attached;              // 主驾安全带（Front Left）0-松开，1-系紧
  bool fr_belt_attached;              // 副驾安全带（Front Right）0-松开，1-系紧
  bool rl_belt_attached;              // 左后安全带（Rear Left）0-松开，1-系紧
  bool rr_belt_attached;              // 右后安全带（Rear Right）0-松开，1-系紧
  bool rm_belt_attached;              // 中后安全带（Rear Center）0-松开，1-系紧
  DiagnosisSystem diagnosis_system;
};

struct AirConditioningSystem {
  McuHeader header;
  uint8_t ac_system_status;        // 空调系统状态（1字节） 0- normal, 1- abnormal,2-此车没有控制接口
  uint8_t ac_function_status;      // 空调状态（1字节）0-关闭，1-打开
  uint8_t ac_mode;                 // 空调模式（1字节）0-制冷，1-制热
  uint8_t ac_temp;                  // 空调温度（1字节） unit: degC, [0 ~ +100]
  uint8_t ac_fanspeed;              // 空调风速（1字节）0-关闭，1-1档，2-2档，3-3档，4-4档，5-5档，6-6档，7-7档，8-8档，9-9档，10-10档
  uint8_t ac_air_circulation_mode;    // 空气循环模式（1字节）0-内循环，1-外循环
  DiagnosisSystem diagnosis_system;
};

struct BatterySystem {
  McuHeader header;
  uint8_t battery_system_status;    // 电池系统状态（1字节）0-normal，1-abnormal
  uint8_t battery_function_status;    // 电池功能状态（1字节）0-放电，1-充电
  uint8_t battery_soc;                  // 电池电量（1字节）0-100
  uint8_t battery_soh;                // 电池健康状态（1字节）0-100
  uint8_t battery_temperature;        // 电池温度（1字节）0-100
  uint8_t battery_cooling_water_temperature;    // 电池冷却水温度（1字节）0-100
  float battery_remaining_mileage;    // 电池剩余里程（4字节）km,[0 ~ +2000]
  uint8_t auxiliary_battery_status;    // 辅助电池状态（1字节）0-normal，1-abnormal
  DiagnosisSystem diagnosis_system;
};

struct TirePressureMonitoringSystem {
  McuHeader header;
  uint8_t
      tire_pressure_monitoring_system_status;    // 轮胎压力监测系统状态（1字节）,bit位, 0- normal, 1- abnormal,bit
                                                 // mask,0-左前轮胎压力，1-右前轮胎压力，2-左后轮胎压力，3-右后轮胎压力
  float fl_tire_pressure_value;                  // 左前轮胎压力值（4字节）kpa, [0 ~ +1000]
  float fr_tire_pressure_value;                  // 右前轮胎压力值（4字节）kpa, [0 ~ +1000]
  float rl_tire_pressure_value;                  // 左后轮胎压力值（4字节）kpa, [0 ~ +1000]
  float rr_tire_pressure_value;                  // 右后轮胎压力值（4字节）kpa, [0 ~ +1000]
  bool fl_tire_low_pressure_warning;             // 左前轮胎压力低警告（1字节）0-否，1-是
  bool fr_tire_low_pressure_warning;             // 右前轮胎压力低警告（1字节）0-否，1-是
  bool rl_tire_low_pressure_warning;             // 左后轮胎压力低警告（1字节）0-否，1-是
  bool rr_tire_low_pressure_warning;             // 右后轮胎压力低警告（1字节）0-否，1-是
  DiagnosisSystem diagnosis_system;
};

struct VehicleInformationSystem {
  McuHeader header;
  char prefix[16];    // 前缀字段（16字节，含终止符）
  char code[32];      // VIN码主体（32字节，含终止符）
  uint8_t vehicle_power_status;    // 车辆电源状态（1字节）0-OFF，1-ACC,2-ON,3-CRANK,4-RUN
  int8_t outside_temperature;    // 车外温度（1字节）unit: degC, [-100 ~ +100]
  float total_mileage;    // 总里程（4字节）unit: km, [0 ~ +很多]
  DiagnosisSystem diagnosis_system;
};

struct MotionSystem {
  McuHeader header;
  float vehicle_speed;                        // 车速（4字节） unit: km/h,[0-400]
  float vehicle_longitudinal_acceleration;    // 纵向加速度（4字节） unit: m/s^2,[-10 ~ +10]
  float vehicle_lateral_acceleration;         // 横向加速度（4字节） unit: m/s^2,[-10 ~ +10]
  float vehicle_yaw_rate;                     // 车偏横摆角速度（4字节） unit: rad/s,[-10 ~ +10]
  DiagnosisSystem diagnosis_system;
};

struct WheelSystem {
  McuHeader header;
  float fl_wheel_speed;    // 前左轮速度（4字节）unit: km/h,[0 ~ +400]
  float fr_wheel_speed;    // 前右轮速度（4字节）unit: km/h,[0 ~ +400]
  float rl_wheel_speed;    // 后左轮速度（4字节）unit: km/h,[0 ~ +400]
  float rr_wheel_speed;    // 后右轮速度（4字节）unit: km/h,[0 ~ +400]

  int8_t fl_wheel_direction;    // 前左轮转向方向（1字节）-1:后退，0:静止，1:前进
  int8_t fr_wheel_direction;    // 前右轮转向方向（1字节）-1:后退，0:静止，1:前进
  int8_t rl_wheel_direction;    // 后左轮转向方向（1字节）-1:后退，0:静止，1:前进
  int8_t rr_wheel_direction;    // 后右轮转向方向（1字节）-1:后退，0:静止，1:前进

  uint32_t fl_wheel_pulse;    // 前左轮脉冲数（1字节）,[0 ~ +很多]
  uint32_t fr_wheel_pulse;    // 前右轮脉冲数（1字节）,[0 ~ +很多]
  uint32_t rl_wheel_pulse;    // 后左轮脉冲数（1字节）,[0 ~ +很多]
  uint32_t rr_wheel_pulse;    // 后右轮脉冲数（1字节）,[0 ~ +很多]
  DiagnosisSystem diagnosis_system;
};

struct XcuSystem {
  McuHeader header;
  uint8_t xcu_function_status;    // XCU功能状态（1字节）0-normal，1-abnormal,2-fault,3-off
  bool xcu_active_status;         // XCU激活状态（1字节）0-inactive，1-active

  // 电机相关
  uint8_t motor_type;                         // 电机类型（1字节）0-后驱电机，1-前驱电机，2-前后驱电机
  float front_motor_rpm;                      // 前电机转速（4字节）unit: rpm,[0 ~ +40000]
  float request_front_motor_torque;           // 前电机需求扭矩,可能来自adas,可能来自esp（4字节）unit: Nm, [0 ~ +5000]
  float actual_front_motor_torque;            // 前电机实际扭矩（4字节）unit: Nm, [0 ~ +5000]
  float actual_front_motor_torque_maximum;    // 前电机实际扭矩允许最大值（4字节）unit: Nm, [0 ~ +5000]
  float actual_front_motor_torque_minimum;    // 前电机实际扭矩允许最小值（4字节）unit: Nm, [0 ~ +5000]

  float rear_motor_rpm;                      // 后电机转速（4字节）unit: rpm,[0 ~ +40000]
  float request_rear_motor_torque;           // 后电机需求扭矩,可能来自adas,可能来自esp（4字节）unit: Nm, [0 ~ +5000]
  float actual_rear_motor_torque;            // 后电机实际扭矩（4字节）unit: Nm, [0 ~ +5000]
  float actual_rear_motor_torque_maximum;    // 后电机实际扭矩允许最大值（4字节）unit: Nm, [0 ~ +5000]
  float actual_rear_motor_torque_minimum;    // 后电机实际扭矩允许最小值（4字节）unit: Nm, [0 ~ +5000]

  // 驱动轴相关
  float front_shaft_torque;       // 前驱动轴扭矩（4字节）unit: Nm, [0 ~ +5000]
  float rear_shaft_torque;        // 后驱动轴扭矩（4字节）unit: Nm, [0 ~ +5000]
  float front_input_shaft_rpm;    // 前轴转速原始值（4字节）unit: rpm, [0 ~ +40000]
  float rear_input_shaft_rpm;     // 后轴转速原始值（4字节）unit: rpm, [0 ~ +40000]

  uint8_t accel_pedal_position;            // 加速踏板开度（1字节）,0-100%
  uint8_t virtual_accel_pedal_position;    // 虚拟加速踏板开度（1字节）,0-100%
  bool is_override;                        // 是否被驾驶员踩下（1字节）0-否，1-是
  uint8_t vehicle_driving_mode;            // 车辆驾驶模式（1字节）0-normal，1-ECO，2-sport，3-snow mode，9-invaild
  DiagnosisSystem diagnosis_system;
};

struct EspSystem {
  McuHeader header;
  uint8_t esp_control_type;       // 控制模式（1字节）0-unknown，1-use_cdd_decel，2-use_brake_torque，3-use_vlc_accel
  uint8_t esp_function_status;    // ESP功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool esp_active_status;         // ESP激活状态，0-inactive，1-active

  uint8_t abs_function_status;    // ABS(防抱死制动系统)功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool abs_active_status;         // ABS(防抱死制动系统)激活状态，0-inactive，1-active

  uint8_t tcs_function_status;    // TCS(牵引力控制系统)功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool tcs_active_status;         // TCS(牵引力控制系统)激活状态，0-inactive，1-active

  uint8_t vdc_function_status;    // VDC(车辆动态控制系统)功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool vdc_active_status;         // VDC(车辆动态控制系统)激活状态，0-inactive，1-active

  uint8_t hsa_function_status;    // HSA(坡道起步辅助系统)功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool hsa_active_status;         // HSA(坡道起步辅助系统)激活状态，0-inactive，1-active

  uint8_t msr_function_status;    // MSR(发动机阻力矩控制系统)功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool msr_active_status;         // MSR(发动机阻力矩控制系统)激活状态，0-inactive，1-active

  uint8_t hba_function_status;    // HBA(液压制动辅助系统)功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool hba_active_status;         // HBA(液压制动辅助系统)激活状态，0-inactive，1-active

  uint8_t aeb_function_status;    // AEB(自动紧急制动系统)接口功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool aeb_active_status;         // AEB(自动紧急制动系统)接口激活状态，0-inactive，1-active

  uint8_t prefill_function_status;    // 预制动接口功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool prefill_active_status;         // 预制动接口激活状态，0-inactive，1-active

  uint8_t aba_function_status;    // ABA(自动制动辅助系统)接口功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool aba_active_status;         // ABA(自动制动辅助系统)接口激活状态，0-inactive，1-active

  uint8_t awb_function_status;    // AWB(预警制动系统)接口功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool awb_active_status;         // AWB(预警制动系统)接口激活状态，0-inactive，1-active

  uint8_t vlc_function_status;    // VLC功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool vlc_active_status;         // VLC激活状态，0-inactive，1-active

  uint8_t cdd_function_status;    // CDD功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool cdd_active_status;         // CDD激活状态，0-inactive，1-active

  float brake_master_cylinder_pressure;    // 主缸制动压力（4字节）unit: bar, [0 ~ +10]
  float brake_pedal_position;              // 制动踏板开度（4字节）unit: %, [0 ~ +100]
  bool brake_pedal_pressed;                // 制动踏板状态（1字节）0-未踩下,1-踩下
  float input_rod_stroke;                  // 制动踏板行程（4字节）unit: mm, [0 ~ +500]
  DiagnosisSystem diagnosis_system;
};

struct EpsSystem {
  McuHeader header;

  uint8_t eps_control_type;       // 控制模式（1字节）0-unknown，1-use_angle，2-use_torque，3-use_velocity
  uint8_t eps_function_status;    // EPS-行车功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool eps_active_status;         // EPS-行车激活状态，0-inactive，1-active

  uint8_t eps_apa_function_status;    // EPS-APA接口功能状态，0-normal，1-abnormal,2-fault,3-off,4-no exist
  uint8_t eps_apa_active_status;      // EPS-APA接口激活状态，0-inactive，1-active

  float actual_steering_angle;             // 转向角度实际值（8字节）unit: degree, [-1000 ~ +1000]
  float actual_steering_angle_velocity;    // 转向速率实际值（8字节）unit: degree/s, [-1000 ~ +1000]
  float actual_steering_torque;            // 转向扭矩实际值（8字节）unit: Nm, [-100 ~ +100]

  float driver_input_steering_torque;    // 转向扭矩（手力矩）输入（8字节）unit: Nm, [-100 ~ +100]
  DiagnosisSystem diagnosis_system;
};

struct TcuSystem {
  McuHeader header;
  uint8_t gearbox_function_status;    // TCU功能状态（1字节）0-normal，1-abnormal,2-fault,3-off,4-no exist
  uint8_t gearbox_position;           // 变速箱位置（1字节）0-D，1-N，2-R，3-P
  DiagnosisSystem diagnosis_system;
};

struct CrbsSystem {
  McuHeader header;
  uint8_t crbs_function_status;    // 协同制动功能状态（1字节）0-normal 1-abnormal,2-fault,3-off,4-no exist
  uint8_t crbs_level;              // 协同制动等级（1字节）0-无效，1-低，2-中，3-高
  uint8_t crbs_regen_torque;       // 协同制动回收扭矩（4字节）unit: Nm,[0 ~ +100]
  DiagnosisSystem diagnosis_system;
};

struct EpbSystem {
  McuHeader header;
  uint8_t epb_function_status;            // EPB功能状态（1字节）0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool epb_active_status;                 // EPB激活状态（1字节）0-inactive，1-active
  uint8_t epb_dynamic_function_status;    // EPB动态制动功能状态（1字节）0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool epb_dynamic_active_status;         // EPB动态制动激活状态（1字节）0-inactive，1-active
  DiagnosisSystem diagnosis_system;
};

struct AutoholdSystem {
  McuHeader header;
  uint8_t autohold_function_status;    // 自动驻车功能状态（1字节）0-normal，1-abnormal,2-fault,3-off,4-no exist
  bool autohold_active_status;         // 自动驻车激活状态（1字节）0-inactive，1-active
  DiagnosisSystem diagnosis_system;
};

struct BodyDomainCmd {
  McuHeader header;
  uint8_t command_front_light;                        // 请求前车灯状态（1字节）0-未开，1-近光灯，2-远光灯
  uint8_t command_hazard_light;                       // 危险警告灯(双闪)状态（1字节）0-关闭，1-打开
  uint8_t command_width_light;                        // 示宽灯状态（1字节）0-关闭，1-打开
  uint8_t command_front_fog_light;                    // 前雾灯状态（1字节）0-关闭，1-打开
  uint8_t command_rear_fog_light;                     // 后雾灯状态（1字节）0-关闭，1-打开
  uint8_t command_daytime_running_light;              // 日间行车灯状态（1字节）0-关闭，1-打开
  uint8_t command_autonomous_driving_status_light;    // 自动驾驶状态灯（1字节）0-自动驾驶未开启，1-自动驾驶开启
  uint8_t command_turn_light;                         // 转向灯状态（1字节）0:无信号, 1:左转, 2:右转
  uint8_t command_dome_light;                         // 车内顶灯状态（1字节）0-关闭，1-打开

  uint8_t command_horn_function_status;    // 喇叭功能状态（1字节）0-未按下，1-按下

  uint8_t command_fl_door_open;        // 主驾车门状态（1字节）0-关闭，1-打开
  uint8_t command_fl_door_lock;        // 主驾车门锁状态（1字节）0-锁定，1-解锁
  uint8_t command_fl_door_position;    // 主驾车门开度（1字节）0-100%，无控制请求时为0xFF

  uint8_t command_fr_door_open;        // 副驾车门状态（1字节）0-关闭，1-打开
  uint8_t command_fr_door_lock;        // 副驾车门锁状态（1字节）0-锁定，1-解锁
  uint8_t command_fr_door_position;    // 副驾车门开度（1字节）0-100%，无控制请求时为0xFF

  uint8_t command_rl_door_open;        // 左后车门状态（1字节）0-关闭，1-打开
  uint8_t command_rl_door_lock;        // 左后车门锁状态（1字节）0-锁定，1-解锁
  uint8_t command_rl_door_position;    // 左后车门开度（1字节）0-100%，无控制请求时为0xFF

  uint8_t command_rr_door_open;        // 右后车门状态（1字节）0-关闭，1-打开
  uint8_t command_rr_door_lock;        // 右后车门锁状态（1字节）0-锁定，1-解锁
  uint8_t command_rr_door_position;    // 右后车门开度（1字节）0-100%，无控制请求时为0xFF

  uint8_t command_hood_open;             // 前舱盖状态（1字节）0-关闭，1-打开
  uint8_t command_trunk_open;            // 后备箱盖状态（1字节）0-关闭，1-打开
  uint8_t command_charging_door_open;    // 充电口盖状态（1字节） 0-关闭，1-打开

  uint8_t command_fl_window_position;    // 主驾车窗开度（4字节）0-100%, 无控制请求时为0xFF
  uint8_t command_fr_window_position;    // 副驾车窗开度（4字节）0-100%, 无控制请求时为0xFF
  uint8_t command_rl_window_position;    // 左后车窗开度（4字节）0-100%, 无控制请求时为0xFF
  uint8_t command_rr_window_position;    // 右后车窗开度（4字节）0-100%, 无控制请求时为0xFF
  uint8_t command_sun_roof_position;     // 天窗开度（4字节）0-100%, 无控制请求时为0xFF

  uint8_t command_wiper_function_status;    // 雨刷功能状态（1字节）0-关闭，1-间歇，2-慢速，3-快速

  uint8_t command_fl_seat_position;        // 主驾座椅位置（1字节），0-100%，无控制请求时为0xFF
  uint8_t command_fr_seat_position;        // 副驾座椅位置（1字节），0-100%，无控制请求时为0xFF
  uint8_t command_rl_seat_position;        // 左后座椅位置（1字节），0-100%，无控制请求时为0xFF
  uint8_t command_rr_seat_position;        // 右后座椅位置（1字节），0-100%，无控制请求时为0xFF
  uint8_t command_rm_seat_position;        // 中后座椅位置（1字节），0-100%，无控制请求时为0xFF
  uint8_t command_fl_seatback_position;    // 主驾座椅靠背位置（1字节）0-100%, 无控制请求时为0xFF
  uint8_t command_fr_seatback_position;    // 副驾座椅靠背位置（1字节）0-100%, 无控制请求时为0xFF
  uint8_t command_rl_seatback_position;    // 左后座椅靠背位置（1字节）0-100%, 无控制请求时为0xFF
  uint8_t command_rr_seatback_position;    // 右后座椅靠背位置（1字节）0-100%, 无控制请求时为0xFF
  uint8_t command_rm_seatback_position;    // 中后座椅靠背位置（1字节）0-100%, 无控制请求时为0xFF

  uint8_t command_fl_seat_heating;    // 主驾座椅加热状态（1字节）,0-关闭，1-打开
  uint8_t command_fr_seat_heating;    // 副驾座椅加热状态（1字节）,0-关闭，1-打开
  uint8_t command_rl_seat_heating;    // 左后座椅加热状态（1字节）,0-关闭，1-打开
  uint8_t command_rr_seat_heating;    // 右后座椅加热状态（1字节）,0-关闭，1-打开
  uint8_t command_rm_seat_heating;    // 中后座椅加热状态（1字节）,0-关闭，1-打开

  uint8_t command_fl_seat_cooling;    // 主驾座椅通风状态（1字节）,0-关闭，1-打开
  uint8_t command_fr_seat_cooling;    // 副驾座椅通风状态（1字节）,0-关闭，1-打开
  uint8_t command_rl_seat_cooling;    // 左后座椅通风状态（1字节）,0-关闭，1-打开
  uint8_t command_rr_seat_cooling;    // 右后座椅通风状态（1字节）,0-关闭，1-打开
  uint8_t command_rm_seat_cooling;    // 中后座椅通风状态（1字节）,0-关闭，1-打开

  uint8_t command_ac_function_status;         // 空调状态（1字节）0-关闭，1-打开
  uint8_t command_ac_mode;                    // 空调模式（1字节）0-制冷，1-制热
  uint8_t command_ac_temp;                    // 空调温度（1字节） unit: degC, [0 ~ +50], 无控制请求时为0xFF
  uint8_t command_ac_air_circulation_mode;    // 空气循环模式（1字节）0-内循环，1-外循环
  uint8_t command_ac_fanspeed;                // 空调风速（1字节）0-关闭，1-1档，2-2档，3-3档，4-4档，5-5档，6-6档，7-7档，8-8档，9-9档，10-10档
};

struct ChassisDominCmd {
  McuHeader header;

  uint8_t command_drive_command_style;    // 0-vlc,给esp子模块发加减速度请求，1-驱动扭矩（XCU）+减速度（CDD），2-
                                          // 驱动扭矩（XCU）+制动扭矩（ESP）
  uint8_t command_control_source;         //0-driver,1-自动驾驶域控制器，2-远控
  bool command_vlc_handshake;             // 控制模式（1字节）0-未请求握手，1-请求握手
  float command_vlc_accel;                // 控制请求：期望加速度 unit: m/s^2,[-10 ~ +10]
  bool command_vlc_drive_off;             // 控制请求：期望驱动扭矩，0-未请求，1-请求
  bool command_vlc_standstill;            // 控制请求：期望减速度，0-未请求，1-请求
  uint8_t command_vlc_shutdown;           // 控制请求：期望关闭模式，0-未请求，1-请求soft off 2-请求fast off
  bool command_vlc_deceler_to_stop;        // 控制请求：期望减速到停止，0-未请求，1-请求
  uint8_t command_vlc_acc_mode;           // 控制请求：acc mode,0-init,1-passive,2-standby,3-active,4-brake_only,
                                          // 5-override,6-standstiall,7-fault

  bool command_xcu_handshake;
  float command_xcu_drive_torque;    // 控制请求：期望驱动扭矩（4字节） unit: Nm
  bool command_cdd_handshake;
  float command_cdd_accel;    // 控制请求：期望减速度（4字节） unit: m/s^2,需要发小于0的值,[-10 ~ 0]
  bool command_break_torque_handshake;
  float command_brake_torque;    // 控制请求：期望制动扭矩（4字节） unit: Nm,[-2000 ~ +2000]
                                     // 无控制请求时发当前制动扭矩值，若无当前制动扭矩发0

  uint8_t command_eps_command_style;    // 0-发送转向角度，1-发送转向力矩，2-发送转向速率
  bool command_eps_handshake;           // 转向模式（1字节）0-未请求握手，1-请求握手
  float command_steering_angle;         // 控制请求：转向角度（4字节） unit: deg,[-1000 ~ +1000]
  float command_steering_angle_vel;     // 控制请求：转向速率（4字节） unit: deg/s,[-1000 ~ +1000]
                                        // 无控制请求时发当前转向速率值，若无当前转向速率发0
  float command_steering_torque;        // 控制请求：转向力矩（4字节） unit: Nm,[-100 ~ +100]
                                        // 无控制请求时发当前转向力矩值，若无当前转向力矩发0

  bool command_esp_apa_handshake;
  float command_esp_apa_target_speed;       // 控制请求：期望速度（4字节） unit: km/h, [0 ~ +50]
  float command_esp_apa_target_distance;    // 控制请求：期望距离（4字节） unit: m, [0 ~ +50]

  bool command_eps_apa_handshake;          // 期望转向握手,0-未请求握手，1-请求握手
  float command_eps_apa_steering_angle;    // 控制请求：期望转向角度（4字节） unit: deg,[-1000 ~ +1000]

  bool command_gear_position_handshake;    // 挡位握手,0-未请求握手，1-请求握手
  uint8_t command_gear_position;           // 控制请求：挡位位置（1字节）0-空挡，1-前进，2-倒车，3-驻车

  bool command_crbs_handshake;    // 协同制动握手,0-未请求握手，1-请求握手
  uint8_t command_crbs_level;     // 请求制动能量回收等级，0-0，1-1，2-2，3-3

  bool command_epb_handshake;            // EPB握手,0-未请求握手，1-请求握手
  uint8_t command_epb_status;            // 请求EPB动作，0-释放，1-拉起
  bool command_epb_dynamic_handshake;    // EPB动态握手,0-未请求握手，1-请求握手
  uint8_t command_epb_dynamic_status;    // 请求EPB动态动作，0-释放，1-拉起

  bool command_auto_hold_handshake;    // 自动驻车握手,0-未请求握手，1-请求握手
  uint8_t command_auto_hold;           // 控制请求：自动驻车（1字节）0-未请求，1-请求

  bool command_aeb_handshake;        // AEB握手,0-未请求握手，1-请求握手
  float command_aeb_target_accel;    // AEB期望减速度（4字节） unit: m/s^2,需要发小于0的值,[-20 ~ 0]

  bool command_aba_handshake;    // ABA握手,0-未请求握手，1-请求握手
  uint8_t command_aba_level;     // 请求制动能量回收等级，0-0，1-1档，2-2档，3-3档

  bool command_awb_handshake;    // AWB握手,0-未请求握手，1-请求握手
  uint8_t command_awb_level;     // 请求制动能量回收等级，0-0，1-1档，2-2档，3-3档

  bool command_prefill_active;    // 预充气,0-未请求激活,1-请求激活

  bool command_takeover;    // 接管握手,0-未请求接管，1-请求接管
};
}    // namespace interface
}    // namespace hv
