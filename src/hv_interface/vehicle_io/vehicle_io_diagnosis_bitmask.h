/**
 * @file vehicle_io_diagnosis_bitmask.h
 * @brief 车辆IO诊断位掩码定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */
#pragma once

#include <cstdint>

namespace hv {
namespace interface {
namespace diagnosis_bitmask {

// 仪表系统诊断位掩码
namespace ClusterSystemDiagnosis {
constexpr uint32_t CLUSTER_VEHICLE_SPEED = 1 << 0;    // 仪表车速信号
}    // namespace ClusterSystemDiagnosis

// 驾驶员输入系统诊断位掩码
namespace DriverInputSystemDiagnosis {
constexpr uint32_t DRIVER_PRESSED_TURN_LIGHT = 1 << 0;      // 驾驶员按下转向灯信号
constexpr uint32_t DRIVER_PRESSED_FRONT_LIGHT = 1 << 1;     // 驾驶员按下前车灯信号
constexpr uint32_t DRIVER_PRESSED_HAZARD_LIGHT = 1 << 2;    // 驾驶员按下危险警告灯信号
constexpr uint32_t DRIVER_PRESSED_FOG_LIGHT = 1 << 3;       // 驾驶员按下雾灯信号
constexpr uint32_t DRIVER_PRESSER_WIPER = 1 << 4;           // 驾驶员按下雨刷信号
constexpr uint32_t DRIVER_PRESSED_WASHER = 1 << 5;          // 驾驶员按下玻璃清洗器信号
constexpr uint32_t DRIVER_PRESSED_HORN = 1 << 6;            // 驾驶员按下喇叭信号
constexpr uint32_t DRIVER_SHIFT_GEAR = 1 << 7;              // 驾驶员换挡信号
}    // namespace DriverInputSystemDiagnosis

// 驾驶员AD输入系统诊断位掩码
namespace DriverAdInputSystemDiagnosis {
constexpr uint32_t DRIVER_PRESSED_AD_RUN = 1 << 0;      // 驾驶员按下自动驾驶开始信号
constexpr uint32_t DRIVER_PRESSED_AD_STOP = 1 << 1;     // 驾驶员按下自动驾驶结束信号
constexpr uint32_t DRIVER_PRESSED_AD_ACCLER = 1 << 2;   // 驾驶员按下自动驾驶加速信号
constexpr uint32_t DRIVER_PRESSED_AD_DECEL = 1 << 3;    // 驾驶员按下自动驾驶减速信号
constexpr uint32_t DRIVER_PRESSED_AD_HY_UP = 1 << 4;    // 驾驶员按下自动驾驶巡航速度上抬信号
constexpr uint32_t DRIVER_PRESSED_AD_HY_DOWN = 1 << 5;  // 驾驶员按下自动驾驶巡航速度下压信号
}    // namespace DriverAdInputSystemDiagnosis

// 照明系统诊断位掩码
namespace LightingSystemDiagnosis {
constexpr uint32_t LIGHTING_SYSTEM_STATUS = 1 << 0;    // 灯光系统状态位掩码
constexpr uint32_t FRONT_LIGHT = 1 << 1;               // 前车灯信号
constexpr uint32_t HAZARD_LIGHT = 1 << 2;              // 危险警告灯信号
constexpr uint32_t WIDTH_LIGHT = 1 << 3;               // 示宽灯信号
constexpr uint32_t REVERSE_LIGHT = 1 << 4;             // 倒车灯信号
constexpr uint32_t FRONT_FOG_LIGHT = 1 << 5;           // 前雾灯信号
constexpr uint32_t REAR_FOG_LIGHT = 1 << 6;            // 后雾灯信号
constexpr uint32_t BRAKE_LIGHT = 1 << 7;               // 刹车灯信号
constexpr uint32_t AD_STATUS_LIGHT = 1 << 8;           // 自动驾驶状态灯信号
constexpr uint32_t TURN_LIGHT = 1 << 9;                // 转向灯信号
constexpr uint32_t DOME_LIGHT = 1 << 10;               // 车内顶灯信号
}    // namespace LightingSystemDiagnosis

// 喇叭系统诊断位掩码
namespace HornSystemDiagnosis {
constexpr uint32_t HORN_FUNCTION = 1 << 0;    // 喇叭功能状态信号
constexpr uint32_t HORN_ACTIVE = 1 << 1;      // 喇叭激活状态信号
}

// 安全气囊系统诊断位掩码
namespace AirbagSystemDiagnosis {
constexpr uint32_t AIRBAG_FUNCTION = 1 << 0;    // 安全气囊状态信号
constexpr uint32_t AIRBAG_ACTIVE = 1 << 1;      // 安全气囊激活状态信号
constexpr uint32_t CRASH_STATUS = 1 << 2;       // 碰撞状态信号
}

// 车门系统诊断位掩码
namespace DoorSystemDiagnosis {
constexpr uint32_t DOOR_SYSTEM_STATUS = 1 << 0;         // 车门系统状态位掩码
constexpr uint32_t DOOR_TYPE = 1 << 1;                  // 车门类型信号
constexpr uint32_t FL_DOOR_OPEN = 1 << 2;               // 主驾车门状态信号
constexpr uint32_t FR_DOOR_OPEN = 1 << 3;               // 副驾车门状态信号
constexpr uint32_t RL_DOOR_OPEN = 1 << 4;               // 左后车门状态信号
constexpr uint32_t RR_DOOR_OPEN = 1 << 5;               // 右后车门状态信号
constexpr uint32_t FL_DOOR_LOCK = 1 << 6;               // 主驾车门锁状态信号
constexpr uint32_t FR_DOOR_LOCK = 1 << 7;               // 副驾车门锁状态信号
constexpr uint32_t RL_DOOR_LOCK = 1 << 8;               // 左后车门锁状态信号
constexpr uint32_t RR_DOOR_LOCK = 1 << 9;               // 右后车门锁状态信号
constexpr uint32_t FL_DOOR_POSITION = 1 << 10;          // 主驾车门开度信号
constexpr uint32_t FR_DOOR_POSITION = 1 << 11;          // 副驾车门开度信号
constexpr uint32_t RL_DOOR_POSITION = 1 << 12;          // 左后车门开度信号
constexpr uint32_t RR_DOOR_POSITION = 1 << 13;          // 右后车门开度信号
constexpr uint32_t HOOD_OPEN = 1 << 14;                 // 前舱盖状态信号
constexpr uint32_t TRUNK_OPEN = 1 << 15;                // 后备箱盖状态信号
constexpr uint32_t CHARGING_DOOR_OPEN = 1 << 16;        // 充电口盖状态信号
constexpr uint32_t CHARGING_GUN_INSERTED = 1 << 17;     // 充电枪插入状态信号
constexpr uint32_t ANTI_PINCH_STATUS = 1 << 18;         // 防夹状态信号
}    // namespace DoorSystemDiagnosis

// 车窗系统诊断位掩码
namespace WindowSystemDiagnosis {
constexpr uint32_t WINDOW_SYSTEM_STATUS = 1 << 0;    // 车窗系统状态位掩码
constexpr uint32_t FL_WINDOW_POSITION = 1 << 1;      // 主驾车窗开度信号
constexpr uint32_t FR_WINDOW_POSITION = 1 << 2;      // 副驾车窗开度信号
constexpr uint32_t RL_WINDOW_POSITION = 1 << 3;      // 左后车窗开度信号
constexpr uint32_t RR_WINDOW_POSITION = 1 << 4;      // 右后车窗开度信号
constexpr uint32_t SUN_ROOF_POSITION = 1 << 5;       // 天窗开度信号
}    // namespace WindowSystemDiagnosis

// 雨刷系统诊断位掩码
namespace WiperSystemDiagnosis {
constexpr uint32_t WIPER_FUNCTION = 1 << 0;    // 雨刷功能状态信号
constexpr uint32_t WASHER_FUNCTION = 1 << 1;   // 玻璃清洗器功能状态信号
}

// 后视镜系统诊断位掩码
namespace SideMirrorSystemDiagnosis {
constexpr uint32_t SIDE_MIRROR_FUNCTION = 1 << 0;    // 后视镜功能状态信号
}

// 座椅占用系统诊断位掩码
namespace SeatOccupcySystemDiagnosis {
constexpr uint32_t SEAT_OCCUPCY_SYSTEM_STATUS = 1 << 0;    // 座椅占用系统状态位掩码
constexpr uint32_t FL_SEAT_OCCUPIED = 1 << 1;              // 主驾座椅占用状态信号
constexpr uint32_t FR_SEAT_OCCUPIED = 1 << 2;              // 副驾座椅占用状态信号
constexpr uint32_t RL_SEAT_OCCUPIED = 1 << 3;              // 左后座椅占用状态信号
constexpr uint32_t RR_SEAT_OCCUPIED = 1 << 4;              // 右后座椅占用状态信号
constexpr uint32_t RM_SEAT_OCCUPIED = 1 << 5;              // 中后座椅占用状态信号
}    // namespace SeatOccupcySystemDiagnosis

// 座椅位置系统诊断位掩码
namespace SeatPositionSystemDiagnosis {
constexpr uint32_t SEAT_POSITION_SYSTEM_STATUS = 1 << 0;    // 座椅位置系统状态位掩码
constexpr uint32_t FL_SEAT_POSITION = 1 << 1;               // 主驾座椅位置信号
constexpr uint32_t FR_SEAT_POSITION = 1 << 2;               // 副驾座椅位置信号
constexpr uint32_t RL_SEAT_POSITION = 1 << 3;               // 左后座椅位置信号
constexpr uint32_t RR_SEAT_POSITION = 1 << 4;               // 右后座椅位置信号
constexpr uint32_t RM_SEAT_POSITION = 1 << 5;               // 中后座椅位置信号
constexpr uint32_t FL_SEATBACK_POSITION = 1 << 6;           // 主驾座椅靠背位置信号
constexpr uint32_t FR_SEATBACK_POSITION = 1 << 7;           // 副驾座椅靠背位置信号
constexpr uint32_t RL_SEATBACK_POSITION = 1 << 8;           // 左后座椅靠背位置信号
constexpr uint32_t RR_SEATBACK_POSITION = 1 << 9;           // 右后座椅靠背位置信号
constexpr uint32_t RM_SEATBACK_POSITION = 1 << 10;          // 中后座椅靠背位置信号
}    // namespace SeatPositionSystemDiagnosis

// 座椅加热系统诊断位掩码
namespace SeatHeatingSystemDiagnosis {
constexpr uint32_t SEAT_HEATING_SYSTEM_STATUS = 1 << 0;    // 座椅加热系统状态位掩码
constexpr uint32_t FL_SEAT_HEATING = 1 << 1;               // 主驾座椅加热状态信号
constexpr uint32_t FR_SEAT_HEATING = 1 << 2;               // 副驾座椅加热状态信号
constexpr uint32_t RL_SEAT_HEATING = 1 << 3;               // 左后座椅加热状态信号
constexpr uint32_t RR_SEAT_HEATING = 1 << 4;               // 右后座椅加热状态信号
constexpr uint32_t RM_SEAT_HEATING = 1 << 5;               // 中后座椅加热状态信号
}    // namespace SeatHeatingSystemDiagnosis

// 座椅通风系统诊断位掩码
namespace SeatCoolingSystemDiagnosis {
constexpr uint32_t SEAT_COOLING_SYSTEM_STATUS = 1 << 0;    // 座椅通风系统状态位掩码
constexpr uint32_t FL_SEAT_COOLING = 1 << 1;               // 主驾座椅通风状态信号
constexpr uint32_t FR_SEAT_COOLING = 1 << 2;               // 副驾座椅通风状态信号
constexpr uint32_t RL_SEAT_COOLING = 1 << 3;               // 左后座椅通风状态信号
constexpr uint32_t RR_SEAT_COOLING = 1 << 4;               // 右后座椅通风状态信号
constexpr uint32_t RM_SEAT_COOLING = 1 << 5;               // 中后座椅通风状态信号
}    // namespace SeatCoolingSystemDiagnosis

// 安全带系统诊断位掩码
namespace SeatBeltSystemDiagnosis {
constexpr uint32_t SEAT_BELT_SYSTEM_STATUS = 1 << 0;    // 安全带系统状态位掩码
constexpr uint32_t FL_BELT_ATTACHED = 1 << 1;           // 主驾安全带信号
constexpr uint32_t FR_BELT_ATTACHED = 1 << 2;           // 副驾安全带信号
constexpr uint32_t RL_BELT_ATTACHED = 1 << 3;           // 左后安全带信号
constexpr uint32_t RR_BELT_ATTACHED = 1 << 4;           // 右后安全带信号
constexpr uint32_t RM_BELT_ATTACHED = 1 << 5;           // 中后安全带信号
}    // namespace SeatBeltSystemDiagnosis

// 空调系统诊断位掩码
namespace AirConditioningSystemDiagnosis {
constexpr uint32_t AC_SYSTEM_STATUS = 1 << 0;              // 空调系统状态信号
constexpr uint32_t AC_FUNCTION = 1 << 1;                   // 空调状态信号
constexpr uint32_t AC_MODE = 1 << 2;                       // 空调模式信号
constexpr uint32_t AC_TEMP = 1 << 3;                       // 空调温度信号
constexpr uint32_t AC_FANSPEED = 1 << 4;                   // 空调风速信号
constexpr uint32_t AIR_CIRCULATION_MODE = 1 << 5;          // 空气循环模式信号
}    // namespace AirConditioningSystemDiagnosis

// 电池系统诊断位掩码
namespace BatterySystemDiagnosis {
constexpr uint32_t BATTERY_SYSTEM_STATUS = 1 << 0;              // 电池系统状态信号
constexpr uint32_t BATTERY_FUNCTION = 1 << 1;                   // 电池功能状态信号
constexpr uint32_t BATTERY_SOC = 1 << 2;                        // 电池电量信号
constexpr uint32_t BATTERY_SOH = 1 << 3;                        // 电池健康状态信号
constexpr uint32_t BATTERY_TEMPERATURE = 1 << 4;                // 电池温度信号
constexpr uint32_t BATTERY_COOLING_WATER_TEMPERATURE = 1 << 5;  // 电池冷却水温度信号
constexpr uint32_t BATTERY_REMAINING_MILEAGE = 1 << 6;          // 电池剩余里程信号
constexpr uint32_t AUXILIARY_BATTERY_STATUS = 1 << 7;           // 辅助电池状态信号
}    // namespace BatterySystemDiagnosis

// 胎压监测系统诊断位掩码
namespace TirePressureMonitoringSystemDiagnosis {
constexpr uint32_t TIRE_PRESSURE_MONITORING_SYSTEM_STATUS = 1 << 0;    // 轮胎压力监测系统状态位掩码
constexpr uint32_t FL_TIRE_PRESSURE = 1 << 1;                          // 左前轮胎压力值信号
constexpr uint32_t FR_TIRE_PRESSURE = 1 << 2;                          // 右前轮胎压力值信号
constexpr uint32_t RL_TIRE_PRESSURE = 1 << 3;                          // 左后轮胎压力值信号
constexpr uint32_t RR_TIRE_PRESSURE = 1 << 4;                          // 右后轮胎压力值信号
constexpr uint32_t FL_TIRE_LOW_PRESSURE_WARNING = 1 << 5;              // 左前轮胎压力低警告信号
constexpr uint32_t FR_TIRE_LOW_PRESSURE_WARNING = 1 << 6;              // 右前轮胎压力低警告信号
constexpr uint32_t RL_TIRE_LOW_PRESSURE_WARNING = 1 << 7;              // 左后轮胎压力低警告信号
constexpr uint32_t RR_TIRE_LOW_PRESSURE_WARNING = 1 << 8;              // 右后轮胎压力低警告信号
}    // namespace TirePressureMonitoringSystemDiagnosis

// 车辆信息系统诊断位掩码
namespace VehicleInformationSystemDiagnosis {
constexpr uint32_t PREFIX = 1 << 0;                    // 前缀字段信号
constexpr uint32_t CODE = 1 << 1;                      // VIN码主体信号
constexpr uint32_t VEHICLE_POWER_STATUS = 1 << 2;      // 车辆电源状态信号
constexpr uint32_t OUTSIDE_TEMPERATURE = 1 << 3;       // 车外温度信号
constexpr uint32_t TOTAL_MILEAGE = 1 << 4;             // 总里程信号
}    // namespace VehicleInformationSystemDiagnosis

// 运动系统诊断位掩码
namespace MotionSystemDiagnosis {
constexpr uint32_t VEHICLE_SPEED = 1 << 0;                    // 车速信号
constexpr uint32_t VEHICLE_LONG_ACCEL = 1 << 1;               // 纯纵向加速度信号
constexpr uint32_t VEHICLE_LAT_ACCEL = 1 << 2;                // 纯横向加速度信号
constexpr uint32_t VEHICLE_YAW_RATE = 1 << 3;                 // 车偏横摆角速度信号
}    // namespace MotionSystemDiagnosis

// 车轮系统诊断位掩码
namespace WheelSystemDiagnosis {
constexpr uint32_t FL_WHEEL_SPEED = 1 << 0;        // 前左轮速度信号
constexpr uint32_t FR_WHEEL_SPEED = 1 << 1;        // 前右轮速度信号
constexpr uint32_t RL_WHEEL_SPEED = 1 << 2;        // 后左轮速度信号
constexpr uint32_t RR_WHEEL_SPEED = 1 << 3;        // 后右轮速度信号
constexpr uint32_t FL_WHEEL_DIRECTION = 1 << 4;    // 前左轮转向方向信号
constexpr uint32_t FR_WHEEL_DIRECTION = 1 << 5;    // 前右轮转向方向信号
constexpr uint32_t RL_WHEEL_DIRECTION = 1 << 6;    // 后左轮转向方向信号
constexpr uint32_t RR_WHEEL_DIRECTION = 1 << 7;    // 后右轮转向方向信号
constexpr uint32_t FL_WHEEL_PULSE = 1 << 8;        // 前左轮脉冲数信号
constexpr uint32_t FR_WHEEL_PULSE = 1 << 9;        // 前右轮脉冲数信号
constexpr uint32_t RL_WHEEL_PULSE = 1 << 10;       // 后左轮脉冲数信号
constexpr uint32_t RR_WHEEL_PULSE = 1 << 11;       // 后右轮脉冲数信号
}    // namespace WheelSystemDiagnosis

// XCU系统诊断位掩码
namespace XcuSystemDiagnosis {
constexpr uint32_t XCU_FUNCTION = 1 << 0;                        // XCU功能状态信号
constexpr uint32_t XCU_ACTIVE = 1 << 1;                          // XCU激活状态信号
constexpr uint32_t MOTOR_TYPE = 1 << 2;                          // 电机类型信号
constexpr uint32_t FRONT_MOTOR_RPM = 1 << 3;                     // 前电机转速信号
constexpr uint32_t REAR_MOTOR_RPM = 1 << 4;                      // 后电机转速信号
constexpr uint32_t REQUEST_FRONT_MOTOR_TORQUE = 1 << 5;          // 前电机需求扭矩信号
constexpr uint32_t ACTUAL_FRONT_MOTOR_TORQUE = 1 << 6;           // 前电机实际扭矩信号
constexpr uint32_t ACTUAL_FRONT_MOTOR_TORQUE_MAXIMUM = 1 << 7;   // 前电机实际扭矩允许最大值信号
constexpr uint32_t ACTUAL_FRONT_MOTOR_TORQUE_MINIMUM = 1 << 8;   // 前电机实际扭矩允许最小值信号
constexpr uint32_t REQUEST_REAR_MOTOR_TORQUE = 1 << 9;           // 后电机需求扭矩信号
constexpr uint32_t ACTUAL_REAR_MOTOR_TORQUE = 1 << 10;           // 后电机实际扭矩信号
constexpr uint32_t ACTUAL_REAR_MOTOR_TORQUE_MAXIMUM = 1 << 11;   // 后电机实际扭矩允许最大值信号
constexpr uint32_t ACTUAL_REAR_MOTOR_TORQUE_MINIMUM = 1 << 12;   // 后电机实际扭矩允许最小值信号
constexpr uint32_t FRONT_SHAFT_TORQUE = 1 << 13;                 // 前驱动轴扭矩信号
constexpr uint32_t REAR_SHAFT_TORQUE = 1 << 14;                  // 后驱动轴扭矩信号
constexpr uint32_t FRONT_INPUT_SHAFT_RPM = 1 << 15;              // 前轴转速原始值信号
constexpr uint32_t REAR_INPUT_SHAFT_RPM = 1 << 16;               // 后轴转速原始值信号
constexpr uint32_t ACCEL_PEDAL_POSITION = 1 << 17;               // 加速踏板开度信号
constexpr uint32_t VIRTUAL_ACCEL_PEDAL_POSITION = 1 << 18;       // 虚拟加速踏板开度信号
constexpr uint32_t IS_OVERRIDE = 1 << 19;                        // 是否被驾驶员踩下信号
constexpr uint32_t VEHICLE_DRIVING_MODE = 1 << 20;               // 车辆驾驶模式信号
}    // namespace XcuSystemDiagnosis

// ESP系统诊断位掩码
namespace EspSystemDiagnosis {
constexpr uint32_t ESP_CONTROL_TYPE = 1 << 0;                    // 控制模式信号
constexpr uint32_t ESP_FUNCTION = 1 << 1;                        // ESP功能状态信号
constexpr uint32_t ESP_ACTIVE = 1 << 2;                          // ESP激活状态信号
constexpr uint32_t ABS_FUNCTION = 1 << 3;                        // ABS功能状态信号
constexpr uint32_t ABS_ACTIVE = 1 << 4;                          // ABS激活状态信号
constexpr uint32_t TCS_FUNCTION = 1 << 5;                        // TCS功能状态信号
constexpr uint32_t TCS_ACTIVE = 1 << 6;                          // TCS激活状态信号
constexpr uint32_t VDC_FUNCTION = 1 << 7;                        // VDC功能状态信号
constexpr uint32_t VDC_ACTIVE = 1 << 8;                          // VDC激活状态信号
constexpr uint32_t HSA_FUNCTION = 1 << 9;                        // HSA功能状态信号
constexpr uint32_t HSA_ACTIVE = 1 << 10;                         // HSA激活状态信号
constexpr uint32_t MSR_FUNCTION = 1 << 11;                       // MSR功能状态信号
constexpr uint32_t MSR_ACTIVE = 1 << 12;                         // MSR激活状态信号
constexpr uint32_t HBA_FUNCTION = 1 << 13;                       // HBA功能状态信号
constexpr uint32_t HBA_ACTIVE = 1 << 14;                         // HBA激活状态信号
constexpr uint32_t AEB_FUNCTION = 1 << 15;                       // AEB功能状态信号
constexpr uint32_t AEB_ACTIVE = 1 << 16;                         // AEB激活状态信号
constexpr uint32_t PREFILL_FUNCTION = 1 << 17;                   // 预制动功能状态信号
constexpr uint32_t PREFILL_ACTIVE = 1 << 18;                     // 预制动激活状态信号
constexpr uint32_t ABA_FUNCTION = 1 << 19;                       // ABA功能状态信号
constexpr uint32_t ABA_ACTIVE = 1 << 20;                         // ABA激活状态信号
constexpr uint32_t AWB_FUNCTION = 1 << 21;                       // AWB功能状态信号
constexpr uint32_t AWB_ACTIVE = 1 << 22;                         // AWB激活状态信号
constexpr uint32_t VLC_FUNCTION = 1 << 23;                       // VLC功能状态信号
constexpr uint32_t VLC_ACTIVE = 1 << 24;                         // VLC激活状态信号
constexpr uint32_t CDD_FUNCTION = 1 << 25;                       // CDD功能状态信号
constexpr uint32_t CDD_ACTIVE = 1 << 26;                         // CDD激活状态信号
constexpr uint32_t BRAKE_MASTER_CYLINDER_PRESSURE = 1 << 27;     // 主缸制动压力信号
constexpr uint32_t BRAKE_PEDAL_POSITION = 1 << 28;               // 制动踏板开度信号
constexpr uint32_t BRAKE_PEDAL_PRESSED = 1 << 29;                // 制动踏板状态信号
constexpr uint32_t INPUT_ROD_STROKE = 1 << 30;                   // 制动踏板行程信号
}    // namespace EspSystemDiagnosis

// EPS系统诊断位掩码
namespace EpsSystemDiagnosis {
constexpr uint32_t EPS_CONTROL_TYPE = 1 << 0;                    // 控制模式信号
constexpr uint32_t EPS_FUNCTION = 1 << 1;                        // EPS功能状态信号
constexpr uint32_t EPS_ACTIVE = 1 << 2;                          // EPS激活状态信号
constexpr uint32_t EPS_APA_FUNCTION = 1 << 3;                    // EPS-APA功能状态信号
constexpr uint32_t EPS_APA_ACTIVE = 1 << 4;                      // EPS-APA激活状态信号
constexpr uint32_t ACTUAL_STEERING_ANGLE = 1 << 5;               // 转向角度实际值信号
constexpr uint32_t ACTUAL_STEERING_ANGLE_VELOCITY = 1 << 6;      // 转向速率实际值信号
constexpr uint32_t ACTUAL_STEERING_TORQUE = 1 << 7;              // 转向扭矩实际值信号
constexpr uint32_t DRIVER_INPUT_STEERING_TORQUE = 1 << 8;        // 转向扭矩输入信号
}    // namespace EpsSystemDiagnosis

// TCU系统诊断位掩码
namespace TcuSystemDiagnosis {
constexpr uint32_t GEARBOX_FUNCTION = 1 << 0;    // TCU功能状态信号
constexpr uint32_t GEARBOX_POSITION = 1 << 1;    // 变速箱位置信号
}    // namespace TcuSystemDiagnosis

// CRBS系统诊断位掩码
namespace CrbsSystemDiagnosis {
constexpr uint32_t CRBS_FUNCTION = 1 << 0;        // 协同制动功能状态信号
constexpr uint32_t CRBS_LEVEL = 1 << 1;           // 协同制动等级信号
constexpr uint32_t CRBS_REGEN_TORQUE = 1 << 2;    // 协同制动回收扭矩信号
}    // namespace CrbsSystemDiagnosis

// EPB系统诊断位掩码
namespace EpbSystemDiagnosis {
constexpr uint32_t EPB_FUNCTION = 1 << 0;                // EPB功能状态信号
constexpr uint32_t EPB_ACTIVE = 1 << 1;                  // EPB激活状态信号
constexpr uint32_t EPB_DYNAMIC_FUNCTION = 1 << 2;        // EPB动态功能状态信号
constexpr uint32_t EPB_DYNAMIC_ACTIVE = 1 << 3;          // EPB动态激活状态信号
}

// 自动驻车系统诊断位掩码
namespace AutoholdSystemDiagnosis {
constexpr uint32_t AUTOHOLD_FUNCTION = 1 << 0;    // 自动驻车功能状态信号
constexpr uint32_t AUTOHOLD_ACTIVE = 1 << 1;      // 自动驻车激活状态信号
}

}    // namespace diagnosis_bitmask
}    // namespace interface
}    // namespace hv
