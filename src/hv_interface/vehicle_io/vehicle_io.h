/**
 * @file vehicle_io.h
 * @brief 车辆IO相关定义
 * <AUTHOR> Interface Team
 * @date 2025
 * 
 * 此文件包含了HV Interface库的相关功能定义
 */

#pragma once

#include "../common/header.h"
#include "mcu_service.h"
#include <cstdint>    // For int8_t, uint8_t
#include <vector>

namespace hv {
namespace interface {

// 车身域，对于时间延迟不敏感，更新周期可能较低。
struct BodyDomain {
  Header header;
  ClusterSystem cluster_system;
  DriverInputSystem driver_input_system;
  LightingSystem lighting_system;
  HornSystem horn_system;
  AirbagSystem airbag_system;
  DoorSystem door_system;
  WindowSystem window_system;
  WiperSystem wiper_system;
  SideMirrorSystem side_mirror_system;
  SeatOccupcySystem seat_occupcy_system;
  SeatPositionSystem seat_position_system;
  SeatHeatingSystem seat_heating_system;
  SeatCoolingSystem seat_cooling_system;
  SeatBeltSystem seat_belt_system;
  AirConditioningSystem air_conditioning_system;
  BatterySystem battery_system;
  TirePressureMonitoringSystem tire_pressure_monitoring_system;
};

// 动力域，对时间延迟敏感，更新周期较低，可能10ms（100hz）,具体更新周期需要根据实际需求确定
struct ChassisDomain {
  Header header;
  VehicleInformationSystem vehicle_information_system;
  MotionSystem motion_system;
  WheelSystem wheel_system;
  XcuSystem xcu_system;
  EspSystem esp_system;
  EpsSystem eps_system;
  TcuSystem tcu_system;
  CrbsSystem crbs_system;
  EpbSystem epb_system;
  AutoholdSystem autohold_system;
};

}    // namespace interface
}    // namespace hv
