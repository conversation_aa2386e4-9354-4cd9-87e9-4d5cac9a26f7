# --- Percp ANCInfo ---
# @brief 感知输出补充辅助信息
# @details

# @brief 感知物数量
# @details
int32 num_od

# @brief 本车道最近车(CIPV)ID
# @details
int32 id_cipv

# @brief 最近行人(MCP)ID
# @details
int32 id_mcp

# @brief CIPV预期碰撞时间(ttc)
# @details
float32 cipv_ttc

# @brief CIPV_ttc有效性
# @enum 0 -> 无效
# @enum 1 -> 有效
float32 cipv_ttc_valid

# @brief MCP预期碰撞时间(ttc)
# @details
float32 mcp_ttc

# @brief MCP_ttc有效性
# @enum 0 -> 无效
# @enum 1 -> 有效
float32 mcp_ttc_valid

