
# --- Percp SensorMeta ---
# @brief 感知输出传感器元信息
# @details

# @brief 输入传感器类型
# @enum 0 -> UNKNOWN 未知传感器类型
# @enum 1 -> MERGED_PCLOUD 拼接Lidar点云
# @enum 2 -> FRONT_SOLID_MLIDAR 前固态主Lidar
# @enum 3 -> LEFT_SOLID_MLIDAR 左固态主Lidar
# @enum 4 -> RIGHT_SOLID_MLIDAR 右固态主Lidar
# @enum 5 -> REAR_SOLID_MLIDAR 后固态主Lidar
# @enum 6 -> FRONT_SOLID_SLIDAR 前固态补盲Lidar
# @enum 7 -> LEFT_SOLID_SLIDAR 左固态补盲Lidar
# @enum 8 -> RIGHT_SOLID_SLIDAR 右固态补盲Lidar
# @enum 9 -> FRONT_WIDE_CAM 前视广角相机
# @enum 10 -> FRONT_FAR_CAM 前视长距相机
# @enum 11 -> LEFT_PANO_CAM 左视周视相机
# @enum 12 -> RIGHT_PANO_CAM 右视周视相机
# @enum 13 -> LEFTFRONT_PANO_CAM 左前周视相机
# @enum 14 -> RIGHTFRONT_PANO_CAM 右前周视相机
# @enum 15 -> LEFTREAR_PANO_CAM左后周视相机
# @enum 16 -> RIGHTREAR_PANO_CAM 右后周视相机
# @enum 17 -> REAR_PANO_CAM 后视周视相机
# @enum 18 -> FRONT_SURR_CAM 前视鱼眼相机
# @enum 19 -> LEFT_SURR_CAM 左视鱼眼相机
# @enum 20 -> RIGHT_SURR_CAM 右视鱼眼相机
# @enum 21 -> REAR_SURR_CAM 后视鱼眼相机
int8 sensor_type

# @brief 双浮点传感器时间戳
# @unit s(至少精确至ms)
float64 sensor_timestamp

