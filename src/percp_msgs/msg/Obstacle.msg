# --- Percp Obstacle ---
# @brief 感知输出单个障碍物信息
# @details

# @brief 障碍物id
# @details 不输出-1/0
int32                 track_id

# @brief 是否为检测器直接输出
# @enum 0 -> 跟踪器预测
# @enum 1 -> 检测器直接输出
int8                  is_detected

# @brief 障碍物目标跟踪时长
# @unit s(至少精确至ms)
float32               tracking_time

# @brief 障碍物目标跟踪帧数
int32                 tracking_age

# @brief 障碍物位置姿态及包围盒/最近边
# @details 自车坐标系(自车前进方向为x 竖直方向为z)
# @details 位置描述为感知bbox中心接地处
# @details 不输出则填0
# @unit m rad
Point32               position
float32               length
float32               width
float32               height
float32               yaw
float32               pitch
float32               roll
Polygon               polygon_bev
Polygon               near_edge

# @brief 类别置信度
# @range 0.0-1.0
float32               od_confidence

# @brief 速度置信度
# @range 0.0-1.0
float32               velocity_confidence

# @brief 速度/加速度/横摆角速度
# @unit m/s m/s^2 rad/s
Point32               velocity
Point32               acceleration
float32               yaw_rate

# @brief 位置/速度/加速度/横摆角/横摆角速度标准差
# @details 自车坐标系(自车前进方向为x 竖直方向为z)
Point32               position_std
Point32               velocity_std
Point32               acceleration_std
float32               yaw_std
float32               yaw_rate_std

# @brief OD来源
# @enum 0 -> 未知来源
# @enum 1 -> 周视相机
# @enum 2 -> 环视相机
# @enum 4 -> Lidar模型
# @enum 5 -> Lidar聚类
# @enum 6 -> Radar
# @enum 7 -> LC融合
# @enum 8 -> LR融合
# @enum 9 -> LCR融合
# @enum 10 -> CR融合
int8                  od_source

# @brief 速度来源
# @enum 0 -> 未知来源
# @enum 1 -> 模型
# @enum 2 -> 跟踪器
# @enum 3 -> Radar
# @enum 4 -> 融合
int8                  velocity_source

# @brief 障碍物运动状态
# @enum 0 -> 未见输出
# @enum 1 -> 运动
# @enum 2 -> 静止
# @enum 3 -> 停止
# @enum 4 -> 缓速运动(同运动区分)
int8                  motion_status

# @brief 障碍物类别
# @enum 0 -> 完全未知类型
# @enum 1 -> 运动未知障碍物
# @enum 2 -> 静止未知障碍物
# @enum 3 -> 行人
# @enum 4 -> 非机动车/摩托车/骑行者
# @enum 5 -> 车辆
int8                  obstacle_category

# @brief 障碍物子类别
# @enum 0 -> 完全未知类型
# @enum 1 -> 运动未知障碍物
# @enum 2 -> 静止未知障碍物
# @enum 5 -> 未知行人类型
# @enum 6 -> 行人(成人)
# @enum 7 -> 行人(儿童)
# @enum 10 -> 自行车骑行者
# @enum 11 -> 摩托车骑行者
# @enum 12 -> 三轮车骑行者
# @enum 14 -> 自行车
# @enum 15 -> 摩托车
# @enum 16 -> 三轮车
# @enum 18 -> 未知车辆类型
# @enum 19 -> 轿车
# @enum 20 -> 巴士/客车
# @enum 21 -> 面包车/小型货车
# @enum 22 -> 卡车
# @enum 23 -> 拖挂车
# @enum 24 -> 特殊车辆
# @enum 30 -> 锥桶
# @enum 31 -> 防撞柱
# @enum 32 -> 水马
# @enum 33 -> 交通标牌
# @enum 34 -> 警示标志
# @enum 35 -> 三角牌
# @enum 36 -> 抬杆
int8                  obstacle_subcategory

# @brief 感知输出车辆尾灯状态
# @details
VehicleLightStatus    vehicle_light_status

# @brief 感知输出行人状态
# @details
PedStatus             ped_status

# @brief 感知输出车辆车道关联状态
# @details
VehicleInLaneStatus   vehicle_in_lane_status

# @brief 感知输出追踪历史信息
# @details
# @
TrackletInfo[]        tracklet_info

# @brief 有效状态位
# @details 当前帧感知结果是否有效
# @enum 0 -> 无效
# @enum 1 -> 有效
int8                  valid_flag

