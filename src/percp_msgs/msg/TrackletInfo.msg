# --- Percp TrackletInfo ---
# @brief 追踪轨迹片段历史信息
# @details 东北天系

# @brief 双浮点时间戳
# @details 感知障碍物对应真实物理世界的时间(近似为传感器时间)
# @unit s(至少精确至ms)
float64 timestamp

# @brief 是否为检测器直接输出
# @enum 0 -> 跟踪器预测
# @enum 1 -> 检测器直接输出
int8 is_detected

# @brief 障碍物位置姿态及包围盒
# @details 全局坐标系(东北天)
# @unit m rad
Point64 position
float64 yaw
float64 height
Polygon64 bbox_bev
Polygon64 polygon_bev

# @brief 速度/加速度/横摆角速度
# @unit m/s m/s^2 rad/s
Point32 velocity
float32 yaw_rate

