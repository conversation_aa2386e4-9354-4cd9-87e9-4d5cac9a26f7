
# --- JsonMsgSchema ---
# @brief 以感知障碍物输出
# @details

# @brief 双浮点时间戳
# @details 感知障碍物对应真实物理世界的时间(近似为传感器时间)
# @unit s(至少精确至ms)
float64    timestamp

# @brief 感知障碍物元信息
# @details 包含pipeline起止时间及上游各输入时间戳
Meta       meta

# @brief 感知障碍物列表
# @details
# @todo 考虑最大障碍物数量下
Obstacle[] obstacles

# @brief 感知结果补充信息
# @details
ANCInfo    anc_info

# @brief 有效状态位
# @details 当前帧感知结果是否有效
# @enum 0 -> INVALID 无效
# @enum 1 -> VALID 有效
int8       valid_flag

