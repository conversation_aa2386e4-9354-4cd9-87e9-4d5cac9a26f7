# --- Percp VehicleInLaneStatus ---
# @brief 车辆车道关联状态
# @details

# @brief CIPV有效状态位
# @details CIPV(本车道前向最近车)
# @enum 0 -> 无效
# @enum 1 -> 有效
int8 cipv_flag

# @brief 车道分配
# @enum 0 -> 未知车道
# @enum 1 -> 左左车道
# @enum 2 -> 左车道
# @enum 3 -> 主车道
# @enum 4 -> 右车道
# @enum 5 -> 右右车道
int8 lane_assignment

# @brief 车道相关行为
# @enum 0 -> 未见输出
# @enum 1 -> 未定义
# @enum 2 -> 自车左右相邻车道车辆同向经过，相对位置基本保持不变
# @enum 3 -> 自车左右相邻车道车辆同向经过，自车被超车
# @enum 4 -> 自车左右相邻车道车辆同向经过，自车超车
# @enum 5 -> 从左右相邻车道进入自车车道的过程
# @enum 6 -> 从自车所在车道移出的过程
# @enum 7 -> 横向穿越或者与本车呈一定角度
# @enum 8 -> Left Turn Across Path
# @enum 9 -> Right Turn Across Path
# @enum 10 -> 同向直行的车辆
# @enum 11 -> 迎面驶来的车辆
int8 motion_category

