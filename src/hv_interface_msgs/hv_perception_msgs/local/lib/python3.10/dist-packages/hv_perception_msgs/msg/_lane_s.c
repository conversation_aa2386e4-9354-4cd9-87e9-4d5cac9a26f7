// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/Lane.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/lane__struct.h"
#include "hv_perception_msgs/msg/detail/lane__functions.h"

#include "rosidl_runtime_c/string.h"
#include "rosidl_runtime_c/string_functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "hv_perception_msgs/msg/detail/lane_arrow__functions.h"
#include "hv_perception_msgs/msg/detail/other_land_mark__functions.h"
// end nested array functions include
ROSIDL_GENERATOR_C_IMPORT
bool hv_common_msgs__msg__polyline2d__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * hv_common_msgs__msg__polyline2d__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__lane_arrow__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__lane_arrow__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__other_land_mark__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__other_land_mark__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__lane__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[34];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._lane.Lane", full_classname_dest, 33) == 0);
  }
  hv_perception_msgs__msg__Lane * ros_message = _ros_message;
  {  // detection_timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "detection_timestamp");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->detection_timestamp = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lane_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lane_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lane_type
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_type");
    if (!field) {
      return false;
    }
    assert(PyUnicode_Check(field));
    PyObject * encoded_field = PyUnicode_AsUTF8String(field);
    if (!encoded_field) {
      Py_DECREF(field);
      return false;
    }
    rosidl_runtime_c__String__assign(&ros_message->lane_type, PyBytes_AS_STRING(encoded_field));
    Py_DECREF(encoded_field);
    Py_DECREF(field);
  }
  {  // lane_direction
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_direction");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lane_direction = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // confidence
    PyObject * field = PyObject_GetAttrString(_pymsg, "confidence");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->confidence = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // center_line
    PyObject * field = PyObject_GetAttrString(_pymsg, "center_line");
    if (!field) {
      return false;
    }
    if (!hv_common_msgs__msg__polyline2d__convert_from_py(field, &ros_message->center_line)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // lane_width
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_width");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lane_width = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // left_line_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "left_line_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->left_line_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // right_line_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "right_line_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->right_line_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // left_curb_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "left_curb_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->left_curb_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // right_curb_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "right_curb_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->right_curb_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lane_arrows
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_arrows");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'lane_arrows'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__LaneArrow__Sequence__init(&(ros_message->lane_arrows), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__LaneArrow__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__LaneArrow * dest = ros_message->lane_arrows.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__lane_arrow__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // other_land_marks
    PyObject * field = PyObject_GetAttrString(_pymsg, "other_land_marks");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'other_land_marks'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__OtherLandMark__Sequence__init(&(ros_message->other_land_marks), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__OtherLandMark__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__OtherLandMark * dest = ros_message->other_land_marks.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__other_land_mark__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // speed_limit
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed_limit");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->speed_limit = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // is_valid
    PyObject * field = PyObject_GetAttrString(_pymsg, "is_valid");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->is_valid = (Py_True == field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__lane__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Lane */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._lane");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Lane");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__Lane * ros_message = (hv_perception_msgs__msg__Lane *)raw_ros_message;
  {  // detection_timestamp
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->detection_timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "detection_timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lane_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_type
    PyObject * field = NULL;
    field = PyUnicode_DecodeUTF8(
      ros_message->lane_type.data,
      strlen(ros_message->lane_type.data),
      "replace");
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_type", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_direction
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lane_direction);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_direction", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // confidence
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->confidence);
    {
      int rc = PyObject_SetAttrString(_pymessage, "confidence", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // center_line
    PyObject * field = NULL;
    field = hv_common_msgs__msg__polyline2d__convert_to_py(&ros_message->center_line);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "center_line", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_width
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lane_width);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_width", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // left_line_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->left_line_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "left_line_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // right_line_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->right_line_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "right_line_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // left_curb_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->left_curb_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "left_curb_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // right_curb_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->right_curb_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "right_curb_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_arrows
    PyObject * field = NULL;
    size_t size = ros_message->lane_arrows.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__LaneArrow * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->lane_arrows.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__lane_arrow__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_arrows", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // other_land_marks
    PyObject * field = NULL;
    size_t size = ros_message->other_land_marks.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__OtherLandMark * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->other_land_marks.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__other_land_mark__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "other_land_marks", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed_limit
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->speed_limit);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed_limit", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // is_valid
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->is_valid ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "is_valid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
