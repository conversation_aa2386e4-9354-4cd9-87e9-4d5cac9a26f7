# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/Animal.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Animal(type):
    """Metaclass of message 'Animal'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.Animal')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__animal
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__animal
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__animal
            cls._TYPE_SUPPORT = module.type_support_msg__msg__animal
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__animal

            from hv_perception_msgs.msg import BaseObstacle
            if BaseObstacle.__class__._TYPE_SUPPORT is None:
                BaseObstacle.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Animal(metaclass=Metaclass_Animal):
    """Message class 'Animal'."""

    __slots__ = [
        '_base',
        '_animal_classification',
        '_animal_behavior',
        '_animal_size',
    ]

    _fields_and_field_types = {
        'base': 'hv_perception_msgs/BaseObstacle',
        'animal_classification': 'uint8',
        'animal_behavior': 'uint8',
        'animal_size': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'BaseObstacle'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from hv_perception_msgs.msg import BaseObstacle
        self.base = kwargs.get('base', BaseObstacle())
        self.animal_classification = kwargs.get('animal_classification', int())
        self.animal_behavior = kwargs.get('animal_behavior', int())
        self.animal_size = kwargs.get('animal_size', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.base != other.base:
            return False
        if self.animal_classification != other.animal_classification:
            return False
        if self.animal_behavior != other.animal_behavior:
            return False
        if self.animal_size != other.animal_size:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def base(self):
        """Message field 'base'."""
        return self._base

    @base.setter
    def base(self, value):
        if __debug__:
            from hv_perception_msgs.msg import BaseObstacle
            assert \
                isinstance(value, BaseObstacle), \
                "The 'base' field must be a sub message of type 'BaseObstacle'"
        self._base = value

    @builtins.property
    def animal_classification(self):
        """Message field 'animal_classification'."""
        return self._animal_classification

    @animal_classification.setter
    def animal_classification(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'animal_classification' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'animal_classification' field must be an unsigned integer in [0, 255]"
        self._animal_classification = value

    @builtins.property
    def animal_behavior(self):
        """Message field 'animal_behavior'."""
        return self._animal_behavior

    @animal_behavior.setter
    def animal_behavior(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'animal_behavior' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'animal_behavior' field must be an unsigned integer in [0, 255]"
        self._animal_behavior = value

    @builtins.property
    def animal_size(self):
        """Message field 'animal_size'."""
        return self._animal_size

    @animal_size.setter
    def animal_size(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'animal_size' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'animal_size' field must be an unsigned integer in [0, 255]"
        self._animal_size = value
