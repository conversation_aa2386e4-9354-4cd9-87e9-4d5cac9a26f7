# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/Junction.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'traffic_light_ids'
# Member 'traffic_sign_ids'
# Member 'stop_line_ids'
# Member 'crosswalk_ids'
# Member 'laneline_ids'
# Member 'curb_ids'
# Member 'occupancy_ids'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Junction(type):
    """Metaclass of message 'Junction'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.Junction')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__junction
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__junction
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__junction
            cls._TYPE_SUPPORT = module.type_support_msg__msg__junction
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__junction

            from hv_common_msgs.msg import Point2d
            if Point2d.__class__._TYPE_SUPPORT is None:
                Point2d.__class__.__import_type_support__()

            from hv_perception_msgs.msg import JunctionConnection
            if JunctionConnection.__class__._TYPE_SUPPORT is None:
                JunctionConnection.__class__.__import_type_support__()

            from hv_perception_msgs.msg import WaitingAreaLane
            if WaitingAreaLane.__class__._TYPE_SUPPORT is None:
                WaitingAreaLane.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Junction(metaclass=Metaclass_Junction):
    """Message class 'Junction'."""

    __slots__ = [
        '_detection_timestamp',
        '_junction_id',
        '_junction_type',
        '_confidence',
        '_center_position',
        '_junction_length',
        '_junction_width',
        '_junction_heading',
        '_distance',
        '_junction_connections',
        '_traffic_light_ids',
        '_traffic_sign_ids',
        '_stop_line_ids',
        '_crosswalk_ids',
        '_waiting_area_lanes',
        '_laneline_ids',
        '_curb_ids',
        '_occupancy_ids',
        '_is_valid',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'double',
        'junction_id': 'uint32',
        'junction_type': 'uint8',
        'confidence': 'uint8',
        'center_position': 'hv_common_msgs/Point2d',
        'junction_length': 'float',
        'junction_width': 'float',
        'junction_heading': 'float',
        'distance': 'float',
        'junction_connections': 'sequence<hv_perception_msgs/JunctionConnection>',
        'traffic_light_ids': 'sequence<uint32>',
        'traffic_sign_ids': 'sequence<uint32>',
        'stop_line_ids': 'sequence<uint32>',
        'crosswalk_ids': 'sequence<uint32>',
        'waiting_area_lanes': 'sequence<hv_perception_msgs/WaitingAreaLane>',
        'laneline_ids': 'sequence<uint32>',
        'curb_ids': 'sequence<uint32>',
        'occupancy_ids': 'sequence<uint32>',
        'is_valid': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point2d'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'JunctionConnection')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'WaitingAreaLane')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', float())
        self.junction_id = kwargs.get('junction_id', int())
        self.junction_type = kwargs.get('junction_type', int())
        self.confidence = kwargs.get('confidence', int())
        from hv_common_msgs.msg import Point2d
        self.center_position = kwargs.get('center_position', Point2d())
        self.junction_length = kwargs.get('junction_length', float())
        self.junction_width = kwargs.get('junction_width', float())
        self.junction_heading = kwargs.get('junction_heading', float())
        self.distance = kwargs.get('distance', float())
        self.junction_connections = kwargs.get('junction_connections', [])
        self.traffic_light_ids = array.array('I', kwargs.get('traffic_light_ids', []))
        self.traffic_sign_ids = array.array('I', kwargs.get('traffic_sign_ids', []))
        self.stop_line_ids = array.array('I', kwargs.get('stop_line_ids', []))
        self.crosswalk_ids = array.array('I', kwargs.get('crosswalk_ids', []))
        self.waiting_area_lanes = kwargs.get('waiting_area_lanes', [])
        self.laneline_ids = array.array('I', kwargs.get('laneline_ids', []))
        self.curb_ids = array.array('I', kwargs.get('curb_ids', []))
        self.occupancy_ids = array.array('I', kwargs.get('occupancy_ids', []))
        self.is_valid = kwargs.get('is_valid', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.junction_id != other.junction_id:
            return False
        if self.junction_type != other.junction_type:
            return False
        if self.confidence != other.confidence:
            return False
        if self.center_position != other.center_position:
            return False
        if self.junction_length != other.junction_length:
            return False
        if self.junction_width != other.junction_width:
            return False
        if self.junction_heading != other.junction_heading:
            return False
        if self.distance != other.distance:
            return False
        if self.junction_connections != other.junction_connections:
            return False
        if self.traffic_light_ids != other.traffic_light_ids:
            return False
        if self.traffic_sign_ids != other.traffic_sign_ids:
            return False
        if self.stop_line_ids != other.stop_line_ids:
            return False
        if self.crosswalk_ids != other.crosswalk_ids:
            return False
        if self.waiting_area_lanes != other.waiting_area_lanes:
            return False
        if self.laneline_ids != other.laneline_ids:
            return False
        if self.curb_ids != other.curb_ids:
            return False
        if self.occupancy_ids != other.occupancy_ids:
            return False
        if self.is_valid != other.is_valid:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'detection_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'detection_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._detection_timestamp = value

    @builtins.property
    def junction_id(self):
        """Message field 'junction_id'."""
        return self._junction_id

    @junction_id.setter
    def junction_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'junction_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'junction_id' field must be an unsigned integer in [0, 4294967295]"
        self._junction_id = value

    @builtins.property
    def junction_type(self):
        """Message field 'junction_type'."""
        return self._junction_type

    @junction_type.setter
    def junction_type(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'junction_type' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'junction_type' field must be an unsigned integer in [0, 255]"
        self._junction_type = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def center_position(self):
        """Message field 'center_position'."""
        return self._center_position

    @center_position.setter
    def center_position(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point2d
            assert \
                isinstance(value, Point2d), \
                "The 'center_position' field must be a sub message of type 'Point2d'"
        self._center_position = value

    @builtins.property
    def junction_length(self):
        """Message field 'junction_length'."""
        return self._junction_length

    @junction_length.setter
    def junction_length(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'junction_length' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'junction_length' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._junction_length = value

    @builtins.property
    def junction_width(self):
        """Message field 'junction_width'."""
        return self._junction_width

    @junction_width.setter
    def junction_width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'junction_width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'junction_width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._junction_width = value

    @builtins.property
    def junction_heading(self):
        """Message field 'junction_heading'."""
        return self._junction_heading

    @junction_heading.setter
    def junction_heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'junction_heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'junction_heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._junction_heading = value

    @builtins.property
    def distance(self):
        """Message field 'distance'."""
        return self._distance

    @distance.setter
    def distance(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'distance' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'distance' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._distance = value

    @builtins.property
    def junction_connections(self):
        """Message field 'junction_connections'."""
        return self._junction_connections

    @junction_connections.setter
    def junction_connections(self, value):
        if __debug__:
            from hv_perception_msgs.msg import JunctionConnection
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, JunctionConnection) for v in value) and
                 True), \
                "The 'junction_connections' field must be a set or sequence and each value of type 'JunctionConnection'"
        self._junction_connections = value

    @builtins.property
    def traffic_light_ids(self):
        """Message field 'traffic_light_ids'."""
        return self._traffic_light_ids

    @traffic_light_ids.setter
    def traffic_light_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'traffic_light_ids' array.array() must have the type code of 'I'"
            self._traffic_light_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'traffic_light_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._traffic_light_ids = array.array('I', value)

    @builtins.property
    def traffic_sign_ids(self):
        """Message field 'traffic_sign_ids'."""
        return self._traffic_sign_ids

    @traffic_sign_ids.setter
    def traffic_sign_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'traffic_sign_ids' array.array() must have the type code of 'I'"
            self._traffic_sign_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'traffic_sign_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._traffic_sign_ids = array.array('I', value)

    @builtins.property
    def stop_line_ids(self):
        """Message field 'stop_line_ids'."""
        return self._stop_line_ids

    @stop_line_ids.setter
    def stop_line_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'stop_line_ids' array.array() must have the type code of 'I'"
            self._stop_line_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'stop_line_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._stop_line_ids = array.array('I', value)

    @builtins.property
    def crosswalk_ids(self):
        """Message field 'crosswalk_ids'."""
        return self._crosswalk_ids

    @crosswalk_ids.setter
    def crosswalk_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'crosswalk_ids' array.array() must have the type code of 'I'"
            self._crosswalk_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'crosswalk_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._crosswalk_ids = array.array('I', value)

    @builtins.property
    def waiting_area_lanes(self):
        """Message field 'waiting_area_lanes'."""
        return self._waiting_area_lanes

    @waiting_area_lanes.setter
    def waiting_area_lanes(self, value):
        if __debug__:
            from hv_perception_msgs.msg import WaitingAreaLane
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, WaitingAreaLane) for v in value) and
                 True), \
                "The 'waiting_area_lanes' field must be a set or sequence and each value of type 'WaitingAreaLane'"
        self._waiting_area_lanes = value

    @builtins.property
    def laneline_ids(self):
        """Message field 'laneline_ids'."""
        return self._laneline_ids

    @laneline_ids.setter
    def laneline_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'laneline_ids' array.array() must have the type code of 'I'"
            self._laneline_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'laneline_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._laneline_ids = array.array('I', value)

    @builtins.property
    def curb_ids(self):
        """Message field 'curb_ids'."""
        return self._curb_ids

    @curb_ids.setter
    def curb_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'curb_ids' array.array() must have the type code of 'I'"
            self._curb_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'curb_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._curb_ids = array.array('I', value)

    @builtins.property
    def occupancy_ids(self):
        """Message field 'occupancy_ids'."""
        return self._occupancy_ids

    @occupancy_ids.setter
    def occupancy_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'occupancy_ids' array.array() must have the type code of 'I'"
            self._occupancy_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'occupancy_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._occupancy_ids = array.array('I', value)

    @builtins.property
    def is_valid(self):
        """Message field 'is_valid'."""
        return self._is_valid

    @is_valid.setter
    def is_valid(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_valid' field must be of type 'bool'"
        self._is_valid = value
