# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/Lane.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Lane(type):
    """Metaclass of message 'Lane'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.Lane')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__lane
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__lane
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__lane
            cls._TYPE_SUPPORT = module.type_support_msg__msg__lane
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__lane

            from hv_common_msgs.msg import Polyline2d
            if Polyline2d.__class__._TYPE_SUPPORT is None:
                Polyline2d.__class__.__import_type_support__()

            from hv_perception_msgs.msg import LaneArrow
            if LaneArrow.__class__._TYPE_SUPPORT is None:
                LaneArrow.__class__.__import_type_support__()

            from hv_perception_msgs.msg import OtherLandMark
            if OtherLandMark.__class__._TYPE_SUPPORT is None:
                OtherLandMark.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Lane(metaclass=Metaclass_Lane):
    """Message class 'Lane'."""

    __slots__ = [
        '_detection_timestamp',
        '_lane_id',
        '_lane_type',
        '_lane_direction',
        '_confidence',
        '_center_line',
        '_lane_width',
        '_left_line_id',
        '_right_line_id',
        '_left_curb_id',
        '_right_curb_id',
        '_lane_arrows',
        '_other_land_marks',
        '_speed_limit',
        '_is_valid',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'double',
        'lane_id': 'uint32',
        'lane_type': 'string',
        'lane_direction': 'uint8',
        'confidence': 'uint8',
        'center_line': 'hv_common_msgs/Polyline2d',
        'lane_width': 'float',
        'left_line_id': 'uint32',
        'right_line_id': 'uint32',
        'left_curb_id': 'uint32',
        'right_curb_id': 'uint32',
        'lane_arrows': 'sequence<hv_perception_msgs/LaneArrow>',
        'other_land_marks': 'sequence<hv_perception_msgs/OtherLandMark>',
        'speed_limit': 'uint8',
        'is_valid': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Polyline2d'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'LaneArrow')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'OtherLandMark')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', float())
        self.lane_id = kwargs.get('lane_id', int())
        self.lane_type = kwargs.get('lane_type', str())
        self.lane_direction = kwargs.get('lane_direction', int())
        self.confidence = kwargs.get('confidence', int())
        from hv_common_msgs.msg import Polyline2d
        self.center_line = kwargs.get('center_line', Polyline2d())
        self.lane_width = kwargs.get('lane_width', float())
        self.left_line_id = kwargs.get('left_line_id', int())
        self.right_line_id = kwargs.get('right_line_id', int())
        self.left_curb_id = kwargs.get('left_curb_id', int())
        self.right_curb_id = kwargs.get('right_curb_id', int())
        self.lane_arrows = kwargs.get('lane_arrows', [])
        self.other_land_marks = kwargs.get('other_land_marks', [])
        self.speed_limit = kwargs.get('speed_limit', int())
        self.is_valid = kwargs.get('is_valid', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.lane_id != other.lane_id:
            return False
        if self.lane_type != other.lane_type:
            return False
        if self.lane_direction != other.lane_direction:
            return False
        if self.confidence != other.confidence:
            return False
        if self.center_line != other.center_line:
            return False
        if self.lane_width != other.lane_width:
            return False
        if self.left_line_id != other.left_line_id:
            return False
        if self.right_line_id != other.right_line_id:
            return False
        if self.left_curb_id != other.left_curb_id:
            return False
        if self.right_curb_id != other.right_curb_id:
            return False
        if self.lane_arrows != other.lane_arrows:
            return False
        if self.other_land_marks != other.other_land_marks:
            return False
        if self.speed_limit != other.speed_limit:
            return False
        if self.is_valid != other.is_valid:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'detection_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'detection_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._detection_timestamp = value

    @builtins.property
    def lane_id(self):
        """Message field 'lane_id'."""
        return self._lane_id

    @lane_id.setter
    def lane_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lane_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'lane_id' field must be an unsigned integer in [0, 4294967295]"
        self._lane_id = value

    @builtins.property
    def lane_type(self):
        """Message field 'lane_type'."""
        return self._lane_type

    @lane_type.setter
    def lane_type(self, value):
        if __debug__:
            assert \
                isinstance(value, str), \
                "The 'lane_type' field must be of type 'str'"
        self._lane_type = value

    @builtins.property
    def lane_direction(self):
        """Message field 'lane_direction'."""
        return self._lane_direction

    @lane_direction.setter
    def lane_direction(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lane_direction' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lane_direction' field must be an unsigned integer in [0, 255]"
        self._lane_direction = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def center_line(self):
        """Message field 'center_line'."""
        return self._center_line

    @center_line.setter
    def center_line(self, value):
        if __debug__:
            from hv_common_msgs.msg import Polyline2d
            assert \
                isinstance(value, Polyline2d), \
                "The 'center_line' field must be a sub message of type 'Polyline2d'"
        self._center_line = value

    @builtins.property
    def lane_width(self):
        """Message field 'lane_width'."""
        return self._lane_width

    @lane_width.setter
    def lane_width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lane_width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lane_width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lane_width = value

    @builtins.property
    def left_line_id(self):
        """Message field 'left_line_id'."""
        return self._left_line_id

    @left_line_id.setter
    def left_line_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'left_line_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'left_line_id' field must be an unsigned integer in [0, 4294967295]"
        self._left_line_id = value

    @builtins.property
    def right_line_id(self):
        """Message field 'right_line_id'."""
        return self._right_line_id

    @right_line_id.setter
    def right_line_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'right_line_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'right_line_id' field must be an unsigned integer in [0, 4294967295]"
        self._right_line_id = value

    @builtins.property
    def left_curb_id(self):
        """Message field 'left_curb_id'."""
        return self._left_curb_id

    @left_curb_id.setter
    def left_curb_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'left_curb_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'left_curb_id' field must be an unsigned integer in [0, 4294967295]"
        self._left_curb_id = value

    @builtins.property
    def right_curb_id(self):
        """Message field 'right_curb_id'."""
        return self._right_curb_id

    @right_curb_id.setter
    def right_curb_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'right_curb_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'right_curb_id' field must be an unsigned integer in [0, 4294967295]"
        self._right_curb_id = value

    @builtins.property
    def lane_arrows(self):
        """Message field 'lane_arrows'."""
        return self._lane_arrows

    @lane_arrows.setter
    def lane_arrows(self, value):
        if __debug__:
            from hv_perception_msgs.msg import LaneArrow
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, LaneArrow) for v in value) and
                 True), \
                "The 'lane_arrows' field must be a set or sequence and each value of type 'LaneArrow'"
        self._lane_arrows = value

    @builtins.property
    def other_land_marks(self):
        """Message field 'other_land_marks'."""
        return self._other_land_marks

    @other_land_marks.setter
    def other_land_marks(self, value):
        if __debug__:
            from hv_perception_msgs.msg import OtherLandMark
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, OtherLandMark) for v in value) and
                 True), \
                "The 'other_land_marks' field must be a set or sequence and each value of type 'OtherLandMark'"
        self._other_land_marks = value

    @builtins.property
    def speed_limit(self):
        """Message field 'speed_limit'."""
        return self._speed_limit

    @speed_limit.setter
    def speed_limit(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'speed_limit' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'speed_limit' field must be an unsigned integer in [0, 255]"
        self._speed_limit = value

    @builtins.property
    def is_valid(self):
        """Message field 'is_valid'."""
        return self._is_valid

    @is_valid.setter
    def is_valid(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_valid' field must be of type 'bool'"
        self._is_valid = value
