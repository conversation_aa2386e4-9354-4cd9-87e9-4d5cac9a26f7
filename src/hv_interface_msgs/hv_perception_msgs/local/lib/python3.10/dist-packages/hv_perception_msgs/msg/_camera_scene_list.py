# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/CameraSceneList.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_CameraSceneList(type):
    """Metaclass of message 'CameraSceneList'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.CameraSceneList')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__camera_scene_list
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__camera_scene_list
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__camera_scene_list
            cls._TYPE_SUPPORT = module.type_support_msg__msg__camera_scene_list
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__camera_scene_list

            from hv_common_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Crosswalk
            if Crosswalk.__class__._TYPE_SUPPORT is None:
                Crosswalk.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Curb
            if Curb.__class__._TYPE_SUPPORT is None:
                Curb.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Junction
            if Junction.__class__._TYPE_SUPPORT is None:
                Junction.__class__.__import_type_support__()

            from hv_perception_msgs.msg import KeepClearArea
            if KeepClearArea.__class__._TYPE_SUPPORT is None:
                KeepClearArea.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Lane
            if Lane.__class__._TYPE_SUPPORT is None:
                Lane.__class__.__import_type_support__()

            from hv_perception_msgs.msg import LaneLine
            if LaneLine.__class__._TYPE_SUPPORT is None:
                LaneLine.__class__.__import_type_support__()

            from hv_perception_msgs.msg import ParkingSpace
            if ParkingSpace.__class__._TYPE_SUPPORT is None:
                ParkingSpace.__class__.__import_type_support__()

            from hv_perception_msgs.msg import PerceptionHeader
            if PerceptionHeader.__class__._TYPE_SUPPORT is None:
                PerceptionHeader.__class__.__import_type_support__()

            from hv_perception_msgs.msg import SpeedBump
            if SpeedBump.__class__._TYPE_SUPPORT is None:
                SpeedBump.__class__.__import_type_support__()

            from hv_perception_msgs.msg import StopLine
            if StopLine.__class__._TYPE_SUPPORT is None:
                StopLine.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class CameraSceneList(metaclass=Metaclass_CameraSceneList):
    """Message class 'CameraSceneList'."""

    __slots__ = [
        '_header',
        '_meta_header',
        '_lane_lines',
        '_lanes',
        '_curbs',
        '_stop_lines',
        '_crosswalks',
        '_parking_spaces',
        '_speed_bumps',
        '_keep_clear_areas',
        '_junctions',
    ]

    _fields_and_field_types = {
        'header': 'hv_common_msgs/Header',
        'meta_header': 'hv_perception_msgs/PerceptionHeader',
        'lane_lines': 'sequence<hv_perception_msgs/LaneLine>',
        'lanes': 'sequence<hv_perception_msgs/Lane>',
        'curbs': 'sequence<hv_perception_msgs/Curb>',
        'stop_lines': 'sequence<hv_perception_msgs/StopLine>',
        'crosswalks': 'sequence<hv_perception_msgs/Crosswalk>',
        'parking_spaces': 'sequence<hv_perception_msgs/ParkingSpace>',
        'speed_bumps': 'sequence<hv_perception_msgs/SpeedBump>',
        'keep_clear_areas': 'sequence<hv_perception_msgs/KeepClearArea>',
        'junctions': 'sequence<hv_perception_msgs/Junction>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'PerceptionHeader'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'LaneLine')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Lane')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Curb')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'StopLine')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Crosswalk')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'ParkingSpace')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'SpeedBump')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'KeepClearArea')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Junction')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from hv_common_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        from hv_perception_msgs.msg import PerceptionHeader
        self.meta_header = kwargs.get('meta_header', PerceptionHeader())
        self.lane_lines = kwargs.get('lane_lines', [])
        self.lanes = kwargs.get('lanes', [])
        self.curbs = kwargs.get('curbs', [])
        self.stop_lines = kwargs.get('stop_lines', [])
        self.crosswalks = kwargs.get('crosswalks', [])
        self.parking_spaces = kwargs.get('parking_spaces', [])
        self.speed_bumps = kwargs.get('speed_bumps', [])
        self.keep_clear_areas = kwargs.get('keep_clear_areas', [])
        self.junctions = kwargs.get('junctions', [])

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.meta_header != other.meta_header:
            return False
        if self.lane_lines != other.lane_lines:
            return False
        if self.lanes != other.lanes:
            return False
        if self.curbs != other.curbs:
            return False
        if self.stop_lines != other.stop_lines:
            return False
        if self.crosswalks != other.crosswalks:
            return False
        if self.parking_spaces != other.parking_spaces:
            return False
        if self.speed_bumps != other.speed_bumps:
            return False
        if self.keep_clear_areas != other.keep_clear_areas:
            return False
        if self.junctions != other.junctions:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if __debug__:
            from hv_common_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def meta_header(self):
        """Message field 'meta_header'."""
        return self._meta_header

    @meta_header.setter
    def meta_header(self, value):
        if __debug__:
            from hv_perception_msgs.msg import PerceptionHeader
            assert \
                isinstance(value, PerceptionHeader), \
                "The 'meta_header' field must be a sub message of type 'PerceptionHeader'"
        self._meta_header = value

    @builtins.property
    def lane_lines(self):
        """Message field 'lane_lines'."""
        return self._lane_lines

    @lane_lines.setter
    def lane_lines(self, value):
        if __debug__:
            from hv_perception_msgs.msg import LaneLine
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, LaneLine) for v in value) and
                 True), \
                "The 'lane_lines' field must be a set or sequence and each value of type 'LaneLine'"
        self._lane_lines = value

    @builtins.property
    def lanes(self):
        """Message field 'lanes'."""
        return self._lanes

    @lanes.setter
    def lanes(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Lane
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Lane) for v in value) and
                 True), \
                "The 'lanes' field must be a set or sequence and each value of type 'Lane'"
        self._lanes = value

    @builtins.property
    def curbs(self):
        """Message field 'curbs'."""
        return self._curbs

    @curbs.setter
    def curbs(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Curb
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Curb) for v in value) and
                 True), \
                "The 'curbs' field must be a set or sequence and each value of type 'Curb'"
        self._curbs = value

    @builtins.property
    def stop_lines(self):
        """Message field 'stop_lines'."""
        return self._stop_lines

    @stop_lines.setter
    def stop_lines(self, value):
        if __debug__:
            from hv_perception_msgs.msg import StopLine
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, StopLine) for v in value) and
                 True), \
                "The 'stop_lines' field must be a set or sequence and each value of type 'StopLine'"
        self._stop_lines = value

    @builtins.property
    def crosswalks(self):
        """Message field 'crosswalks'."""
        return self._crosswalks

    @crosswalks.setter
    def crosswalks(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Crosswalk
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Crosswalk) for v in value) and
                 True), \
                "The 'crosswalks' field must be a set or sequence and each value of type 'Crosswalk'"
        self._crosswalks = value

    @builtins.property
    def parking_spaces(self):
        """Message field 'parking_spaces'."""
        return self._parking_spaces

    @parking_spaces.setter
    def parking_spaces(self, value):
        if __debug__:
            from hv_perception_msgs.msg import ParkingSpace
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, ParkingSpace) for v in value) and
                 True), \
                "The 'parking_spaces' field must be a set or sequence and each value of type 'ParkingSpace'"
        self._parking_spaces = value

    @builtins.property
    def speed_bumps(self):
        """Message field 'speed_bumps'."""
        return self._speed_bumps

    @speed_bumps.setter
    def speed_bumps(self, value):
        if __debug__:
            from hv_perception_msgs.msg import SpeedBump
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, SpeedBump) for v in value) and
                 True), \
                "The 'speed_bumps' field must be a set or sequence and each value of type 'SpeedBump'"
        self._speed_bumps = value

    @builtins.property
    def keep_clear_areas(self):
        """Message field 'keep_clear_areas'."""
        return self._keep_clear_areas

    @keep_clear_areas.setter
    def keep_clear_areas(self, value):
        if __debug__:
            from hv_perception_msgs.msg import KeepClearArea
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, KeepClearArea) for v in value) and
                 True), \
                "The 'keep_clear_areas' field must be a set or sequence and each value of type 'KeepClearArea'"
        self._keep_clear_areas = value

    @builtins.property
    def junctions(self):
        """Message field 'junctions'."""
        return self._junctions

    @junctions.setter
    def junctions(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Junction
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Junction) for v in value) and
                 True), \
                "The 'junctions' field must be a set or sequence and each value of type 'Junction'"
        self._junctions = value
