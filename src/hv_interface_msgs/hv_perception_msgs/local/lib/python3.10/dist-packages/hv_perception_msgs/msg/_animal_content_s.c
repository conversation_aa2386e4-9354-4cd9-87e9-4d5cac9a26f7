// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/AnimalContent.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/animal_content__struct.h"
#include "hv_perception_msgs/msg/detail/animal_content__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__animal_content__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[53];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._animal_content.AnimalContent", full_classname_dest, 52) == 0);
  }
  hv_perception_msgs__msg__AnimalContent * ros_message = _ros_message;
  {  // animal_classification
    PyObject * field = PyObject_GetAttrString(_pymsg, "animal_classification");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->animal_classification = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // animal_behavior
    PyObject * field = PyObject_GetAttrString(_pymsg, "animal_behavior");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->animal_behavior = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // animal_size
    PyObject * field = PyObject_GetAttrString(_pymsg, "animal_size");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->animal_size = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__animal_content__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of AnimalContent */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._animal_content");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "AnimalContent");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__AnimalContent * ros_message = (hv_perception_msgs__msg__AnimalContent *)raw_ros_message;
  {  // animal_classification
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->animal_classification);
    {
      int rc = PyObject_SetAttrString(_pymessage, "animal_classification", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // animal_behavior
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->animal_behavior);
    {
      int rc = PyObject_SetAttrString(_pymessage, "animal_behavior", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // animal_size
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->animal_size);
    {
      int rc = PyObject_SetAttrString(_pymessage, "animal_size", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
