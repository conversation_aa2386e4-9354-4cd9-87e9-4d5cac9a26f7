// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/ImageQuality.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/image_quality__struct.h"
#include "hv_perception_msgs/msg/detail/image_quality__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__image_quality__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[51];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._image_quality.ImageQuality", full_classname_dest, 50) == 0);
  }
  hv_perception_msgs__msg__ImageQuality * ros_message = _ros_message;
  {  // image_quality_type
    PyObject * field = PyObject_GetAttrString(_pymsg, "image_quality_type");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->image_quality_type = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // image_quality_level
    PyObject * field = PyObject_GetAttrString(_pymsg, "image_quality_level");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->image_quality_level = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__image_quality__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of ImageQuality */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._image_quality");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "ImageQuality");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__ImageQuality * ros_message = (hv_perception_msgs__msg__ImageQuality *)raw_ros_message;
  {  // image_quality_type
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->image_quality_type);
    {
      int rc = PyObject_SetAttrString(_pymessage, "image_quality_type", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // image_quality_level
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->image_quality_level);
    {
      int rc = PyObject_SetAttrString(_pymessage, "image_quality_level", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
