# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/DynamicObstacleList.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_DynamicObstacleList(type):
    """Metaclass of message 'DynamicObstacleList'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.DynamicObstacleList')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__dynamic_obstacle_list
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__dynamic_obstacle_list
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__dynamic_obstacle_list
            cls._TYPE_SUPPORT = module.type_support_msg__msg__dynamic_obstacle_list
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__dynamic_obstacle_list

            from hv_common_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

            from hv_common_msgs.msg import MetaHeader
            if MetaHeader.__class__._TYPE_SUPPORT is None:
                MetaHeader.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Animal
            if Animal.__class__._TYPE_SUPPORT is None:
                Animal.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Bicycle
            if Bicycle.__class__._TYPE_SUPPORT is None:
                Bicycle.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Pedestrian
            if Pedestrian.__class__._TYPE_SUPPORT is None:
                Pedestrian.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Unknown
            if Unknown.__class__._TYPE_SUPPORT is None:
                Unknown.__class__.__import_type_support__()

            from hv_perception_msgs.msg import Vehicle
            if Vehicle.__class__._TYPE_SUPPORT is None:
                Vehicle.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class DynamicObstacleList(metaclass=Metaclass_DynamicObstacleList):
    """Message class 'DynamicObstacleList'."""

    __slots__ = [
        '_header',
        '_meta_header',
        '_vehicles',
        '_pedestrians',
        '_bicycles',
        '_animals',
        '_unknowns',
    ]

    _fields_and_field_types = {
        'header': 'hv_common_msgs/Header',
        'meta_header': 'hv_common_msgs/MetaHeader',
        'vehicles': 'sequence<hv_perception_msgs/Vehicle>',
        'pedestrians': 'sequence<hv_perception_msgs/Pedestrian>',
        'bicycles': 'sequence<hv_perception_msgs/Bicycle>',
        'animals': 'sequence<hv_perception_msgs/Animal>',
        'unknowns': 'sequence<hv_perception_msgs/Unknown>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'MetaHeader'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Vehicle')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Pedestrian')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Bicycle')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Animal')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'Unknown')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from hv_common_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        from hv_common_msgs.msg import MetaHeader
        self.meta_header = kwargs.get('meta_header', MetaHeader())
        self.vehicles = kwargs.get('vehicles', [])
        self.pedestrians = kwargs.get('pedestrians', [])
        self.bicycles = kwargs.get('bicycles', [])
        self.animals = kwargs.get('animals', [])
        self.unknowns = kwargs.get('unknowns', [])

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.meta_header != other.meta_header:
            return False
        if self.vehicles != other.vehicles:
            return False
        if self.pedestrians != other.pedestrians:
            return False
        if self.bicycles != other.bicycles:
            return False
        if self.animals != other.animals:
            return False
        if self.unknowns != other.unknowns:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if __debug__:
            from hv_common_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def meta_header(self):
        """Message field 'meta_header'."""
        return self._meta_header

    @meta_header.setter
    def meta_header(self, value):
        if __debug__:
            from hv_common_msgs.msg import MetaHeader
            assert \
                isinstance(value, MetaHeader), \
                "The 'meta_header' field must be a sub message of type 'MetaHeader'"
        self._meta_header = value

    @builtins.property
    def vehicles(self):
        """Message field 'vehicles'."""
        return self._vehicles

    @vehicles.setter
    def vehicles(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Vehicle
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Vehicle) for v in value) and
                 True), \
                "The 'vehicles' field must be a set or sequence and each value of type 'Vehicle'"
        self._vehicles = value

    @builtins.property
    def pedestrians(self):
        """Message field 'pedestrians'."""
        return self._pedestrians

    @pedestrians.setter
    def pedestrians(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Pedestrian
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Pedestrian) for v in value) and
                 True), \
                "The 'pedestrians' field must be a set or sequence and each value of type 'Pedestrian'"
        self._pedestrians = value

    @builtins.property
    def bicycles(self):
        """Message field 'bicycles'."""
        return self._bicycles

    @bicycles.setter
    def bicycles(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Bicycle
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Bicycle) for v in value) and
                 True), \
                "The 'bicycles' field must be a set or sequence and each value of type 'Bicycle'"
        self._bicycles = value

    @builtins.property
    def animals(self):
        """Message field 'animals'."""
        return self._animals

    @animals.setter
    def animals(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Animal
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Animal) for v in value) and
                 True), \
                "The 'animals' field must be a set or sequence and each value of type 'Animal'"
        self._animals = value

    @builtins.property
    def unknowns(self):
        """Message field 'unknowns'."""
        return self._unknowns

    @unknowns.setter
    def unknowns(self, value):
        if __debug__:
            from hv_perception_msgs.msg import Unknown
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Unknown) for v in value) and
                 True), \
                "The 'unknowns' field must be a set or sequence and each value of type 'Unknown'"
        self._unknowns = value
