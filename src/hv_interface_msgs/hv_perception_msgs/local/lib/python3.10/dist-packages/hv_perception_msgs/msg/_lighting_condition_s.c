// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/LightingCondition.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/lighting_condition__struct.h"
#include "hv_perception_msgs/msg/detail/lighting_condition__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__lighting_condition__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[61];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._lighting_condition.LightingCondition", full_classname_dest, 60) == 0);
  }
  hv_perception_msgs__msg__LightingCondition * ros_message = _ros_message;
  {  // detection_timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "detection_timestamp");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->detection_timestamp = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // camera_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "camera_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->camera_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lighting_condition_type
    PyObject * field = PyObject_GetAttrString(_pymsg, "lighting_condition_type");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lighting_condition_type = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // confidence
    PyObject * field = PyObject_GetAttrString(_pymsg, "confidence");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->confidence = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // brightness
    PyObject * field = PyObject_GetAttrString(_pymsg, "brightness");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->brightness = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // glare_intensity
    PyObject * field = PyObject_GetAttrString(_pymsg, "glare_intensity");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->glare_intensity = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // is_affecting_visibility
    PyObject * field = PyObject_GetAttrString(_pymsg, "is_affecting_visibility");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->is_affecting_visibility = (Py_True == field);
    Py_DECREF(field);
  }
  {  // is_valid
    PyObject * field = PyObject_GetAttrString(_pymsg, "is_valid");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->is_valid = (Py_True == field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__lighting_condition__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of LightingCondition */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._lighting_condition");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "LightingCondition");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__LightingCondition * ros_message = (hv_perception_msgs__msg__LightingCondition *)raw_ros_message;
  {  // detection_timestamp
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->detection_timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "detection_timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // camera_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->camera_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "camera_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lighting_condition_type
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lighting_condition_type);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lighting_condition_type", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // confidence
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->confidence);
    {
      int rc = PyObject_SetAttrString(_pymessage, "confidence", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // brightness
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->brightness);
    {
      int rc = PyObject_SetAttrString(_pymessage, "brightness", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // glare_intensity
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->glare_intensity);
    {
      int rc = PyObject_SetAttrString(_pymessage, "glare_intensity", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // is_affecting_visibility
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->is_affecting_visibility ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "is_affecting_visibility", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // is_valid
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->is_valid ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "is_valid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
