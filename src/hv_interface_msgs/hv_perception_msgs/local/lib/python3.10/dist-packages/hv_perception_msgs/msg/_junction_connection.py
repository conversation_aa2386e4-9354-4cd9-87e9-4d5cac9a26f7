# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/JunctionConnection.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'left_line_ids'
# Member 'right_line_ids'
# Member 'arrow_ids'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_JunctionConnection(type):
    """Metaclass of message 'JunctionConnection'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.JunctionConnection')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__junction_connection
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__junction_connection
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__junction_connection
            cls._TYPE_SUPPORT = module.type_support_msg__msg__junction_connection
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__junction_connection

            from hv_common_msgs.msg import Point2d
            if Point2d.__class__._TYPE_SUPPORT is None:
                Point2d.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class JunctionConnection(metaclass=Metaclass_JunctionConnection):
    """Message class 'JunctionConnection'."""

    __slots__ = [
        '_detection_timestamp',
        '_connection_id',
        '_connection_type',
        '_confidence',
        '_center_line',
        '_connection_width',
        '_left_line_ids',
        '_right_line_ids',
        '_arrow_ids',
        '_entry_point',
        '_exit_point',
        '_entry_heading',
        '_exit_heading',
        '_incoming_lane_id',
        '_outgoing_lane_id',
        '_has_traffic_light',
        '_associated_traffic_light_id',
        '_has_stop_line',
        '_associated_stop_line_id',
        '_is_valid',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'double',
        'connection_id': 'uint32',
        'connection_type': 'uint8',
        'confidence': 'uint8',
        'center_line': 'sequence<hv_common_msgs/Point2d>',
        'connection_width': 'double',
        'left_line_ids': 'sequence<uint32>',
        'right_line_ids': 'sequence<uint32>',
        'arrow_ids': 'sequence<uint32>',
        'entry_point': 'hv_common_msgs/Point2d',
        'exit_point': 'hv_common_msgs/Point2d',
        'entry_heading': 'float',
        'exit_heading': 'float',
        'incoming_lane_id': 'uint32',
        'outgoing_lane_id': 'uint32',
        'has_traffic_light': 'boolean',
        'associated_traffic_light_id': 'uint32',
        'has_stop_line': 'boolean',
        'associated_stop_line_id': 'uint32',
        'is_valid': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point2d')),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point2d'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point2d'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', float())
        self.connection_id = kwargs.get('connection_id', int())
        self.connection_type = kwargs.get('connection_type', int())
        self.confidence = kwargs.get('confidence', int())
        self.center_line = kwargs.get('center_line', [])
        self.connection_width = kwargs.get('connection_width', float())
        self.left_line_ids = array.array('I', kwargs.get('left_line_ids', []))
        self.right_line_ids = array.array('I', kwargs.get('right_line_ids', []))
        self.arrow_ids = array.array('I', kwargs.get('arrow_ids', []))
        from hv_common_msgs.msg import Point2d
        self.entry_point = kwargs.get('entry_point', Point2d())
        from hv_common_msgs.msg import Point2d
        self.exit_point = kwargs.get('exit_point', Point2d())
        self.entry_heading = kwargs.get('entry_heading', float())
        self.exit_heading = kwargs.get('exit_heading', float())
        self.incoming_lane_id = kwargs.get('incoming_lane_id', int())
        self.outgoing_lane_id = kwargs.get('outgoing_lane_id', int())
        self.has_traffic_light = kwargs.get('has_traffic_light', bool())
        self.associated_traffic_light_id = kwargs.get('associated_traffic_light_id', int())
        self.has_stop_line = kwargs.get('has_stop_line', bool())
        self.associated_stop_line_id = kwargs.get('associated_stop_line_id', int())
        self.is_valid = kwargs.get('is_valid', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.connection_id != other.connection_id:
            return False
        if self.connection_type != other.connection_type:
            return False
        if self.confidence != other.confidence:
            return False
        if self.center_line != other.center_line:
            return False
        if self.connection_width != other.connection_width:
            return False
        if self.left_line_ids != other.left_line_ids:
            return False
        if self.right_line_ids != other.right_line_ids:
            return False
        if self.arrow_ids != other.arrow_ids:
            return False
        if self.entry_point != other.entry_point:
            return False
        if self.exit_point != other.exit_point:
            return False
        if self.entry_heading != other.entry_heading:
            return False
        if self.exit_heading != other.exit_heading:
            return False
        if self.incoming_lane_id != other.incoming_lane_id:
            return False
        if self.outgoing_lane_id != other.outgoing_lane_id:
            return False
        if self.has_traffic_light != other.has_traffic_light:
            return False
        if self.associated_traffic_light_id != other.associated_traffic_light_id:
            return False
        if self.has_stop_line != other.has_stop_line:
            return False
        if self.associated_stop_line_id != other.associated_stop_line_id:
            return False
        if self.is_valid != other.is_valid:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'detection_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'detection_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._detection_timestamp = value

    @builtins.property
    def connection_id(self):
        """Message field 'connection_id'."""
        return self._connection_id

    @connection_id.setter
    def connection_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'connection_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'connection_id' field must be an unsigned integer in [0, 4294967295]"
        self._connection_id = value

    @builtins.property
    def connection_type(self):
        """Message field 'connection_type'."""
        return self._connection_type

    @connection_type.setter
    def connection_type(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'connection_type' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'connection_type' field must be an unsigned integer in [0, 255]"
        self._connection_type = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def center_line(self):
        """Message field 'center_line'."""
        return self._center_line

    @center_line.setter
    def center_line(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point2d
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Point2d) for v in value) and
                 True), \
                "The 'center_line' field must be a set or sequence and each value of type 'Point2d'"
        self._center_line = value

    @builtins.property
    def connection_width(self):
        """Message field 'connection_width'."""
        return self._connection_width

    @connection_width.setter
    def connection_width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'connection_width' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'connection_width' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._connection_width = value

    @builtins.property
    def left_line_ids(self):
        """Message field 'left_line_ids'."""
        return self._left_line_ids

    @left_line_ids.setter
    def left_line_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'left_line_ids' array.array() must have the type code of 'I'"
            self._left_line_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'left_line_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._left_line_ids = array.array('I', value)

    @builtins.property
    def right_line_ids(self):
        """Message field 'right_line_ids'."""
        return self._right_line_ids

    @right_line_ids.setter
    def right_line_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'right_line_ids' array.array() must have the type code of 'I'"
            self._right_line_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'right_line_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._right_line_ids = array.array('I', value)

    @builtins.property
    def arrow_ids(self):
        """Message field 'arrow_ids'."""
        return self._arrow_ids

    @arrow_ids.setter
    def arrow_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'arrow_ids' array.array() must have the type code of 'I'"
            self._arrow_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'arrow_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._arrow_ids = array.array('I', value)

    @builtins.property
    def entry_point(self):
        """Message field 'entry_point'."""
        return self._entry_point

    @entry_point.setter
    def entry_point(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point2d
            assert \
                isinstance(value, Point2d), \
                "The 'entry_point' field must be a sub message of type 'Point2d'"
        self._entry_point = value

    @builtins.property
    def exit_point(self):
        """Message field 'exit_point'."""
        return self._exit_point

    @exit_point.setter
    def exit_point(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point2d
            assert \
                isinstance(value, Point2d), \
                "The 'exit_point' field must be a sub message of type 'Point2d'"
        self._exit_point = value

    @builtins.property
    def entry_heading(self):
        """Message field 'entry_heading'."""
        return self._entry_heading

    @entry_heading.setter
    def entry_heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'entry_heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'entry_heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._entry_heading = value

    @builtins.property
    def exit_heading(self):
        """Message field 'exit_heading'."""
        return self._exit_heading

    @exit_heading.setter
    def exit_heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'exit_heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'exit_heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._exit_heading = value

    @builtins.property
    def incoming_lane_id(self):
        """Message field 'incoming_lane_id'."""
        return self._incoming_lane_id

    @incoming_lane_id.setter
    def incoming_lane_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'incoming_lane_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'incoming_lane_id' field must be an unsigned integer in [0, 4294967295]"
        self._incoming_lane_id = value

    @builtins.property
    def outgoing_lane_id(self):
        """Message field 'outgoing_lane_id'."""
        return self._outgoing_lane_id

    @outgoing_lane_id.setter
    def outgoing_lane_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'outgoing_lane_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'outgoing_lane_id' field must be an unsigned integer in [0, 4294967295]"
        self._outgoing_lane_id = value

    @builtins.property
    def has_traffic_light(self):
        """Message field 'has_traffic_light'."""
        return self._has_traffic_light

    @has_traffic_light.setter
    def has_traffic_light(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'has_traffic_light' field must be of type 'bool'"
        self._has_traffic_light = value

    @builtins.property
    def associated_traffic_light_id(self):
        """Message field 'associated_traffic_light_id'."""
        return self._associated_traffic_light_id

    @associated_traffic_light_id.setter
    def associated_traffic_light_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'associated_traffic_light_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'associated_traffic_light_id' field must be an unsigned integer in [0, 4294967295]"
        self._associated_traffic_light_id = value

    @builtins.property
    def has_stop_line(self):
        """Message field 'has_stop_line'."""
        return self._has_stop_line

    @has_stop_line.setter
    def has_stop_line(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'has_stop_line' field must be of type 'bool'"
        self._has_stop_line = value

    @builtins.property
    def associated_stop_line_id(self):
        """Message field 'associated_stop_line_id'."""
        return self._associated_stop_line_id

    @associated_stop_line_id.setter
    def associated_stop_line_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'associated_stop_line_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'associated_stop_line_id' field must be an unsigned integer in [0, 4294967295]"
        self._associated_stop_line_id = value

    @builtins.property
    def is_valid(self):
        """Message field 'is_valid'."""
        return self._is_valid

    @is_valid.setter
    def is_valid(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_valid' field must be of type 'bool'"
        self._is_valid = value
