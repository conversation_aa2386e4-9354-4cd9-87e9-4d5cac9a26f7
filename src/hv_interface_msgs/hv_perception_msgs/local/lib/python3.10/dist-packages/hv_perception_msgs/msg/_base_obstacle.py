# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/BaseObstacle.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_BaseObstacle(type):
    """Metaclass of message 'BaseObstacle'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.BaseObstacle')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__base_obstacle
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__base_obstacle
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__base_obstacle
            cls._TYPE_SUPPORT = module.type_support_msg__msg__base_obstacle
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__base_obstacle

            from hv_common_msgs.msg import Point3d
            if Point3d.__class__._TYPE_SUPPORT is None:
                Point3d.__class__.__import_type_support__()

            from hv_common_msgs.msg import Vector3d
            if Vector3d.__class__._TYPE_SUPPORT is None:
                Vector3d.__class__.__import_type_support__()

            from hv_perception_msgs.msg import PredictionPoint
            if PredictionPoint.__class__._TYPE_SUPPORT is None:
                PredictionPoint.__class__.__import_type_support__()

            from hv_perception_msgs.msg import TypeHistory
            if TypeHistory.__class__._TYPE_SUPPORT is None:
                TypeHistory.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class BaseObstacle(metaclass=Metaclass_BaseObstacle):
    """Message class 'BaseObstacle'."""

    __slots__ = [
        '_track_id',
        '_track_status',
        '_track_age',
        '_track_time',
        '_sensor_fusion_source',
        '_type_history',
        '_confidence',
        '_position',
        '_position_type',
        '_velocity',
        '_acceleration',
        '_abs_speed',
        '_abs_acceleration',
        '_heading_to_ego',
        '_heading_rate',
        '_position_boot',
        '_velocity_boot',
        '_acceleration_boot',
        '_yaw',
        '_yaw_rate',
        '_length',
        '_width',
        '_height',
        '_motion_status',
        '_points',
        '_intent',
        '_priority',
        '_interactive_intent',
    ]

    _fields_and_field_types = {
        'track_id': 'uint32',
        'track_status': 'uint8',
        'track_age': 'uint32',
        'track_time': 'double',
        'sensor_fusion_source': 'uint8',
        'type_history': 'sequence<hv_perception_msgs/TypeHistory>',
        'confidence': 'uint8',
        'position': 'hv_common_msgs/Point3d',
        'position_type': 'uint8',
        'velocity': 'hv_common_msgs/Vector3d',
        'acceleration': 'hv_common_msgs/Vector3d',
        'abs_speed': 'double',
        'abs_acceleration': 'double',
        'heading_to_ego': 'double',
        'heading_rate': 'double',
        'position_boot': 'hv_common_msgs/Point3d',
        'velocity_boot': 'hv_common_msgs/Vector3d',
        'acceleration_boot': 'hv_common_msgs/Vector3d',
        'yaw': 'double',
        'yaw_rate': 'double',
        'length': 'double',
        'width': 'double',
        'height': 'double',
        'motion_status': 'uint8',
        'points': 'sequence<hv_perception_msgs/PredictionPoint>',
        'intent': 'int8',
        'priority': 'int8',
        'interactive_intent': 'int8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'TypeHistory')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point3d'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Vector3d'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Vector3d'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point3d'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Vector3d'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Vector3d'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'PredictionPoint')),  # noqa: E501
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.track_id = kwargs.get('track_id', int())
        self.track_status = kwargs.get('track_status', int())
        self.track_age = kwargs.get('track_age', int())
        self.track_time = kwargs.get('track_time', float())
        self.sensor_fusion_source = kwargs.get('sensor_fusion_source', int())
        self.type_history = kwargs.get('type_history', [])
        self.confidence = kwargs.get('confidence', int())
        from hv_common_msgs.msg import Point3d
        self.position = kwargs.get('position', Point3d())
        self.position_type = kwargs.get('position_type', int())
        from hv_common_msgs.msg import Vector3d
        self.velocity = kwargs.get('velocity', Vector3d())
        from hv_common_msgs.msg import Vector3d
        self.acceleration = kwargs.get('acceleration', Vector3d())
        self.abs_speed = kwargs.get('abs_speed', float())
        self.abs_acceleration = kwargs.get('abs_acceleration', float())
        self.heading_to_ego = kwargs.get('heading_to_ego', float())
        self.heading_rate = kwargs.get('heading_rate', float())
        from hv_common_msgs.msg import Point3d
        self.position_boot = kwargs.get('position_boot', Point3d())
        from hv_common_msgs.msg import Vector3d
        self.velocity_boot = kwargs.get('velocity_boot', Vector3d())
        from hv_common_msgs.msg import Vector3d
        self.acceleration_boot = kwargs.get('acceleration_boot', Vector3d())
        self.yaw = kwargs.get('yaw', float())
        self.yaw_rate = kwargs.get('yaw_rate', float())
        self.length = kwargs.get('length', float())
        self.width = kwargs.get('width', float())
        self.height = kwargs.get('height', float())
        self.motion_status = kwargs.get('motion_status', int())
        self.points = kwargs.get('points', [])
        self.intent = kwargs.get('intent', int())
        self.priority = kwargs.get('priority', int())
        self.interactive_intent = kwargs.get('interactive_intent', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.track_id != other.track_id:
            return False
        if self.track_status != other.track_status:
            return False
        if self.track_age != other.track_age:
            return False
        if self.track_time != other.track_time:
            return False
        if self.sensor_fusion_source != other.sensor_fusion_source:
            return False
        if self.type_history != other.type_history:
            return False
        if self.confidence != other.confidence:
            return False
        if self.position != other.position:
            return False
        if self.position_type != other.position_type:
            return False
        if self.velocity != other.velocity:
            return False
        if self.acceleration != other.acceleration:
            return False
        if self.abs_speed != other.abs_speed:
            return False
        if self.abs_acceleration != other.abs_acceleration:
            return False
        if self.heading_to_ego != other.heading_to_ego:
            return False
        if self.heading_rate != other.heading_rate:
            return False
        if self.position_boot != other.position_boot:
            return False
        if self.velocity_boot != other.velocity_boot:
            return False
        if self.acceleration_boot != other.acceleration_boot:
            return False
        if self.yaw != other.yaw:
            return False
        if self.yaw_rate != other.yaw_rate:
            return False
        if self.length != other.length:
            return False
        if self.width != other.width:
            return False
        if self.height != other.height:
            return False
        if self.motion_status != other.motion_status:
            return False
        if self.points != other.points:
            return False
        if self.intent != other.intent:
            return False
        if self.priority != other.priority:
            return False
        if self.interactive_intent != other.interactive_intent:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def track_id(self):
        """Message field 'track_id'."""
        return self._track_id

    @track_id.setter
    def track_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'track_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'track_id' field must be an unsigned integer in [0, 4294967295]"
        self._track_id = value

    @builtins.property
    def track_status(self):
        """Message field 'track_status'."""
        return self._track_status

    @track_status.setter
    def track_status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'track_status' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'track_status' field must be an unsigned integer in [0, 255]"
        self._track_status = value

    @builtins.property
    def track_age(self):
        """Message field 'track_age'."""
        return self._track_age

    @track_age.setter
    def track_age(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'track_age' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'track_age' field must be an unsigned integer in [0, 4294967295]"
        self._track_age = value

    @builtins.property
    def track_time(self):
        """Message field 'track_time'."""
        return self._track_time

    @track_time.setter
    def track_time(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'track_time' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'track_time' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._track_time = value

    @builtins.property
    def sensor_fusion_source(self):
        """Message field 'sensor_fusion_source'."""
        return self._sensor_fusion_source

    @sensor_fusion_source.setter
    def sensor_fusion_source(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sensor_fusion_source' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'sensor_fusion_source' field must be an unsigned integer in [0, 255]"
        self._sensor_fusion_source = value

    @builtins.property
    def type_history(self):
        """Message field 'type_history'."""
        return self._type_history

    @type_history.setter
    def type_history(self, value):
        if __debug__:
            from hv_perception_msgs.msg import TypeHistory
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, TypeHistory) for v in value) and
                 True), \
                "The 'type_history' field must be a set or sequence and each value of type 'TypeHistory'"
        self._type_history = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def position(self):
        """Message field 'position'."""
        return self._position

    @position.setter
    def position(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point3d
            assert \
                isinstance(value, Point3d), \
                "The 'position' field must be a sub message of type 'Point3d'"
        self._position = value

    @builtins.property
    def position_type(self):
        """Message field 'position_type'."""
        return self._position_type

    @position_type.setter
    def position_type(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'position_type' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'position_type' field must be an unsigned integer in [0, 255]"
        self._position_type = value

    @builtins.property
    def velocity(self):
        """Message field 'velocity'."""
        return self._velocity

    @velocity.setter
    def velocity(self, value):
        if __debug__:
            from hv_common_msgs.msg import Vector3d
            assert \
                isinstance(value, Vector3d), \
                "The 'velocity' field must be a sub message of type 'Vector3d'"
        self._velocity = value

    @builtins.property
    def acceleration(self):
        """Message field 'acceleration'."""
        return self._acceleration

    @acceleration.setter
    def acceleration(self, value):
        if __debug__:
            from hv_common_msgs.msg import Vector3d
            assert \
                isinstance(value, Vector3d), \
                "The 'acceleration' field must be a sub message of type 'Vector3d'"
        self._acceleration = value

    @builtins.property
    def abs_speed(self):
        """Message field 'abs_speed'."""
        return self._abs_speed

    @abs_speed.setter
    def abs_speed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'abs_speed' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'abs_speed' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._abs_speed = value

    @builtins.property
    def abs_acceleration(self):
        """Message field 'abs_acceleration'."""
        return self._abs_acceleration

    @abs_acceleration.setter
    def abs_acceleration(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'abs_acceleration' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'abs_acceleration' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._abs_acceleration = value

    @builtins.property
    def heading_to_ego(self):
        """Message field 'heading_to_ego'."""
        return self._heading_to_ego

    @heading_to_ego.setter
    def heading_to_ego(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading_to_ego' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'heading_to_ego' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._heading_to_ego = value

    @builtins.property
    def heading_rate(self):
        """Message field 'heading_rate'."""
        return self._heading_rate

    @heading_rate.setter
    def heading_rate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading_rate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'heading_rate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._heading_rate = value

    @builtins.property
    def position_boot(self):
        """Message field 'position_boot'."""
        return self._position_boot

    @position_boot.setter
    def position_boot(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point3d
            assert \
                isinstance(value, Point3d), \
                "The 'position_boot' field must be a sub message of type 'Point3d'"
        self._position_boot = value

    @builtins.property
    def velocity_boot(self):
        """Message field 'velocity_boot'."""
        return self._velocity_boot

    @velocity_boot.setter
    def velocity_boot(self, value):
        if __debug__:
            from hv_common_msgs.msg import Vector3d
            assert \
                isinstance(value, Vector3d), \
                "The 'velocity_boot' field must be a sub message of type 'Vector3d'"
        self._velocity_boot = value

    @builtins.property
    def acceleration_boot(self):
        """Message field 'acceleration_boot'."""
        return self._acceleration_boot

    @acceleration_boot.setter
    def acceleration_boot(self, value):
        if __debug__:
            from hv_common_msgs.msg import Vector3d
            assert \
                isinstance(value, Vector3d), \
                "The 'acceleration_boot' field must be a sub message of type 'Vector3d'"
        self._acceleration_boot = value

    @builtins.property
    def yaw(self):
        """Message field 'yaw'."""
        return self._yaw

    @yaw.setter
    def yaw(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'yaw' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'yaw' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._yaw = value

    @builtins.property
    def yaw_rate(self):
        """Message field 'yaw_rate'."""
        return self._yaw_rate

    @yaw_rate.setter
    def yaw_rate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'yaw_rate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'yaw_rate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._yaw_rate = value

    @builtins.property
    def length(self):
        """Message field 'length'."""
        return self._length

    @length.setter
    def length(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'length' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'length' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._length = value

    @builtins.property
    def width(self):
        """Message field 'width'."""
        return self._width

    @width.setter
    def width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'width' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'width' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._width = value

    @builtins.property
    def height(self):
        """Message field 'height'."""
        return self._height

    @height.setter
    def height(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'height' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'height' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._height = value

    @builtins.property
    def motion_status(self):
        """Message field 'motion_status'."""
        return self._motion_status

    @motion_status.setter
    def motion_status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'motion_status' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'motion_status' field must be an unsigned integer in [0, 255]"
        self._motion_status = value

    @builtins.property
    def points(self):
        """Message field 'points'."""
        return self._points

    @points.setter
    def points(self, value):
        if __debug__:
            from hv_perception_msgs.msg import PredictionPoint
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, PredictionPoint) for v in value) and
                 True), \
                "The 'points' field must be a set or sequence and each value of type 'PredictionPoint'"
        self._points = value

    @builtins.property
    def intent(self):
        """Message field 'intent'."""
        return self._intent

    @intent.setter
    def intent(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'intent' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'intent' field must be an integer in [-128, 127]"
        self._intent = value

    @builtins.property
    def priority(self):
        """Message field 'priority'."""
        return self._priority

    @priority.setter
    def priority(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'priority' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'priority' field must be an integer in [-128, 127]"
        self._priority = value

    @builtins.property
    def interactive_intent(self):
        """Message field 'interactive_intent'."""
        return self._interactive_intent

    @interactive_intent.setter
    def interactive_intent(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'interactive_intent' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'interactive_intent' field must be an integer in [-128, 127]"
        self._interactive_intent = value
