# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/CameraQuality.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_CameraQuality(type):
    """Metaclass of message 'CameraQuality'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.CameraQuality')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__camera_quality
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__camera_quality
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__camera_quality
            cls._TYPE_SUPPORT = module.type_support_msg__msg__camera_quality
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__camera_quality

            from hv_perception_msgs.msg import ImageQuality
            if ImageQuality.__class__._TYPE_SUPPORT is None:
                ImageQuality.__class__.__import_type_support__()

            from hv_perception_msgs.msg import RegionImageQuality
            if RegionImageQuality.__class__._TYPE_SUPPORT is None:
                RegionImageQuality.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class CameraQuality(metaclass=Metaclass_CameraQuality):
    """Message class 'CameraQuality'."""

    __slots__ = [
        '_camera_device_name',
        '_measurement_timestamp',
        '_camera_quality',
        '_region_image_quality',
    ]

    _fields_and_field_types = {
        'camera_device_name': 'string',
        'measurement_timestamp': 'double',
        'camera_quality': 'hv_perception_msgs/ImageQuality',
        'region_image_quality': 'sequence<hv_perception_msgs/RegionImageQuality>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'ImageQuality'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'RegionImageQuality')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.camera_device_name = kwargs.get('camera_device_name', str())
        self.measurement_timestamp = kwargs.get('measurement_timestamp', float())
        from hv_perception_msgs.msg import ImageQuality
        self.camera_quality = kwargs.get('camera_quality', ImageQuality())
        self.region_image_quality = kwargs.get('region_image_quality', [])

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.camera_device_name != other.camera_device_name:
            return False
        if self.measurement_timestamp != other.measurement_timestamp:
            return False
        if self.camera_quality != other.camera_quality:
            return False
        if self.region_image_quality != other.region_image_quality:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def camera_device_name(self):
        """Message field 'camera_device_name'."""
        return self._camera_device_name

    @camera_device_name.setter
    def camera_device_name(self, value):
        if __debug__:
            assert \
                isinstance(value, str), \
                "The 'camera_device_name' field must be of type 'str'"
        self._camera_device_name = value

    @builtins.property
    def measurement_timestamp(self):
        """Message field 'measurement_timestamp'."""
        return self._measurement_timestamp

    @measurement_timestamp.setter
    def measurement_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'measurement_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'measurement_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._measurement_timestamp = value

    @builtins.property
    def camera_quality(self):
        """Message field 'camera_quality'."""
        return self._camera_quality

    @camera_quality.setter
    def camera_quality(self, value):
        if __debug__:
            from hv_perception_msgs.msg import ImageQuality
            assert \
                isinstance(value, ImageQuality), \
                "The 'camera_quality' field must be a sub message of type 'ImageQuality'"
        self._camera_quality = value

    @builtins.property
    def region_image_quality(self):
        """Message field 'region_image_quality'."""
        return self._region_image_quality

    @region_image_quality.setter
    def region_image_quality(self, value):
        if __debug__:
            from hv_perception_msgs.msg import RegionImageQuality
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, RegionImageQuality) for v in value) and
                 True), \
                "The 'region_image_quality' field must be a set or sequence and each value of type 'RegionImageQuality'"
        self._region_image_quality = value
