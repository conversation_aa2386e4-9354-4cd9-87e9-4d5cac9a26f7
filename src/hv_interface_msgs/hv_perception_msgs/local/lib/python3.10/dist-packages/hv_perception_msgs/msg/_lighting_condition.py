# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/LightingCondition.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_LightingCondition(type):
    """Metaclass of message 'LightingCondition'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.LightingCondition')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__lighting_condition
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__lighting_condition
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__lighting_condition
            cls._TYPE_SUPPORT = module.type_support_msg__msg__lighting_condition
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__lighting_condition

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class LightingCondition(metaclass=Metaclass_LightingCondition):
    """Message class 'LightingCondition'."""

    __slots__ = [
        '_detection_timestamp',
        '_camera_id',
        '_lighting_condition_type',
        '_confidence',
        '_brightness',
        '_glare_intensity',
        '_is_affecting_visibility',
        '_is_valid',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'double',
        'camera_id': 'uint32',
        'lighting_condition_type': 'uint8',
        'confidence': 'uint8',
        'brightness': 'double',
        'glare_intensity': 'double',
        'is_affecting_visibility': 'boolean',
        'is_valid': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', float())
        self.camera_id = kwargs.get('camera_id', int())
        self.lighting_condition_type = kwargs.get('lighting_condition_type', int())
        self.confidence = kwargs.get('confidence', int())
        self.brightness = kwargs.get('brightness', float())
        self.glare_intensity = kwargs.get('glare_intensity', float())
        self.is_affecting_visibility = kwargs.get('is_affecting_visibility', bool())
        self.is_valid = kwargs.get('is_valid', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.camera_id != other.camera_id:
            return False
        if self.lighting_condition_type != other.lighting_condition_type:
            return False
        if self.confidence != other.confidence:
            return False
        if self.brightness != other.brightness:
            return False
        if self.glare_intensity != other.glare_intensity:
            return False
        if self.is_affecting_visibility != other.is_affecting_visibility:
            return False
        if self.is_valid != other.is_valid:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'detection_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'detection_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._detection_timestamp = value

    @builtins.property
    def camera_id(self):
        """Message field 'camera_id'."""
        return self._camera_id

    @camera_id.setter
    def camera_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'camera_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'camera_id' field must be an unsigned integer in [0, 4294967295]"
        self._camera_id = value

    @builtins.property
    def lighting_condition_type(self):
        """Message field 'lighting_condition_type'."""
        return self._lighting_condition_type

    @lighting_condition_type.setter
    def lighting_condition_type(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lighting_condition_type' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lighting_condition_type' field must be an unsigned integer in [0, 255]"
        self._lighting_condition_type = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def brightness(self):
        """Message field 'brightness'."""
        return self._brightness

    @brightness.setter
    def brightness(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'brightness' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'brightness' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._brightness = value

    @builtins.property
    def glare_intensity(self):
        """Message field 'glare_intensity'."""
        return self._glare_intensity

    @glare_intensity.setter
    def glare_intensity(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'glare_intensity' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'glare_intensity' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._glare_intensity = value

    @builtins.property
    def is_affecting_visibility(self):
        """Message field 'is_affecting_visibility'."""
        return self._is_affecting_visibility

    @is_affecting_visibility.setter
    def is_affecting_visibility(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_affecting_visibility' field must be of type 'bool'"
        self._is_affecting_visibility = value

    @builtins.property
    def is_valid(self):
        """Message field 'is_valid'."""
        return self._is_valid

    @is_valid.setter
    def is_valid(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_valid' field must be of type 'bool'"
        self._is_valid = value
