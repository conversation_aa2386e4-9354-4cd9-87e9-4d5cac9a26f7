# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/BicycleContent.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_BicycleContent(type):
    """Metaclass of message 'BicycleContent'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.BicycleContent')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__bicycle_content
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__bicycle_content
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__bicycle_content
            cls._TYPE_SUPPORT = module.type_support_msg__msg__bicycle_content
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__bicycle_content

            from hv_perception_msgs.msg import BicycleLightStatus
            if BicycleLightStatus.__class__._TYPE_SUPPORT is None:
                BicycleLightStatus.__class__.__import_type_support__()

            from hv_perception_msgs.msg import LidarCornerPoint
            if LidarCornerPoint.__class__._TYPE_SUPPORT is None:
                LidarCornerPoint.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class BicycleContent(metaclass=Metaclass_BicycleContent):
    """Message class 'BicycleContent'."""

    __slots__ = [
        '_bicycle_classification',
        '_is_bicycle_with_driver',
        '_driverfacing',
        '_bicycle_light_status',
        '_lidar_corner_points',
        '_wheel_angle_to_body',
    ]

    _fields_and_field_types = {
        'bicycle_classification': 'uint8',
        'is_bicycle_with_driver': 'boolean',
        'driverfacing': 'uint8',
        'bicycle_light_status': 'hv_perception_msgs/BicycleLightStatus',
        'lidar_corner_points': 'sequence<hv_perception_msgs/LidarCornerPoint>',
        'wheel_angle_to_body': 'int8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'BicycleLightStatus'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_perception_msgs', 'msg'], 'LidarCornerPoint')),  # noqa: E501
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.bicycle_classification = kwargs.get('bicycle_classification', int())
        self.is_bicycle_with_driver = kwargs.get('is_bicycle_with_driver', bool())
        self.driverfacing = kwargs.get('driverfacing', int())
        from hv_perception_msgs.msg import BicycleLightStatus
        self.bicycle_light_status = kwargs.get('bicycle_light_status', BicycleLightStatus())
        self.lidar_corner_points = kwargs.get('lidar_corner_points', [])
        self.wheel_angle_to_body = kwargs.get('wheel_angle_to_body', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.bicycle_classification != other.bicycle_classification:
            return False
        if self.is_bicycle_with_driver != other.is_bicycle_with_driver:
            return False
        if self.driverfacing != other.driverfacing:
            return False
        if self.bicycle_light_status != other.bicycle_light_status:
            return False
        if self.lidar_corner_points != other.lidar_corner_points:
            return False
        if self.wheel_angle_to_body != other.wheel_angle_to_body:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def bicycle_classification(self):
        """Message field 'bicycle_classification'."""
        return self._bicycle_classification

    @bicycle_classification.setter
    def bicycle_classification(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'bicycle_classification' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'bicycle_classification' field must be an unsigned integer in [0, 255]"
        self._bicycle_classification = value

    @builtins.property
    def is_bicycle_with_driver(self):
        """Message field 'is_bicycle_with_driver'."""
        return self._is_bicycle_with_driver

    @is_bicycle_with_driver.setter
    def is_bicycle_with_driver(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_bicycle_with_driver' field must be of type 'bool'"
        self._is_bicycle_with_driver = value

    @builtins.property
    def driverfacing(self):
        """Message field 'driverfacing'."""
        return self._driverfacing

    @driverfacing.setter
    def driverfacing(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'driverfacing' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'driverfacing' field must be an unsigned integer in [0, 255]"
        self._driverfacing = value

    @builtins.property
    def bicycle_light_status(self):
        """Message field 'bicycle_light_status'."""
        return self._bicycle_light_status

    @bicycle_light_status.setter
    def bicycle_light_status(self, value):
        if __debug__:
            from hv_perception_msgs.msg import BicycleLightStatus
            assert \
                isinstance(value, BicycleLightStatus), \
                "The 'bicycle_light_status' field must be a sub message of type 'BicycleLightStatus'"
        self._bicycle_light_status = value

    @builtins.property
    def lidar_corner_points(self):
        """Message field 'lidar_corner_points'."""
        return self._lidar_corner_points

    @lidar_corner_points.setter
    def lidar_corner_points(self, value):
        if __debug__:
            from hv_perception_msgs.msg import LidarCornerPoint
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, LidarCornerPoint) for v in value) and
                 True), \
                "The 'lidar_corner_points' field must be a set or sequence and each value of type 'LidarCornerPoint'"
        self._lidar_corner_points = value

    @builtins.property
    def wheel_angle_to_body(self):
        """Message field 'wheel_angle_to_body'."""
        return self._wheel_angle_to_body

    @wheel_angle_to_body.setter
    def wheel_angle_to_body(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'wheel_angle_to_body' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'wheel_angle_to_body' field must be an integer in [-128, 127]"
        self._wheel_angle_to_body = value
