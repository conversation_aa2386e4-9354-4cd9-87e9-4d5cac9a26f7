# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/LidarOccupancyGrid.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'grid_packed_data'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_LidarOccupancyGrid(type):
    """Metaclass of message 'LidarOccupancyGrid'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.LidarOccupancyGrid')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__lidar_occupancy_grid
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__lidar_occupancy_grid
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__lidar_occupancy_grid
            cls._TYPE_SUPPORT = module.type_support_msg__msg__lidar_occupancy_grid
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__lidar_occupancy_grid

            from hv_common_msgs.msg import Point2d
            if Point2d.__class__._TYPE_SUPPORT is None:
                Point2d.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class LidarOccupancyGrid(metaclass=Metaclass_LidarOccupancyGrid):
    """Message class 'LidarOccupancyGrid'."""

    __slots__ = [
        '_detection_timestamp',
        '_grid_id',
        '_confidence',
        '_grid_row_num',
        '_grid_col_num',
        '_grid_packed_data',
        '_grid_resolution',
        '_grid_origin',
        '_grid_width',
        '_grid_height',
        '_is_valid',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'double',
        'grid_id': 'uint32',
        'confidence': 'uint8',
        'grid_row_num': 'uint16',
        'grid_col_num': 'uint16',
        'grid_packed_data': 'sequence<uint8>',
        'grid_resolution': 'float',
        'grid_origin': 'hv_common_msgs/Point2d',
        'grid_width': 'float',
        'grid_height': 'float',
        'is_valid': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint16'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint16'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint8')),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point2d'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', float())
        self.grid_id = kwargs.get('grid_id', int())
        self.confidence = kwargs.get('confidence', int())
        self.grid_row_num = kwargs.get('grid_row_num', int())
        self.grid_col_num = kwargs.get('grid_col_num', int())
        self.grid_packed_data = array.array('B', kwargs.get('grid_packed_data', []))
        self.grid_resolution = kwargs.get('grid_resolution', float())
        from hv_common_msgs.msg import Point2d
        self.grid_origin = kwargs.get('grid_origin', Point2d())
        self.grid_width = kwargs.get('grid_width', float())
        self.grid_height = kwargs.get('grid_height', float())
        self.is_valid = kwargs.get('is_valid', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.grid_id != other.grid_id:
            return False
        if self.confidence != other.confidence:
            return False
        if self.grid_row_num != other.grid_row_num:
            return False
        if self.grid_col_num != other.grid_col_num:
            return False
        if self.grid_packed_data != other.grid_packed_data:
            return False
        if self.grid_resolution != other.grid_resolution:
            return False
        if self.grid_origin != other.grid_origin:
            return False
        if self.grid_width != other.grid_width:
            return False
        if self.grid_height != other.grid_height:
            return False
        if self.is_valid != other.is_valid:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'detection_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'detection_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._detection_timestamp = value

    @builtins.property
    def grid_id(self):
        """Message field 'grid_id'."""
        return self._grid_id

    @grid_id.setter
    def grid_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'grid_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'grid_id' field must be an unsigned integer in [0, 4294967295]"
        self._grid_id = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def grid_row_num(self):
        """Message field 'grid_row_num'."""
        return self._grid_row_num

    @grid_row_num.setter
    def grid_row_num(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'grid_row_num' field must be of type 'int'"
            assert value >= 0 and value < 65536, \
                "The 'grid_row_num' field must be an unsigned integer in [0, 65535]"
        self._grid_row_num = value

    @builtins.property
    def grid_col_num(self):
        """Message field 'grid_col_num'."""
        return self._grid_col_num

    @grid_col_num.setter
    def grid_col_num(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'grid_col_num' field must be of type 'int'"
            assert value >= 0 and value < 65536, \
                "The 'grid_col_num' field must be an unsigned integer in [0, 65535]"
        self._grid_col_num = value

    @builtins.property
    def grid_packed_data(self):
        """Message field 'grid_packed_data'."""
        return self._grid_packed_data

    @grid_packed_data.setter
    def grid_packed_data(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'B', \
                "The 'grid_packed_data' array.array() must have the type code of 'B'"
            self._grid_packed_data = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 256 for val in value)), \
                "The 'grid_packed_data' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 255]"
        self._grid_packed_data = array.array('B', value)

    @builtins.property
    def grid_resolution(self):
        """Message field 'grid_resolution'."""
        return self._grid_resolution

    @grid_resolution.setter
    def grid_resolution(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'grid_resolution' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'grid_resolution' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._grid_resolution = value

    @builtins.property
    def grid_origin(self):
        """Message field 'grid_origin'."""
        return self._grid_origin

    @grid_origin.setter
    def grid_origin(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point2d
            assert \
                isinstance(value, Point2d), \
                "The 'grid_origin' field must be a sub message of type 'Point2d'"
        self._grid_origin = value

    @builtins.property
    def grid_width(self):
        """Message field 'grid_width'."""
        return self._grid_width

    @grid_width.setter
    def grid_width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'grid_width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'grid_width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._grid_width = value

    @builtins.property
    def grid_height(self):
        """Message field 'grid_height'."""
        return self._grid_height

    @grid_height.setter
    def grid_height(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'grid_height' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'grid_height' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._grid_height = value

    @builtins.property
    def is_valid(self):
        """Message field 'is_valid'."""
        return self._is_valid

    @is_valid.setter
    def is_valid(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_valid' field must be of type 'bool'"
        self._is_valid = value
