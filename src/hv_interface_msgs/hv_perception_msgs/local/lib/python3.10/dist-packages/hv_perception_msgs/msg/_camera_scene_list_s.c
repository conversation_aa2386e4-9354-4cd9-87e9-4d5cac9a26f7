// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/CameraSceneList.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/camera_scene_list__struct.h"
#include "hv_perception_msgs/msg/detail/camera_scene_list__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "hv_perception_msgs/msg/detail/crosswalk__functions.h"
#include "hv_perception_msgs/msg/detail/curb__functions.h"
#include "hv_perception_msgs/msg/detail/junction__functions.h"
#include "hv_perception_msgs/msg/detail/keep_clear_area__functions.h"
#include "hv_perception_msgs/msg/detail/lane__functions.h"
#include "hv_perception_msgs/msg/detail/lane_line__functions.h"
#include "hv_perception_msgs/msg/detail/parking_space__functions.h"
#include "hv_perception_msgs/msg/detail/speed_bump__functions.h"
#include "hv_perception_msgs/msg/detail/stop_line__functions.h"
// end nested array functions include
ROSIDL_GENERATOR_C_IMPORT
bool hv_common_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * hv_common_msgs__msg__header__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__perception_header__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__perception_header__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__lane_line__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__lane_line__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__lane__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__lane__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__curb__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__curb__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__stop_line__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__stop_line__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__crosswalk__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__crosswalk__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__parking_space__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__parking_space__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__speed_bump__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__speed_bump__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__keep_clear_area__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__keep_clear_area__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__junction__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__junction__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__camera_scene_list__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[58];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._camera_scene_list.CameraSceneList", full_classname_dest, 57) == 0);
  }
  hv_perception_msgs__msg__CameraSceneList * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!hv_common_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // meta_header
    PyObject * field = PyObject_GetAttrString(_pymsg, "meta_header");
    if (!field) {
      return false;
    }
    if (!hv_perception_msgs__msg__perception_header__convert_from_py(field, &ros_message->meta_header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // lane_lines
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_lines");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'lane_lines'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__LaneLine__Sequence__init(&(ros_message->lane_lines), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__LaneLine__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__LaneLine * dest = ros_message->lane_lines.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__lane_line__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // lanes
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanes");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'lanes'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__Lane__Sequence__init(&(ros_message->lanes), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__Lane__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__Lane * dest = ros_message->lanes.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__lane__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // curbs
    PyObject * field = PyObject_GetAttrString(_pymsg, "curbs");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'curbs'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__Curb__Sequence__init(&(ros_message->curbs), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__Curb__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__Curb * dest = ros_message->curbs.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__curb__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // stop_lines
    PyObject * field = PyObject_GetAttrString(_pymsg, "stop_lines");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'stop_lines'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__StopLine__Sequence__init(&(ros_message->stop_lines), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__StopLine__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__StopLine * dest = ros_message->stop_lines.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__stop_line__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // crosswalks
    PyObject * field = PyObject_GetAttrString(_pymsg, "crosswalks");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'crosswalks'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__Crosswalk__Sequence__init(&(ros_message->crosswalks), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__Crosswalk__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__Crosswalk * dest = ros_message->crosswalks.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__crosswalk__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // parking_spaces
    PyObject * field = PyObject_GetAttrString(_pymsg, "parking_spaces");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'parking_spaces'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__ParkingSpace__Sequence__init(&(ros_message->parking_spaces), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__ParkingSpace__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__ParkingSpace * dest = ros_message->parking_spaces.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__parking_space__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // speed_bumps
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed_bumps");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'speed_bumps'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__SpeedBump__Sequence__init(&(ros_message->speed_bumps), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__SpeedBump__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__SpeedBump * dest = ros_message->speed_bumps.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__speed_bump__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // keep_clear_areas
    PyObject * field = PyObject_GetAttrString(_pymsg, "keep_clear_areas");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'keep_clear_areas'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__KeepClearArea__Sequence__init(&(ros_message->keep_clear_areas), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__KeepClearArea__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__KeepClearArea * dest = ros_message->keep_clear_areas.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__keep_clear_area__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // junctions
    PyObject * field = PyObject_GetAttrString(_pymsg, "junctions");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'junctions'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__Junction__Sequence__init(&(ros_message->junctions), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__Junction__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__Junction * dest = ros_message->junctions.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__junction__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__camera_scene_list__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of CameraSceneList */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._camera_scene_list");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "CameraSceneList");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__CameraSceneList * ros_message = (hv_perception_msgs__msg__CameraSceneList *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = hv_common_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // meta_header
    PyObject * field = NULL;
    field = hv_perception_msgs__msg__perception_header__convert_to_py(&ros_message->meta_header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "meta_header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_lines
    PyObject * field = NULL;
    size_t size = ros_message->lane_lines.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__LaneLine * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->lane_lines.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__lane_line__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_lines", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanes
    PyObject * field = NULL;
    size_t size = ros_message->lanes.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__Lane * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->lanes.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__lane__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanes", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // curbs
    PyObject * field = NULL;
    size_t size = ros_message->curbs.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__Curb * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->curbs.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__curb__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "curbs", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // stop_lines
    PyObject * field = NULL;
    size_t size = ros_message->stop_lines.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__StopLine * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->stop_lines.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__stop_line__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "stop_lines", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // crosswalks
    PyObject * field = NULL;
    size_t size = ros_message->crosswalks.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__Crosswalk * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->crosswalks.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__crosswalk__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "crosswalks", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // parking_spaces
    PyObject * field = NULL;
    size_t size = ros_message->parking_spaces.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__ParkingSpace * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->parking_spaces.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__parking_space__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "parking_spaces", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed_bumps
    PyObject * field = NULL;
    size_t size = ros_message->speed_bumps.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__SpeedBump * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->speed_bumps.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__speed_bump__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed_bumps", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // keep_clear_areas
    PyObject * field = NULL;
    size_t size = ros_message->keep_clear_areas.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__KeepClearArea * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->keep_clear_areas.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__keep_clear_area__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "keep_clear_areas", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // junctions
    PyObject * field = NULL;
    size_t size = ros_message->junctions.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__Junction * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->junctions.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__junction__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "junctions", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
