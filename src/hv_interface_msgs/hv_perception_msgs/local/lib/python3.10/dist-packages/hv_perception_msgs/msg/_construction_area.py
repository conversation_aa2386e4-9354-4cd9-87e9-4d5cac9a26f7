# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/ConstructionArea.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'cone_ids'
# Member 'lane_ids'
# Member 'edge_passability'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_ConstructionArea(type):
    """Metaclass of message 'ConstructionArea'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.ConstructionArea')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__construction_area
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__construction_area
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__construction_area
            cls._TYPE_SUPPORT = module.type_support_msg__msg__construction_area
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__construction_area

            from hv_common_msgs.msg import Polygon2d
            if Polygon2d.__class__._TYPE_SUPPORT is None:
                Polygon2d.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class ConstructionArea(metaclass=Metaclass_ConstructionArea):
    """Message class 'ConstructionArea'."""

    __slots__ = [
        '_detection_timestamp',
        '_construction_area_id',
        '_confidence',
        '_box_corners',
        '_cone_ids',
        '_lane_ids',
        '_edge_passability',
        '_nudge_hint',
        '_is_valid',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'double',
        'construction_area_id': 'uint32',
        'confidence': 'uint8',
        'box_corners': 'hv_common_msgs/Polygon2d',
        'cone_ids': 'sequence<uint32>',
        'lane_ids': 'sequence<uint32>',
        'edge_passability': 'sequence<uint8>',
        'nudge_hint': 'uint8',
        'is_valid': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Polygon2d'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint8')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', float())
        self.construction_area_id = kwargs.get('construction_area_id', int())
        self.confidence = kwargs.get('confidence', int())
        from hv_common_msgs.msg import Polygon2d
        self.box_corners = kwargs.get('box_corners', Polygon2d())
        self.cone_ids = array.array('I', kwargs.get('cone_ids', []))
        self.lane_ids = array.array('I', kwargs.get('lane_ids', []))
        self.edge_passability = array.array('B', kwargs.get('edge_passability', []))
        self.nudge_hint = kwargs.get('nudge_hint', int())
        self.is_valid = kwargs.get('is_valid', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.construction_area_id != other.construction_area_id:
            return False
        if self.confidence != other.confidence:
            return False
        if self.box_corners != other.box_corners:
            return False
        if self.cone_ids != other.cone_ids:
            return False
        if self.lane_ids != other.lane_ids:
            return False
        if self.edge_passability != other.edge_passability:
            return False
        if self.nudge_hint != other.nudge_hint:
            return False
        if self.is_valid != other.is_valid:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'detection_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'detection_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._detection_timestamp = value

    @builtins.property
    def construction_area_id(self):
        """Message field 'construction_area_id'."""
        return self._construction_area_id

    @construction_area_id.setter
    def construction_area_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'construction_area_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'construction_area_id' field must be an unsigned integer in [0, 4294967295]"
        self._construction_area_id = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def box_corners(self):
        """Message field 'box_corners'."""
        return self._box_corners

    @box_corners.setter
    def box_corners(self, value):
        if __debug__:
            from hv_common_msgs.msg import Polygon2d
            assert \
                isinstance(value, Polygon2d), \
                "The 'box_corners' field must be a sub message of type 'Polygon2d'"
        self._box_corners = value

    @builtins.property
    def cone_ids(self):
        """Message field 'cone_ids'."""
        return self._cone_ids

    @cone_ids.setter
    def cone_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'cone_ids' array.array() must have the type code of 'I'"
            self._cone_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'cone_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._cone_ids = array.array('I', value)

    @builtins.property
    def lane_ids(self):
        """Message field 'lane_ids'."""
        return self._lane_ids

    @lane_ids.setter
    def lane_ids(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'lane_ids' array.array() must have the type code of 'I'"
            self._lane_ids = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'lane_ids' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._lane_ids = array.array('I', value)

    @builtins.property
    def edge_passability(self):
        """Message field 'edge_passability'."""
        return self._edge_passability

    @edge_passability.setter
    def edge_passability(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'B', \
                "The 'edge_passability' array.array() must have the type code of 'B'"
            self._edge_passability = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 256 for val in value)), \
                "The 'edge_passability' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 255]"
        self._edge_passability = array.array('B', value)

    @builtins.property
    def nudge_hint(self):
        """Message field 'nudge_hint'."""
        return self._nudge_hint

    @nudge_hint.setter
    def nudge_hint(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'nudge_hint' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'nudge_hint' field must be an unsigned integer in [0, 255]"
        self._nudge_hint = value

    @builtins.property
    def is_valid(self):
        """Message field 'is_valid'."""
        return self._is_valid

    @is_valid.setter
    def is_valid(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_valid' field must be of type 'bool'"
        self._is_valid = value
