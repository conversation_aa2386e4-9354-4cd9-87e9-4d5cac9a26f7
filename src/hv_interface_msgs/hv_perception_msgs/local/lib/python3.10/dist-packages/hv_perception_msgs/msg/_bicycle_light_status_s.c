// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/BicycleLightStatus.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/bicycle_light_status__struct.h"
#include "hv_perception_msgs/msg/detail/bicycle_light_status__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__bicycle_light_status__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[64];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._bicycle_light_status.BicycleLightStatus", full_classname_dest, 63) == 0);
  }
  hv_perception_msgs__msg__BicycleLightStatus * ros_message = _ros_message;
  {  // bicycle_blinker_status
    PyObject * field = PyObject_GetAttrString(_pymsg, "bicycle_blinker_status");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->bicycle_blinker_status = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // bicycle_brake_light_status
    PyObject * field = PyObject_GetAttrString(_pymsg, "bicycle_brake_light_status");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->bicycle_brake_light_status = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // bicycle_corner_lamp_status
    PyObject * field = PyObject_GetAttrString(_pymsg, "bicycle_corner_lamp_status");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->bicycle_corner_lamp_status = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__bicycle_light_status__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of BicycleLightStatus */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._bicycle_light_status");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "BicycleLightStatus");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__BicycleLightStatus * ros_message = (hv_perception_msgs__msg__BicycleLightStatus *)raw_ros_message;
  {  // bicycle_blinker_status
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->bicycle_blinker_status);
    {
      int rc = PyObject_SetAttrString(_pymessage, "bicycle_blinker_status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // bicycle_brake_light_status
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->bicycle_brake_light_status);
    {
      int rc = PyObject_SetAttrString(_pymessage, "bicycle_brake_light_status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // bicycle_corner_lamp_status
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->bicycle_corner_lamp_status);
    {
      int rc = PyObject_SetAttrString(_pymessage, "bicycle_corner_lamp_status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
