# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/KeepClearArea.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_KeepClearArea(type):
    """Metaclass of message 'KeepClearArea'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.KeepClearArea')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__keep_clear_area
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__keep_clear_area
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__keep_clear_area
            cls._TYPE_SUPPORT = module.type_support_msg__msg__keep_clear_area
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__keep_clear_area

            from hv_common_msgs.msg import Polygon2d
            if Polygon2d.__class__._TYPE_SUPPORT is None:
                Polygon2d.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class KeepClearArea(metaclass=Metaclass_KeepClearArea):
    """Message class 'KeepClearArea'."""

    __slots__ = [
        '_detection_timestamp',
        '_keep_clear_area_id',
        '_confidence',
        '_points',
        '_width',
        '_length',
        '_distance',
        '_is_valid',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'uint64',
        'keep_clear_area_id': 'uint32',
        'confidence': 'uint8',
        'points': 'hv_common_msgs/Polygon2d',
        'width': 'float',
        'length': 'float',
        'distance': 'float',
        'is_valid': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Polygon2d'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', int())
        self.keep_clear_area_id = kwargs.get('keep_clear_area_id', int())
        self.confidence = kwargs.get('confidence', int())
        from hv_common_msgs.msg import Polygon2d
        self.points = kwargs.get('points', Polygon2d())
        self.width = kwargs.get('width', float())
        self.length = kwargs.get('length', float())
        self.distance = kwargs.get('distance', float())
        self.is_valid = kwargs.get('is_valid', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.keep_clear_area_id != other.keep_clear_area_id:
            return False
        if self.confidence != other.confidence:
            return False
        if self.points != other.points:
            return False
        if self.width != other.width:
            return False
        if self.length != other.length:
            return False
        if self.distance != other.distance:
            return False
        if self.is_valid != other.is_valid:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'detection_timestamp' field must be of type 'int'"
            assert value >= 0 and value < 18446744073709551616, \
                "The 'detection_timestamp' field must be an unsigned integer in [0, 18446744073709551615]"
        self._detection_timestamp = value

    @builtins.property
    def keep_clear_area_id(self):
        """Message field 'keep_clear_area_id'."""
        return self._keep_clear_area_id

    @keep_clear_area_id.setter
    def keep_clear_area_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'keep_clear_area_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'keep_clear_area_id' field must be an unsigned integer in [0, 4294967295]"
        self._keep_clear_area_id = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def points(self):
        """Message field 'points'."""
        return self._points

    @points.setter
    def points(self, value):
        if __debug__:
            from hv_common_msgs.msg import Polygon2d
            assert \
                isinstance(value, Polygon2d), \
                "The 'points' field must be a sub message of type 'Polygon2d'"
        self._points = value

    @builtins.property
    def width(self):
        """Message field 'width'."""
        return self._width

    @width.setter
    def width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._width = value

    @builtins.property
    def length(self):
        """Message field 'length'."""
        return self._length

    @length.setter
    def length(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'length' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'length' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._length = value

    @builtins.property
    def distance(self):
        """Message field 'distance'."""
        return self._distance

    @distance.setter
    def distance(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'distance' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'distance' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._distance = value

    @builtins.property
    def is_valid(self):
        """Message field 'is_valid'."""
        return self._is_valid

    @is_valid.setter
    def is_valid(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_valid' field must be of type 'bool'"
        self._is_valid = value
