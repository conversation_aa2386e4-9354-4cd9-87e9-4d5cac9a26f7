# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_perception_msgs:msg/LaneArrow.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_LaneArrow(type):
    """Metaclass of message 'LaneArrow'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_perception_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_perception_msgs.msg.LaneArrow')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__lane_arrow
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__lane_arrow
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__lane_arrow
            cls._TYPE_SUPPORT = module.type_support_msg__msg__lane_arrow
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__lane_arrow

            from hv_common_msgs.msg import Point2d
            if Point2d.__class__._TYPE_SUPPORT is None:
                Point2d.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class LaneArrow(metaclass=Metaclass_LaneArrow):
    """Message class 'LaneArrow'."""

    __slots__ = [
        '_detection_timestamp',
        '_arrow_id',
        '_arrow_type',
        '_confidence',
        '_position',
        '_heading',
        '_width',
        '_length',
        '_is_visible',
    ]

    _fields_and_field_types = {
        'detection_timestamp': 'double',
        'arrow_id': 'uint32',
        'arrow_type': 'uint8',
        'confidence': 'uint8',
        'position': 'hv_common_msgs/Point2d',
        'heading': 'float',
        'width': 'float',
        'length': 'float',
        'is_visible': 'boolean',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Point2d'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.detection_timestamp = kwargs.get('detection_timestamp', float())
        self.arrow_id = kwargs.get('arrow_id', int())
        self.arrow_type = kwargs.get('arrow_type', int())
        self.confidence = kwargs.get('confidence', int())
        from hv_common_msgs.msg import Point2d
        self.position = kwargs.get('position', Point2d())
        self.heading = kwargs.get('heading', float())
        self.width = kwargs.get('width', float())
        self.length = kwargs.get('length', float())
        self.is_visible = kwargs.get('is_visible', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.detection_timestamp != other.detection_timestamp:
            return False
        if self.arrow_id != other.arrow_id:
            return False
        if self.arrow_type != other.arrow_type:
            return False
        if self.confidence != other.confidence:
            return False
        if self.position != other.position:
            return False
        if self.heading != other.heading:
            return False
        if self.width != other.width:
            return False
        if self.length != other.length:
            return False
        if self.is_visible != other.is_visible:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def detection_timestamp(self):
        """Message field 'detection_timestamp'."""
        return self._detection_timestamp

    @detection_timestamp.setter
    def detection_timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'detection_timestamp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'detection_timestamp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._detection_timestamp = value

    @builtins.property
    def arrow_id(self):
        """Message field 'arrow_id'."""
        return self._arrow_id

    @arrow_id.setter
    def arrow_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'arrow_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'arrow_id' field must be an unsigned integer in [0, 4294967295]"
        self._arrow_id = value

    @builtins.property
    def arrow_type(self):
        """Message field 'arrow_type'."""
        return self._arrow_type

    @arrow_type.setter
    def arrow_type(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'arrow_type' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'arrow_type' field must be an unsigned integer in [0, 255]"
        self._arrow_type = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'confidence' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'confidence' field must be an unsigned integer in [0, 255]"
        self._confidence = value

    @builtins.property
    def position(self):
        """Message field 'position'."""
        return self._position

    @position.setter
    def position(self, value):
        if __debug__:
            from hv_common_msgs.msg import Point2d
            assert \
                isinstance(value, Point2d), \
                "The 'position' field must be a sub message of type 'Point2d'"
        self._position = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._heading = value

    @builtins.property
    def width(self):
        """Message field 'width'."""
        return self._width

    @width.setter
    def width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._width = value

    @builtins.property
    def length(self):
        """Message field 'length'."""
        return self._length

    @length.setter
    def length(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'length' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'length' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._length = value

    @builtins.property
    def is_visible(self):
        """Message field 'is_visible'."""
        return self._is_visible

    @is_visible.setter
    def is_visible(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_visible' field must be of type 'bool'"
        self._is_visible = value
