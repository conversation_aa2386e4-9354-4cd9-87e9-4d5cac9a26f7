// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/BicycleContent.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/bicycle_content__struct.h"
#include "hv_perception_msgs/msg/detail/bicycle_content__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "hv_perception_msgs/msg/detail/lidar_corner_point__functions.h"
// end nested array functions include
bool hv_perception_msgs__msg__bicycle_light_status__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__bicycle_light_status__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__lidar_corner_point__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__lidar_corner_point__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__bicycle_content__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[55];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._bicycle_content.BicycleContent", full_classname_dest, 54) == 0);
  }
  hv_perception_msgs__msg__BicycleContent * ros_message = _ros_message;
  {  // bicycle_classification
    PyObject * field = PyObject_GetAttrString(_pymsg, "bicycle_classification");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->bicycle_classification = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // is_bicycle_with_driver
    PyObject * field = PyObject_GetAttrString(_pymsg, "is_bicycle_with_driver");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->is_bicycle_with_driver = (Py_True == field);
    Py_DECREF(field);
  }
  {  // driverfacing
    PyObject * field = PyObject_GetAttrString(_pymsg, "driverfacing");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->driverfacing = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // bicycle_light_status
    PyObject * field = PyObject_GetAttrString(_pymsg, "bicycle_light_status");
    if (!field) {
      return false;
    }
    if (!hv_perception_msgs__msg__bicycle_light_status__convert_from_py(field, &ros_message->bicycle_light_status)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // lidar_corner_points
    PyObject * field = PyObject_GetAttrString(_pymsg, "lidar_corner_points");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'lidar_corner_points'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__LidarCornerPoint__Sequence__init(&(ros_message->lidar_corner_points), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__LidarCornerPoint__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__LidarCornerPoint * dest = ros_message->lidar_corner_points.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__lidar_corner_point__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // wheel_angle_to_body
    PyObject * field = PyObject_GetAttrString(_pymsg, "wheel_angle_to_body");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->wheel_angle_to_body = (int8_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__bicycle_content__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of BicycleContent */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._bicycle_content");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "BicycleContent");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__BicycleContent * ros_message = (hv_perception_msgs__msg__BicycleContent *)raw_ros_message;
  {  // bicycle_classification
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->bicycle_classification);
    {
      int rc = PyObject_SetAttrString(_pymessage, "bicycle_classification", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // is_bicycle_with_driver
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->is_bicycle_with_driver ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "is_bicycle_with_driver", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // driverfacing
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->driverfacing);
    {
      int rc = PyObject_SetAttrString(_pymessage, "driverfacing", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // bicycle_light_status
    PyObject * field = NULL;
    field = hv_perception_msgs__msg__bicycle_light_status__convert_to_py(&ros_message->bicycle_light_status);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "bicycle_light_status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lidar_corner_points
    PyObject * field = NULL;
    size_t size = ros_message->lidar_corner_points.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__LidarCornerPoint * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->lidar_corner_points.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__lidar_corner_point__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "lidar_corner_points", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // wheel_angle_to_body
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->wheel_angle_to_body);
    {
      int rc = PyObject_SetAttrString(_pymessage, "wheel_angle_to_body", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
