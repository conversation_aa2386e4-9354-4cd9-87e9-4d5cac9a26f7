// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/LidarOccupancyGridList.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/lidar_occupancy_grid_list__struct.h"
#include "hv_perception_msgs/msg/detail/lidar_occupancy_grid_list__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "hv_perception_msgs/msg/detail/lidar_occupancy_grid__functions.h"
// end nested array functions include
ROSIDL_GENERATOR_C_IMPORT
bool hv_common_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * hv_common_msgs__msg__header__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__perception_header__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__perception_header__convert_to_py(void * raw_ros_message);
bool hv_perception_msgs__msg__lidar_occupancy_grid__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_perception_msgs__msg__lidar_occupancy_grid__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__lidar_occupancy_grid_list__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[73];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._lidar_occupancy_grid_list.LidarOccupancyGridList", full_classname_dest, 72) == 0);
  }
  hv_perception_msgs__msg__LidarOccupancyGridList * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!hv_common_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // meta_header
    PyObject * field = PyObject_GetAttrString(_pymsg, "meta_header");
    if (!field) {
      return false;
    }
    if (!hv_perception_msgs__msg__perception_header__convert_from_py(field, &ros_message->meta_header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // occupancy_grids
    PyObject * field = PyObject_GetAttrString(_pymsg, "occupancy_grids");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'occupancy_grids'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_perception_msgs__msg__LidarOccupancyGrid__Sequence__init(&(ros_message->occupancy_grids), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_perception_msgs__msg__LidarOccupancyGrid__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_perception_msgs__msg__LidarOccupancyGrid * dest = ros_message->occupancy_grids.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_perception_msgs__msg__lidar_occupancy_grid__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__lidar_occupancy_grid_list__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of LidarOccupancyGridList */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._lidar_occupancy_grid_list");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "LidarOccupancyGridList");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__LidarOccupancyGridList * ros_message = (hv_perception_msgs__msg__LidarOccupancyGridList *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = hv_common_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // meta_header
    PyObject * field = NULL;
    field = hv_perception_msgs__msg__perception_header__convert_to_py(&ros_message->meta_header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "meta_header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // occupancy_grids
    PyObject * field = NULL;
    size_t size = ros_message->occupancy_grids.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_perception_msgs__msg__LidarOccupancyGrid * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->occupancy_grids.data[i]);
      PyObject * pyitem = hv_perception_msgs__msg__lidar_occupancy_grid__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "occupancy_grids", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
