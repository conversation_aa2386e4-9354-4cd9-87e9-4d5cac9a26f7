// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_perception_msgs:msg/LidarCornerPoint.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_perception_msgs/msg/detail/lidar_corner_point__struct.h"
#include "hv_perception_msgs/msg/detail/lidar_corner_point__functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool hv_common_msgs__msg__point2d__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * hv_common_msgs__msg__point2d__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hv_perception_msgs__msg__lidar_corner_point__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[60];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_perception_msgs.msg._lidar_corner_point.LidarCornerPoint", full_classname_dest, 59) == 0);
  }
  hv_perception_msgs__msg__LidarCornerPoint * ros_message = _ros_message;
  {  // pos_in_world
    PyObject * field = PyObject_GetAttrString(_pymsg, "pos_in_world");
    if (!field) {
      return false;
    }
    if (!hv_common_msgs__msg__point2d__convert_from_py(field, &ros_message->pos_in_world)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_perception_msgs__msg__lidar_corner_point__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of LidarCornerPoint */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_perception_msgs.msg._lidar_corner_point");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "LidarCornerPoint");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_perception_msgs__msg__LidarCornerPoint * ros_message = (hv_perception_msgs__msg__LidarCornerPoint *)raw_ros_message;
  {  // pos_in_world
    PyObject * field = NULL;
    field = hv_common_msgs__msg__point2d__convert_to_py(&ros_message->pos_in_world);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "pos_in_world", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
