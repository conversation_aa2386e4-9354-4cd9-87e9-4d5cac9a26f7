<?xml version="1.0"?>
<package format="3">
  <name>hv_interface_converters</name>
  <version>0.1.9</version>
  <description>Header-only converters between hv::interface structs and ROS2 messages.</description>
  <maintainer email="<EMAIL>">maintainer</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <depend>hv_interface</depend>
  <depend>hv_common_msgs</depend>
  <depend>hv_control_msgs</depend>
  <depend>hv_function_manager_msgs</depend>
  <depend>hv_localization_msgs</depend>
  <depend>hv_map_msgs</depend>
  <depend>hv_perception_msgs</depend>
  <depend>hv_planning_msgs</depend>
  <depend>hv_prediction_msgs</depend>
  <depend>hv_recoder_msgs</depend>
  <depend>hv_remote_msgs</depend>
  <depend>hv_routing_msgs</depend>
  <depend>hv_sensor_msgs</depend>
  <depend>hv_shm_msgs_msgs</depend>
  <depend>hv_vehicle_io_msgs</depend>
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
