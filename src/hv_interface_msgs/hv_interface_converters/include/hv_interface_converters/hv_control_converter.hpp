// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_control_msgs/msg/control_debug.hpp"
#include "hv_control_msgs/msg/control.hpp"
#include "hv_control_msgs/msg/body_control.hpp"
#include "hv_control_msgs/msg/chassis_control.hpp"
#include "hv_control_msgs/msg/control_header.hpp"

// Include interface headers
#include "hv_interface/control/control.h"
#include "hv_interface/control/control_debug.h"

// Include dependent converters
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_control_msgs::msg::ControlHeader struct_to_msg(const hv::interface::ControlHeader& s);
inline hv::interface::ControlHeader msg_to_struct(const hv_control_msgs::msg::ControlHeader& m);
inline hv_control_msgs::msg::ChassisControl struct_to_msg(const hv::interface::ChassisControl& s);
inline hv::interface::ChassisControl msg_to_struct(const hv_control_msgs::msg::ChassisControl& m);
inline hv_control_msgs::msg::BodyControl struct_to_msg(const hv::interface::BodyControl& s);
inline hv::interface::BodyControl msg_to_struct(const hv_control_msgs::msg::BodyControl& m);
inline hv_control_msgs::msg::Control struct_to_msg(const hv::interface::Control& s);
inline hv::interface::Control msg_to_struct(const hv_control_msgs::msg::Control& m);
inline hv_control_msgs::msg::ControlDebug struct_to_msg(const hv::interface::ControlDebug& s);
inline hv::interface::ControlDebug msg_to_struct(const hv_control_msgs::msg::ControlDebug& m);

inline hv_control_msgs::msg::ControlHeader struct_to_msg(const hv::interface::ControlHeader& s) {
    hv_control_msgs::msg::ControlHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.frame_sequence = s.frame_sequence;
    m.version = s.version;
    return m;
}


inline hv::interface::ControlHeader msg_to_struct(const hv_control_msgs::msg::ControlHeader& m) {
    hv::interface::ControlHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.frame_sequence = m.frame_sequence;
    s.version = m.version;
    return s;
}


inline hv_control_msgs::msg::ChassisControl struct_to_msg(const hv::interface::ChassisControl& s) {
    hv_control_msgs::msg::ChassisControl m;
    m.longitudinal_control_enabled = s.longitudinal_control_enabled;
    m.target_longitudinal_acceleration = s.target_longitudinal_acceleration;
    m.lateral_control_enabled = s.lateral_control_enabled;
    m.target_steering_angle = s.target_steering_angle;
    m.target_gear_position = s.target_gear_position;
    return m;
}


inline hv::interface::ChassisControl msg_to_struct(const hv_control_msgs::msg::ChassisControl& m) {
    hv::interface::ChassisControl s;
    s.longitudinal_control_enabled = m.longitudinal_control_enabled;
    s.target_longitudinal_acceleration = m.target_longitudinal_acceleration;
    s.lateral_control_enabled = m.lateral_control_enabled;
    s.target_steering_angle = m.target_steering_angle;
    s.target_gear_position = m.target_gear_position;
    return s;
}


inline hv_control_msgs::msg::BodyControl struct_to_msg(const hv::interface::BodyControl& s) {
    hv_control_msgs::msg::BodyControl m;
    m.open_turn_left_light = s.open_turn_left_light;
    m.open_turn_right_light = s.open_turn_right_light;
    m.open_hazard_light = s.open_hazard_light;
    return m;
}


inline hv::interface::BodyControl msg_to_struct(const hv_control_msgs::msg::BodyControl& m) {
    hv::interface::BodyControl s;
    s.open_turn_left_light = m.open_turn_left_light;
    s.open_turn_right_light = m.open_turn_right_light;
    s.open_hazard_light = m.open_hazard_light;
    return s;
}


inline hv_control_msgs::msg::Control struct_to_msg(const hv::interface::Control& s) {
    hv_control_msgs::msg::Control m;
    m.header = struct_to_msg(s.header);
    m.control_header = struct_to_msg(s.control_header);
    m.chassis_control = struct_to_msg(s.chassis_control);
    m.body_control = struct_to_msg(s.body_control);
    return m;
}


inline hv::interface::Control msg_to_struct(const hv_control_msgs::msg::Control& m) {
    hv::interface::Control s;
    s.header = msg_to_struct(m.header);
    s.control_header = msg_to_struct(m.control_header);
    s.chassis_control = msg_to_struct(m.chassis_control);
    s.body_control = msg_to_struct(m.body_control);
    return s;
}


inline hv_control_msgs::msg::ControlDebug struct_to_msg(const hv::interface::ControlDebug& s) {
    hv_control_msgs::msg::ControlDebug m;
    m.header = struct_to_msg(s.header);
    m.lat_error = s.lat_error;
    m.lon_error = s.lon_error;
    m.speed_error = s.speed_error;
    m.head_error_deg = s.head_error_deg;
    m.steer_frontfeed = s.steer_frontfeed;
    m.steer_feedback = s.steer_feedback;
    m.steer_item_lat = s.steer_item_lat;
    m.steer_item_head = s.steer_item_head;
    m.steer_item_head_rate = s.steer_item_head_rate;
    m.steer_item_total = s.steer_item_total;
    m.steer_angle_cmd = s.steer_angle_cmd;
    m.steer_angle_real = s.steer_angle_real;
    m.state_received_loc = struct_to_msg(s.state_received_loc);
    m.state_received_traj = struct_to_msg(s.state_received_traj);
    m.state_received_chassis = struct_to_msg(s.state_received_chassis);
    m.state_send_control = struct_to_msg(s.state_send_control);
    m.curvature = s.curvature;
    m.vehicle_yawrate = s.vehicle_yawrate;
    m.loc_current_x = s.loc_current_x;
    m.loc_current_y = s.loc_current_y;
    m.loc_matched_x = s.loc_matched_x;
    m.loc_matched_y = s.loc_matched_y;
    m.equal_k1 = s.equal_k1;
    m.equal_k2 = s.equal_k2;
    m.equal_k3 = s.equal_k3;
    m.longitude_target_speed = s.longitude_target_speed;
    m.longitude_acc_req = s.longitude_acc_req;
    m.frontfeed_normal = s.frontfeed_normal;
    m.frontfeed_kv = s.frontfeed_kv;
    m.frontfeed_e2ss = s.frontfeed_e2ss;
    m.preview_acceleration_reference = s.preview_acceleration_reference;
    m.acceleration_cmd_closeloop = s.acceleration_cmd_closeloop;
    m.reserved0.reserve(s.reserved0.size());
    for (const auto& element : s.reserved0) {
        m.reserved0.push_back(struct_to_msg(element));
    }
    m.reserved0_size = struct_to_msg(s.reserved0_size);
    m.reserved1 = s.reserved1;
    m.reserved1_size = struct_to_msg(s.reserved1_size);
    return m;
}


inline hv::interface::ControlDebug msg_to_struct(const hv_control_msgs::msg::ControlDebug& m) {
    hv::interface::ControlDebug s;
    s.header = msg_to_struct(m.header);
    s.lat_error = m.lat_error;
    s.lon_error = m.lon_error;
    s.speed_error = m.speed_error;
    s.head_error_deg = m.head_error_deg;
    s.steer_frontfeed = m.steer_frontfeed;
    s.steer_feedback = m.steer_feedback;
    s.steer_item_lat = m.steer_item_lat;
    s.steer_item_head = m.steer_item_head;
    s.steer_item_head_rate = m.steer_item_head_rate;
    s.steer_item_total = m.steer_item_total;
    s.steer_angle_cmd = m.steer_angle_cmd;
    s.steer_angle_real = m.steer_angle_real;
    s.state_received_loc = msg_to_struct(m.state_received_loc);
    s.state_received_traj = msg_to_struct(m.state_received_traj);
    s.state_received_chassis = msg_to_struct(m.state_received_chassis);
    s.state_send_control = msg_to_struct(m.state_send_control);
    s.curvature = m.curvature;
    s.vehicle_yawrate = m.vehicle_yawrate;
    s.loc_current_x = m.loc_current_x;
    s.loc_current_y = m.loc_current_y;
    s.loc_matched_x = m.loc_matched_x;
    s.loc_matched_y = m.loc_matched_y;
    s.equal_k1 = m.equal_k1;
    s.equal_k2 = m.equal_k2;
    s.equal_k3 = m.equal_k3;
    s.longitude_target_speed = m.longitude_target_speed;
    s.longitude_acc_req = m.longitude_acc_req;
    s.frontfeed_normal = m.frontfeed_normal;
    s.frontfeed_kv = m.frontfeed_kv;
    s.frontfeed_e2ss = m.frontfeed_e2ss;
    s.preview_acceleration_reference = m.preview_acceleration_reference;
    s.acceleration_cmd_closeloop = m.acceleration_cmd_closeloop;
    s.reserved0.reserve(m.reserved0.size());
    for (const auto& element : m.reserved0) s.reserved0.push_back(msg_to_struct(element));
    s.reserved0_size = msg_to_struct(m.reserved0_size);
    s.reserved1 = m.reserved1;
    s.reserved1_size = msg_to_struct(m.reserved1_size);
    return s;
}



} // namespace converter
} // namespace hv
