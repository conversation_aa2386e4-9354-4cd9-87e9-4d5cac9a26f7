// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_common_msgs/msg/polylineenu.hpp"
#include "hv_common_msgs/msg/translation3d.hpp"
#include "hv_common_msgs/msg/pointenu.hpp"
#include "hv_common_msgs/msg/polygonenu.hpp"
#include "hv_common_msgs/msg/line2d.hpp"
#include "hv_common_msgs/msg/piecewise_curve2d.hpp"
#include "hv_common_msgs/msg/pointllh.hpp"
#include "hv_common_msgs/msg/point3d.hpp"
#include "hv_common_msgs/msg/polygon_boundary_slice.hpp"
#include "hv_common_msgs/msg/polygon2d.hpp"
#include "hv_common_msgs/msg/polygon2d_packed.hpp"
#include "hv_common_msgs/msg/quaternionf.hpp"
#include "hv_common_msgs/msg/image_box3d.hpp"
#include "hv_common_msgs/msg/rigid3d.hpp"
#include "hv_common_msgs/msg/uniform_piecewise_linear_function.hpp"
#include "hv_common_msgs/msg/directed_curve2d.hpp"
#include "hv_common_msgs/msg/point3f.hpp"
#include "hv_common_msgs/msg/box2d.hpp"
#include "hv_common_msgs/msg/rotation_quaterniond.hpp"
#include "hv_common_msgs/msg/euler_anglesd.hpp"
#include "hv_common_msgs/msg/polyline.hpp"
#include "hv_common_msgs/msg/transform.hpp"
#include "hv_common_msgs/msg/header.hpp"
#include "hv_common_msgs/msg/polygonllh.hpp"
#include "hv_common_msgs/msg/rotation_quaternionf.hpp"
#include "hv_common_msgs/msg/curve2d.hpp"
#include "hv_common_msgs/msg/matrixd.hpp"
#include "hv_common_msgs/msg/vector3i.hpp"
#include "hv_common_msgs/msg/plane.hpp"
#include "hv_common_msgs/msg/piecewise_cubic_function.hpp"
#include "hv_common_msgs/msg/point3d_packed.hpp"
#include "hv_common_msgs/msg/euler_anglesf.hpp"
#include "hv_common_msgs/msg/vector3f.hpp"
#include "hv_common_msgs/msg/polyline2d.hpp"
#include "hv_common_msgs/msg/reversible_curve2d.hpp"
#include "hv_common_msgs/msg/vector3d.hpp"
#include "hv_common_msgs/msg/point2f.hpp"
#include "hv_common_msgs/msg/prism.hpp"
#include "hv_common_msgs/msg/rigid3f.hpp"
#include "hv_common_msgs/msg/polyline_packed.hpp"
#include "hv_common_msgs/msg/segment3d.hpp"
#include "hv_common_msgs/msg/image_box2d.hpp"
#include "hv_common_msgs/msg/translation3f.hpp"
#include "hv_common_msgs/msg/point2d.hpp"
#include "hv_common_msgs/msg/quaterniond.hpp"
#include "hv_common_msgs/msg/mcu_header.hpp"
#include "hv_common_msgs/msg/segment2d.hpp"
#include "hv_common_msgs/msg/polygon.hpp"
#include "hv_common_msgs/msg/vector3denu.hpp"
#include "hv_common_msgs/msg/matrixf.hpp"
#include "hv_common_msgs/msg/point2i.hpp"
#include "hv_common_msgs/msg/polylinellh.hpp"
#include "hv_common_msgs/msg/vector2d.hpp"

// Include interface headers
#include "hv_interface/common/header.h"
#include "hv_interface/common/geometry/geometry.h"
#include "hv_interface/common/geometry/matrix.h"
#include "hv_interface/common/geometry/transform.h"

// Include dependent converters

namespace hv {
namespace converter {

inline hv_common_msgs::msg::McuHeader struct_to_msg(const hv::interface::McuHeader& s);
inline hv::interface::McuHeader msg_to_struct(const hv_common_msgs::msg::McuHeader& m);
inline hv_common_msgs::msg::Header struct_to_msg(const hv::interface::Header& s);
inline hv::interface::Header msg_to_struct(const hv_common_msgs::msg::Header& m);
inline hv_common_msgs::msg::Point2d struct_to_msg(const hv::interface::Point2d& s);
inline hv::interface::Point2d msg_to_struct(const hv_common_msgs::msg::Point2d& m);
inline hv_common_msgs::msg::Point2f struct_to_msg(const hv::interface::Point2f& s);
inline hv::interface::Point2f msg_to_struct(const hv_common_msgs::msg::Point2f& m);
inline hv_common_msgs::msg::Point2i struct_to_msg(const hv::interface::Point2i& s);
inline hv::interface::Point2i msg_to_struct(const hv_common_msgs::msg::Point2i& m);
inline hv_common_msgs::msg::Vector2d struct_to_msg(const hv::interface::Vector2d& s);
inline hv::interface::Vector2d msg_to_struct(const hv_common_msgs::msg::Vector2d& m);
inline hv_common_msgs::msg::Point3d struct_to_msg(const hv::interface::Point3d& s);
inline hv::interface::Point3d msg_to_struct(const hv_common_msgs::msg::Point3d& m);
inline hv_common_msgs::msg::Point3f struct_to_msg(const hv::interface::Point3f& s);
inline hv::interface::Point3f msg_to_struct(const hv_common_msgs::msg::Point3f& m);
inline hv_common_msgs::msg::Pointllh struct_to_msg(const hv::interface::Pointllh& s);
inline hv::interface::Pointllh msg_to_struct(const hv_common_msgs::msg::Pointllh& m);
inline hv_common_msgs::msg::Pointenu struct_to_msg(const hv::interface::Pointenu& s);
inline hv::interface::Pointenu msg_to_struct(const hv_common_msgs::msg::Pointenu& m);
inline hv_common_msgs::msg::Polyline struct_to_msg(const hv::interface::Polyline& s);
inline hv::interface::Polyline msg_to_struct(const hv_common_msgs::msg::Polyline& m);
inline hv_common_msgs::msg::Polyline2d struct_to_msg(const hv::interface::Polyline2d& s);
inline hv::interface::Polyline2d msg_to_struct(const hv_common_msgs::msg::Polyline2d& m);
inline hv_common_msgs::msg::Polylinellh struct_to_msg(const hv::interface::Polylinellh& s);
inline hv::interface::Polylinellh msg_to_struct(const hv_common_msgs::msg::Polylinellh& m);
inline hv_common_msgs::msg::Polylineenu struct_to_msg(const hv::interface::Polylineenu& s);
inline hv::interface::Polylineenu msg_to_struct(const hv_common_msgs::msg::Polylineenu& m);
inline hv_common_msgs::msg::PolylinePacked struct_to_msg(const hv::interface::PolylinePacked& s);
inline hv::interface::PolylinePacked msg_to_struct(const hv_common_msgs::msg::PolylinePacked& m);
inline hv_common_msgs::msg::Polygon struct_to_msg(const hv::interface::Polygon& s);
inline hv::interface::Polygon msg_to_struct(const hv_common_msgs::msg::Polygon& m);
inline hv_common_msgs::msg::Polygonllh struct_to_msg(const hv::interface::Polygonllh& s);
inline hv::interface::Polygonllh msg_to_struct(const hv_common_msgs::msg::Polygonllh& m);
inline hv_common_msgs::msg::Polygonenu struct_to_msg(const hv::interface::Polygonenu& s);
inline hv::interface::Polygonenu msg_to_struct(const hv_common_msgs::msg::Polygonenu& m);
inline hv_common_msgs::msg::Polygon2d struct_to_msg(const hv::interface::Polygon2d& s);
inline hv::interface::Polygon2d msg_to_struct(const hv_common_msgs::msg::Polygon2d& m);
inline hv_common_msgs::msg::Polygon2dPacked struct_to_msg(const hv::interface::Polygon2dPacked& s);
inline hv::interface::Polygon2dPacked msg_to_struct(const hv_common_msgs::msg::Polygon2dPacked& m);
inline hv_common_msgs::msg::Point3dPacked struct_to_msg(const hv::interface::Point3dPacked& s);
inline hv::interface::Point3dPacked msg_to_struct(const hv_common_msgs::msg::Point3dPacked& m);
inline hv_common_msgs::msg::Curve2d struct_to_msg(const hv::interface::Curve2d& s);
inline hv::interface::Curve2d msg_to_struct(const hv_common_msgs::msg::Curve2d& m);
inline hv_common_msgs::msg::DirectedCurve2d struct_to_msg(const hv::interface::DirectedCurve2d& s);
inline hv::interface::DirectedCurve2d msg_to_struct(const hv_common_msgs::msg::DirectedCurve2d& m);
inline hv_common_msgs::msg::ReversibleCurve2d struct_to_msg(const hv::interface::ReversibleCurve2d& s);
inline hv::interface::ReversibleCurve2d msg_to_struct(const hv_common_msgs::msg::ReversibleCurve2d& m);
inline hv_common_msgs::msg::PiecewiseCurve2d struct_to_msg(const hv::interface::PiecewiseCurve2d& s);
inline hv::interface::PiecewiseCurve2d msg_to_struct(const hv_common_msgs::msg::PiecewiseCurve2d& m);
inline hv_common_msgs::msg::PiecewiseCubicFunction struct_to_msg(const hv::interface::PiecewiseCubicFunction& s);
inline hv::interface::PiecewiseCubicFunction msg_to_struct(const hv_common_msgs::msg::PiecewiseCubicFunction& m);
inline hv_common_msgs::msg::UniformPiecewiseLinearFunction struct_to_msg(const hv::interface::UniformPiecewiseLinearFunction& s);
inline hv::interface::UniformPiecewiseLinearFunction msg_to_struct(const hv_common_msgs::msg::UniformPiecewiseLinearFunction& m);
inline hv_common_msgs::msg::Prism struct_to_msg(const hv::interface::Prism& s);
inline hv::interface::Prism msg_to_struct(const hv_common_msgs::msg::Prism& m);
inline hv_common_msgs::msg::Box2d struct_to_msg(const hv::interface::Box2d& s);
inline hv::interface::Box2d msg_to_struct(const hv_common_msgs::msg::Box2d& m);
inline hv_common_msgs::msg::Plane struct_to_msg(const hv::interface::Plane& s);
inline hv::interface::Plane msg_to_struct(const hv_common_msgs::msg::Plane& m);
inline hv_common_msgs::msg::PolygonBoundarySlice struct_to_msg(const hv::interface::PolygonBoundarySlice& s);
inline hv::interface::PolygonBoundarySlice msg_to_struct(const hv_common_msgs::msg::PolygonBoundarySlice& m);
inline hv_common_msgs::msg::Segment2d struct_to_msg(const hv::interface::Segment2d& s);
inline hv::interface::Segment2d msg_to_struct(const hv_common_msgs::msg::Segment2d& m);
inline hv_common_msgs::msg::Segment3d struct_to_msg(const hv::interface::Segment3d& s);
inline hv::interface::Segment3d msg_to_struct(const hv_common_msgs::msg::Segment3d& m);
inline hv_common_msgs::msg::Line2d struct_to_msg(const hv::interface::Line2d& s);
inline hv::interface::Line2d msg_to_struct(const hv_common_msgs::msg::Line2d& m);
inline hv_common_msgs::msg::ImageBox3d struct_to_msg(const hv::interface::ImageBox3d& s);
inline hv::interface::ImageBox3d msg_to_struct(const hv_common_msgs::msg::ImageBox3d& m);
inline hv_common_msgs::msg::ImageBox2d struct_to_msg(const hv::interface::ImageBox2d& s);
inline hv::interface::ImageBox2d msg_to_struct(const hv_common_msgs::msg::ImageBox2d& m);
inline hv_common_msgs::msg::Matrixd struct_to_msg(const hv::interface::Matrixd& s);
inline hv::interface::Matrixd msg_to_struct(const hv_common_msgs::msg::Matrixd& m);
inline hv_common_msgs::msg::Matrixf struct_to_msg(const hv::interface::Matrixf& s);
inline hv::interface::Matrixf msg_to_struct(const hv_common_msgs::msg::Matrixf& m);
inline hv_common_msgs::msg::Vector3d struct_to_msg(const hv::interface::Vector3d& s);
inline hv::interface::Vector3d msg_to_struct(const hv_common_msgs::msg::Vector3d& m);
inline hv_common_msgs::msg::Vector3f struct_to_msg(const hv::interface::Vector3f& s);
inline hv::interface::Vector3f msg_to_struct(const hv_common_msgs::msg::Vector3f& m);
inline hv_common_msgs::msg::Vector3i struct_to_msg(const hv::interface::Vector3i& s);
inline hv::interface::Vector3i msg_to_struct(const hv_common_msgs::msg::Vector3i& m);
inline hv_common_msgs::msg::Vector3denu struct_to_msg(const hv::interface::Vector3denu& s);
inline hv::interface::Vector3denu msg_to_struct(const hv_common_msgs::msg::Vector3denu& m);
inline hv_common_msgs::msg::Quaterniond struct_to_msg(const hv::interface::Quaterniond& s);
inline hv::interface::Quaterniond msg_to_struct(const hv_common_msgs::msg::Quaterniond& m);
inline hv_common_msgs::msg::Quaternionf struct_to_msg(const hv::interface::Quaternionf& s);
inline hv::interface::Quaternionf msg_to_struct(const hv_common_msgs::msg::Quaternionf& m);
inline hv_common_msgs::msg::EulerAnglesd struct_to_msg(const hv::interface::EulerAnglesd& s);
inline hv::interface::EulerAnglesd msg_to_struct(const hv_common_msgs::msg::EulerAnglesd& m);
inline hv_common_msgs::msg::EulerAnglesf struct_to_msg(const hv::interface::EulerAnglesf& s);
inline hv::interface::EulerAnglesf msg_to_struct(const hv_common_msgs::msg::EulerAnglesf& m);
inline hv_common_msgs::msg::Rigid3d struct_to_msg(const hv::interface::Rigid3d& s);
inline hv::interface::Rigid3d msg_to_struct(const hv_common_msgs::msg::Rigid3d& m);
inline hv_common_msgs::msg::Rigid3f struct_to_msg(const hv::interface::Rigid3f& s);
inline hv::interface::Rigid3f msg_to_struct(const hv_common_msgs::msg::Rigid3f& m);
inline hv_common_msgs::msg::Transform struct_to_msg(const hv::interface::Transform& s);
inline hv::interface::Transform msg_to_struct(const hv_common_msgs::msg::Transform& m);

inline hv_common_msgs::msg::McuHeader struct_to_msg(const hv::interface::McuHeader& s) {
    hv_common_msgs::msg::McuHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.rolling_counter = s.rolling_counter;
    m.checksum = s.checksum;
    return m;
}


inline hv::interface::McuHeader msg_to_struct(const hv_common_msgs::msg::McuHeader& m) {
    hv::interface::McuHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.rolling_counter = m.rolling_counter;
    s.checksum = m.checksum;
    return s;
}


inline hv_common_msgs::msg::Header struct_to_msg(const hv::interface::Header& s) {
    hv_common_msgs::msg::Header m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.frame_sequence = s.frame_sequence;
    m.frame_id = s.frame_id;
    return m;
}


inline hv::interface::Header msg_to_struct(const hv_common_msgs::msg::Header& m) {
    hv::interface::Header s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.frame_sequence = m.frame_sequence;
    s.frame_id = m.frame_id;
    return s;
}


inline hv_common_msgs::msg::Point2d struct_to_msg(const hv::interface::Point2d& s) {
    hv_common_msgs::msg::Point2d m;
    m.x = s.x;
    m.y = s.y;
    return m;
}


inline hv::interface::Point2d msg_to_struct(const hv_common_msgs::msg::Point2d& m) {
    hv::interface::Point2d s;
    s.x = m.x;
    s.y = m.y;
    return s;
}


inline hv_common_msgs::msg::Point2f struct_to_msg(const hv::interface::Point2f& s) {
    hv_common_msgs::msg::Point2f m;
    m.x = s.x;
    m.y = s.y;
    return m;
}


inline hv::interface::Point2f msg_to_struct(const hv_common_msgs::msg::Point2f& m) {
    hv::interface::Point2f s;
    s.x = m.x;
    s.y = m.y;
    return s;
}


inline hv_common_msgs::msg::Point2i struct_to_msg(const hv::interface::Point2i& s) {
    hv_common_msgs::msg::Point2i m;
    m.x = s.x;
    m.y = s.y;
    return m;
}


inline hv::interface::Point2i msg_to_struct(const hv_common_msgs::msg::Point2i& m) {
    hv::interface::Point2i s;
    s.x = m.x;
    s.y = m.y;
    return s;
}


inline hv_common_msgs::msg::Vector2d struct_to_msg(const hv::interface::Vector2d& s) {
    hv_common_msgs::msg::Vector2d m;
    m.x = s.x;
    m.y = s.y;
    return m;
}


inline hv::interface::Vector2d msg_to_struct(const hv_common_msgs::msg::Vector2d& m) {
    hv::interface::Vector2d s;
    s.x = m.x;
    s.y = m.y;
    return s;
}


inline hv_common_msgs::msg::Point3d struct_to_msg(const hv::interface::Point3d& s) {
    hv_common_msgs::msg::Point3d m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    return m;
}


inline hv::interface::Point3d msg_to_struct(const hv_common_msgs::msg::Point3d& m) {
    hv::interface::Point3d s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    return s;
}


inline hv_common_msgs::msg::Point3f struct_to_msg(const hv::interface::Point3f& s) {
    hv_common_msgs::msg::Point3f m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    return m;
}


inline hv::interface::Point3f msg_to_struct(const hv_common_msgs::msg::Point3f& m) {
    hv::interface::Point3f s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    return s;
}


inline hv_common_msgs::msg::Pointllh struct_to_msg(const hv::interface::Pointllh& s) {
    hv_common_msgs::msg::Pointllh m;
    m.latitude = s.latitude;
    m.longitude = s.longitude;
    m.height = s.height;
    return m;
}


inline hv::interface::Pointllh msg_to_struct(const hv_common_msgs::msg::Pointllh& m) {
    hv::interface::Pointllh s;
    s.latitude = m.latitude;
    s.longitude = m.longitude;
    s.height = m.height;
    return s;
}


inline hv_common_msgs::msg::Pointenu struct_to_msg(const hv::interface::Pointenu& s) {
    hv_common_msgs::msg::Pointenu m;
    m.east = s.east;
    m.north = s.north;
    m.up = s.up;
    return m;
}


inline hv::interface::Pointenu msg_to_struct(const hv_common_msgs::msg::Pointenu& m) {
    hv::interface::Pointenu s;
    s.east = m.east;
    s.north = m.north;
    s.up = m.up;
    return s;
}


inline hv_common_msgs::msg::Polyline struct_to_msg(const hv::interface::Polyline& s) {
    hv_common_msgs::msg::Polyline m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polyline msg_to_struct(const hv_common_msgs::msg::Polyline& m) {
    hv::interface::Polyline s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::Polyline2d struct_to_msg(const hv::interface::Polyline2d& s) {
    hv_common_msgs::msg::Polyline2d m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polyline2d msg_to_struct(const hv_common_msgs::msg::Polyline2d& m) {
    hv::interface::Polyline2d s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::Polylinellh struct_to_msg(const hv::interface::Polylinellh& s) {
    hv_common_msgs::msg::Polylinellh m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polylinellh msg_to_struct(const hv_common_msgs::msg::Polylinellh& m) {
    hv::interface::Polylinellh s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::Polylineenu struct_to_msg(const hv::interface::Polylineenu& s) {
    hv_common_msgs::msg::Polylineenu m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polylineenu msg_to_struct(const hv_common_msgs::msg::Polylineenu& m) {
    hv::interface::Polylineenu s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::PolylinePacked struct_to_msg(const hv::interface::PolylinePacked& s) {
    hv_common_msgs::msg::PolylinePacked m;
    m.data = s.data;
    return m;
}


inline hv::interface::PolylinePacked msg_to_struct(const hv_common_msgs::msg::PolylinePacked& m) {
    hv::interface::PolylinePacked s;
    s.data = m.data;
    return s;
}


inline hv_common_msgs::msg::Polygon struct_to_msg(const hv::interface::Polygon& s) {
    hv_common_msgs::msg::Polygon m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polygon msg_to_struct(const hv_common_msgs::msg::Polygon& m) {
    hv::interface::Polygon s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::Polygonllh struct_to_msg(const hv::interface::Polygonllh& s) {
    hv_common_msgs::msg::Polygonllh m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polygonllh msg_to_struct(const hv_common_msgs::msg::Polygonllh& m) {
    hv::interface::Polygonllh s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::Polygonenu struct_to_msg(const hv::interface::Polygonenu& s) {
    hv_common_msgs::msg::Polygonenu m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polygonenu msg_to_struct(const hv_common_msgs::msg::Polygonenu& m) {
    hv::interface::Polygonenu s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::Polygon2d struct_to_msg(const hv::interface::Polygon2d& s) {
    hv_common_msgs::msg::Polygon2d m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Polygon2d msg_to_struct(const hv_common_msgs::msg::Polygon2d& m) {
    hv::interface::Polygon2d s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::Polygon2dPacked struct_to_msg(const hv::interface::Polygon2dPacked& s) {
    hv_common_msgs::msg::Polygon2dPacked m;
    m.data = s.data;
    return m;
}


inline hv::interface::Polygon2dPacked msg_to_struct(const hv_common_msgs::msg::Polygon2dPacked& m) {
    hv::interface::Polygon2dPacked s;
    s.data = m.data;
    return s;
}


inline hv_common_msgs::msg::Point3dPacked struct_to_msg(const hv::interface::Point3dPacked& s) {
    hv_common_msgs::msg::Point3dPacked m;
    m.data = s.data;
    return m;
}


inline hv::interface::Point3dPacked msg_to_struct(const hv_common_msgs::msg::Point3dPacked& m) {
    hv::interface::Point3dPacked s;
    s.data = m.data;
    return s;
}


inline hv_common_msgs::msg::Curve2d struct_to_msg(const hv::interface::Curve2d& s) {
    hv_common_msgs::msg::Curve2d m;
    m.s0 = s.s0;
    m.s_step = s.s_step;
    m.points.reserve(s.points.size());
    for (const auto& element : s.points) {
        m.points.push_back(struct_to_msg(element));
    }
    m.start_tangent = struct_to_msg(s.start_tangent);
    m.end_tangent = struct_to_msg(s.end_tangent);
    return m;
}


inline hv::interface::Curve2d msg_to_struct(const hv_common_msgs::msg::Curve2d& m) {
    hv::interface::Curve2d s;
    s.s0 = m.s0;
    s.s_step = m.s_step;
    s.points.reserve(m.points.size());
    for (const auto& element : m.points) s.points.push_back(msg_to_struct(element));
    s.start_tangent = msg_to_struct(m.start_tangent);
    s.end_tangent = msg_to_struct(m.end_tangent);
    return s;
}


inline hv_common_msgs::msg::DirectedCurve2d struct_to_msg(const hv::interface::DirectedCurve2d& s) {
    hv_common_msgs::msg::DirectedCurve2d m;
    m.curve = struct_to_msg(s.curve);
    m.is_reverse = s.is_reverse;
    return m;
}


inline hv::interface::DirectedCurve2d msg_to_struct(const hv_common_msgs::msg::DirectedCurve2d& m) {
    hv::interface::DirectedCurve2d s;
    s.curve = msg_to_struct(m.curve);
    s.is_reverse = m.is_reverse;
    return s;
}


inline hv_common_msgs::msg::ReversibleCurve2d struct_to_msg(const hv::interface::ReversibleCurve2d& s) {
    hv_common_msgs::msg::ReversibleCurve2d m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::ReversibleCurve2d msg_to_struct(const hv_common_msgs::msg::ReversibleCurve2d& m) {
    hv::interface::ReversibleCurve2d s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::PiecewiseCurve2d struct_to_msg(const hv::interface::PiecewiseCurve2d& s) {
    hv_common_msgs::msg::PiecewiseCurve2d m;
    m.data.reserve(s.data.size());
    for (const auto& element : s.data) {
        m.data.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::PiecewiseCurve2d msg_to_struct(const hv_common_msgs::msg::PiecewiseCurve2d& m) {
    hv::interface::PiecewiseCurve2d s;
    s.data.reserve(m.data.size());
    for (const auto& element : m.data) s.data.push_back(msg_to_struct(element));
    return s;
}


inline hv_common_msgs::msg::PiecewiseCubicFunction struct_to_msg(const hv::interface::PiecewiseCubicFunction& s) {
    hv_common_msgs::msg::PiecewiseCubicFunction m;
    m.x0 = s.x0;
    m.x_step = s.x_step;
    m.y_values = s.y_values;
    m.is_non_decreasing = s.is_non_decreasing;
    m.monotonic_type = static_cast<int32_t>(s.monotonic_type);
    return m;
}


inline hv::interface::PiecewiseCubicFunction msg_to_struct(const hv_common_msgs::msg::PiecewiseCubicFunction& m) {
    hv::interface::PiecewiseCubicFunction s;
    s.x0 = m.x0;
    s.x_step = m.x_step;
    s.y_values = m.y_values;
    s.is_non_decreasing = m.is_non_decreasing;
    s.monotonic_type = static_cast<hv::interface::MonotonicType>(m.monotonic_type);
    return s;
}


inline hv_common_msgs::msg::UniformPiecewiseLinearFunction struct_to_msg(const hv::interface::UniformPiecewiseLinearFunction& s) {
    hv_common_msgs::msg::UniformPiecewiseLinearFunction m;
    m.x0 = s.x0;
    m.x_step = s.x_step;
    m.y_values = s.y_values;
    return m;
}


inline hv::interface::UniformPiecewiseLinearFunction msg_to_struct(const hv_common_msgs::msg::UniformPiecewiseLinearFunction& m) {
    hv::interface::UniformPiecewiseLinearFunction s;
    s.x0 = m.x0;
    s.x_step = m.x_step;
    s.y_values = m.y_values;
    return s;
}


inline hv_common_msgs::msg::Prism struct_to_msg(const hv::interface::Prism& s) {
    hv_common_msgs::msg::Prism m;
    m.down_surface = struct_to_msg(s.down_surface);
    m.height = s.height;
    return m;
}


inline hv::interface::Prism msg_to_struct(const hv_common_msgs::msg::Prism& m) {
    hv::interface::Prism s;
    s.down_surface = msg_to_struct(m.down_surface);
    s.height = m.height;
    return s;
}


inline hv_common_msgs::msg::Box2d struct_to_msg(const hv::interface::Box2d& s) {
    hv_common_msgs::msg::Box2d m;
    m.center_x = s.center_x;
    m.center_y = s.center_y;
    m.length = s.length;
    m.width = s.width;
    m.sin_heading = s.sin_heading;
    m.cos_heading = s.cos_heading;
    return m;
}


inline hv::interface::Box2d msg_to_struct(const hv_common_msgs::msg::Box2d& m) {
    hv::interface::Box2d s;
    s.center_x = m.center_x;
    s.center_y = m.center_y;
    s.length = m.length;
    s.width = m.width;
    s.sin_heading = m.sin_heading;
    s.cos_heading = m.cos_heading;
    return s;
}


inline hv_common_msgs::msg::Plane struct_to_msg(const hv::interface::Plane& s) {
    hv_common_msgs::msg::Plane m;
    m.normal = struct_to_msg(s.normal);
    m.offset = s.offset;
    return m;
}


inline hv::interface::Plane msg_to_struct(const hv_common_msgs::msg::Plane& m) {
    hv::interface::Plane s;
    s.normal = msg_to_struct(m.normal);
    s.offset = m.offset;
    return s;
}


inline hv_common_msgs::msg::PolygonBoundarySlice struct_to_msg(const hv::interface::PolygonBoundarySlice& s) {
    hv_common_msgs::msg::PolygonBoundarySlice m;
    m.start_index = s.start_index;
    m.end_index = s.end_index;
    return m;
}


inline hv::interface::PolygonBoundarySlice msg_to_struct(const hv_common_msgs::msg::PolygonBoundarySlice& m) {
    hv::interface::PolygonBoundarySlice s;
    s.start_index = m.start_index;
    s.end_index = m.end_index;
    return s;
}


inline hv_common_msgs::msg::Segment2d struct_to_msg(const hv::interface::Segment2d& s) {
    hv_common_msgs::msg::Segment2d m;
    m.start = struct_to_msg(s.start);
    m.end = struct_to_msg(s.end);
    return m;
}


inline hv::interface::Segment2d msg_to_struct(const hv_common_msgs::msg::Segment2d& m) {
    hv::interface::Segment2d s;
    s.start = msg_to_struct(m.start);
    s.end = msg_to_struct(m.end);
    return s;
}


inline hv_common_msgs::msg::Segment3d struct_to_msg(const hv::interface::Segment3d& s) {
    hv_common_msgs::msg::Segment3d m;
    m.start = struct_to_msg(s.start);
    m.end = struct_to_msg(s.end);
    return m;
}


inline hv::interface::Segment3d msg_to_struct(const hv_common_msgs::msg::Segment3d& m) {
    hv::interface::Segment3d s;
    s.start = msg_to_struct(m.start);
    s.end = msg_to_struct(m.end);
    return s;
}


inline hv_common_msgs::msg::Line2d struct_to_msg(const hv::interface::Line2d& s) {
    hv_common_msgs::msg::Line2d m;
    m.normal = struct_to_msg(s.normal);
    m.offset = s.offset;
    return m;
}


inline hv::interface::Line2d msg_to_struct(const hv_common_msgs::msg::Line2d& m) {
    hv::interface::Line2d s;
    s.normal = msg_to_struct(m.normal);
    s.offset = m.offset;
    return s;
}


inline hv_common_msgs::msg::ImageBox3d struct_to_msg(const hv::interface::ImageBox3d& s) {
    hv_common_msgs::msg::ImageBox3d m;
    m.front_top_left = struct_to_msg(s.front_top_left);
    m.front_bottom_right = struct_to_msg(s.front_bottom_right);
    m.back_top_left = struct_to_msg(s.back_top_left);
    m.back_bottom_right = struct_to_msg(s.back_bottom_right);
    return m;
}


inline hv::interface::ImageBox3d msg_to_struct(const hv_common_msgs::msg::ImageBox3d& m) {
    hv::interface::ImageBox3d s;
    s.front_top_left = msg_to_struct(m.front_top_left);
    s.front_bottom_right = msg_to_struct(m.front_bottom_right);
    s.back_top_left = msg_to_struct(m.back_top_left);
    s.back_bottom_right = msg_to_struct(m.back_bottom_right);
    return s;
}


inline hv_common_msgs::msg::ImageBox2d struct_to_msg(const hv::interface::ImageBox2d& s) {
    hv_common_msgs::msg::ImageBox2d m;
    m.top_left = struct_to_msg(s.top_left);
    m.bottom_right = struct_to_msg(s.bottom_right);
    return m;
}


inline hv::interface::ImageBox2d msg_to_struct(const hv_common_msgs::msg::ImageBox2d& m) {
    hv::interface::ImageBox2d s;
    s.top_left = msg_to_struct(m.top_left);
    s.bottom_right = msg_to_struct(m.bottom_right);
    return s;
}


inline hv_common_msgs::msg::Matrixd struct_to_msg(const hv::interface::Matrixd& s) {
    hv_common_msgs::msg::Matrixd m;
    m.rows = s.rows;
    m.cols = s.cols;
    m.elements = s.elements;
    return m;
}


inline hv::interface::Matrixd msg_to_struct(const hv_common_msgs::msg::Matrixd& m) {
    hv::interface::Matrixd s;
    s.rows = m.rows;
    s.cols = m.cols;
    s.elements = m.elements;
    return s;
}


inline hv_common_msgs::msg::Matrixf struct_to_msg(const hv::interface::Matrixf& s) {
    hv_common_msgs::msg::Matrixf m;
    m.rows = s.rows;
    m.cols = s.cols;
    m.elements = s.elements;
    return m;
}


inline hv::interface::Matrixf msg_to_struct(const hv_common_msgs::msg::Matrixf& m) {
    hv::interface::Matrixf s;
    s.rows = m.rows;
    s.cols = m.cols;
    s.elements = m.elements;
    return s;
}


inline hv_common_msgs::msg::Vector3d struct_to_msg(const hv::interface::Vector3d& s) {
    hv_common_msgs::msg::Vector3d m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    return m;
}


inline hv::interface::Vector3d msg_to_struct(const hv_common_msgs::msg::Vector3d& m) {
    hv::interface::Vector3d s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    return s;
}


inline hv_common_msgs::msg::Vector3f struct_to_msg(const hv::interface::Vector3f& s) {
    hv_common_msgs::msg::Vector3f m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    return m;
}


inline hv::interface::Vector3f msg_to_struct(const hv_common_msgs::msg::Vector3f& m) {
    hv::interface::Vector3f s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    return s;
}


inline hv_common_msgs::msg::Vector3i struct_to_msg(const hv::interface::Vector3i& s) {
    hv_common_msgs::msg::Vector3i m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    return m;
}


inline hv::interface::Vector3i msg_to_struct(const hv_common_msgs::msg::Vector3i& m) {
    hv::interface::Vector3i s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    return s;
}


inline hv_common_msgs::msg::Vector3denu struct_to_msg(const hv::interface::Vector3denu& s) {
    hv_common_msgs::msg::Vector3denu m;
    m.east = s.east;
    m.north = s.north;
    m.up = s.up;
    return m;
}


inline hv::interface::Vector3denu msg_to_struct(const hv_common_msgs::msg::Vector3denu& m) {
    hv::interface::Vector3denu s;
    s.east = m.east;
    s.north = m.north;
    s.up = m.up;
    return s;
}


inline hv_common_msgs::msg::Quaterniond struct_to_msg(const hv::interface::Quaterniond& s) {
    hv_common_msgs::msg::Quaterniond m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    m.w = s.w;
    return m;
}


inline hv::interface::Quaterniond msg_to_struct(const hv_common_msgs::msg::Quaterniond& m) {
    hv::interface::Quaterniond s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    s.w = m.w;
    return s;
}


inline hv_common_msgs::msg::Quaternionf struct_to_msg(const hv::interface::Quaternionf& s) {
    hv_common_msgs::msg::Quaternionf m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    m.w = s.w;
    return m;
}


inline hv::interface::Quaternionf msg_to_struct(const hv_common_msgs::msg::Quaternionf& m) {
    hv::interface::Quaternionf s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    s.w = m.w;
    return s;
}


inline hv_common_msgs::msg::EulerAnglesd struct_to_msg(const hv::interface::EulerAnglesd& s) {
    hv_common_msgs::msg::EulerAnglesd m;
    m.roll = s.roll;
    m.pitch = s.pitch;
    m.yaw = s.yaw;
    return m;
}


inline hv::interface::EulerAnglesd msg_to_struct(const hv_common_msgs::msg::EulerAnglesd& m) {
    hv::interface::EulerAnglesd s;
    s.roll = m.roll;
    s.pitch = m.pitch;
    s.yaw = m.yaw;
    return s;
}


inline hv_common_msgs::msg::EulerAnglesf struct_to_msg(const hv::interface::EulerAnglesf& s) {
    hv_common_msgs::msg::EulerAnglesf m;
    m.roll = s.roll;
    m.pitch = s.pitch;
    m.yaw = s.yaw;
    return m;
}


inline hv::interface::EulerAnglesf msg_to_struct(const hv_common_msgs::msg::EulerAnglesf& m) {
    hv::interface::EulerAnglesf s;
    s.roll = m.roll;
    s.pitch = m.pitch;
    s.yaw = m.yaw;
    return s;
}


inline hv_common_msgs::msg::Rigid3d struct_to_msg(const hv::interface::Rigid3d& s) {
    hv_common_msgs::msg::Rigid3d m;
    m.translation = struct_to_msg(s.translation);
    m.rotation = struct_to_msg(s.rotation);
    return m;
}


inline hv::interface::Rigid3d msg_to_struct(const hv_common_msgs::msg::Rigid3d& m) {
    hv::interface::Rigid3d s;
    s.translation = msg_to_struct(m.translation);
    s.rotation = msg_to_struct(m.rotation);
    return s;
}


inline hv_common_msgs::msg::Rigid3f struct_to_msg(const hv::interface::Rigid3f& s) {
    hv_common_msgs::msg::Rigid3f m;
    m.translation = struct_to_msg(s.translation);
    m.rotation = struct_to_msg(s.rotation);
    return m;
}


inline hv::interface::Rigid3f msg_to_struct(const hv_common_msgs::msg::Rigid3f& m) {
    hv::interface::Rigid3f s;
    s.translation = msg_to_struct(m.translation);
    s.rotation = msg_to_struct(m.rotation);
    return s;
}


inline hv_common_msgs::msg::Transform struct_to_msg(const hv::interface::Transform& s) {
    hv_common_msgs::msg::Transform m;
    m.translation = struct_to_msg(s.translation);
    m.rotation = struct_to_msg(s.rotation);
    return m;
}


inline hv::interface::Transform msg_to_struct(const hv_common_msgs::msg::Transform& m) {
    hv::interface::Transform s;
    s.translation = msg_to_struct(m.translation);
    s.rotation = msg_to_struct(m.rotation);
    return s;
}



} // namespace converter
} // namespace hv
