// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_prediction_msgs/msg/prediction_agent_list.hpp"
#include "hv_prediction_msgs/msg/prediction_trajectory_point.hpp"
#include "hv_prediction_msgs/msg/prediction_header.hpp"
#include "hv_prediction_msgs/msg/prediction_trajectory.hpp"
#include "hv_prediction_msgs/msg/prediction_agent.hpp"

// Include interface headers
#include "hv_interface/prediction/prediction.h"

// Include dependent converters
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_prediction_msgs::msg::PredictionHeader struct_to_msg(const hv::interface::PredictionHeader& s);
inline hv::interface::PredictionHeader msg_to_struct(const hv_prediction_msgs::msg::PredictionHeader& m);
inline hv_prediction_msgs::msg::PredictionTrajectoryPoint struct_to_msg(const hv::interface::PredictionTrajectoryPoint& s);
inline hv::interface::PredictionTrajectoryPoint msg_to_struct(const hv_prediction_msgs::msg::PredictionTrajectoryPoint& m);
inline hv_prediction_msgs::msg::PredictionTrajectory struct_to_msg(const hv::interface::PredictionTrajectory& s);
inline hv::interface::PredictionTrajectory msg_to_struct(const hv_prediction_msgs::msg::PredictionTrajectory& m);
inline hv_prediction_msgs::msg::PredictionAgent struct_to_msg(const hv::interface::PredictionAgent& s);
inline hv::interface::PredictionAgent msg_to_struct(const hv_prediction_msgs::msg::PredictionAgent& m);
inline hv_prediction_msgs::msg::PredictionAgentList struct_to_msg(const hv::interface::PredictionAgentList& s);
inline hv::interface::PredictionAgentList msg_to_struct(const hv_prediction_msgs::msg::PredictionAgentList& m);

inline hv_prediction_msgs::msg::PredictionHeader struct_to_msg(const hv::interface::PredictionHeader& s) {
    hv_prediction_msgs::msg::PredictionHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.major_version = s.major_version;
    m.minor_version = s.minor_version;
    m.num_valid_angents = s.num_valid_angents;
    return m;
}


inline hv::interface::PredictionHeader msg_to_struct(const hv_prediction_msgs::msg::PredictionHeader& m) {
    hv::interface::PredictionHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.major_version = m.major_version;
    s.minor_version = m.minor_version;
    s.num_valid_angents = m.num_valid_angents;
    return s;
}


inline hv_prediction_msgs::msg::PredictionTrajectoryPoint struct_to_msg(const hv::interface::PredictionTrajectoryPoint& s) {
    hv_prediction_msgs::msg::PredictionTrajectoryPoint m;
    m.point = struct_to_msg(s.point);
    m.velocity = s.velocity;
    m.heading = s.heading;
    m.sigma = s.sigma;
    return m;
}


inline hv::interface::PredictionTrajectoryPoint msg_to_struct(const hv_prediction_msgs::msg::PredictionTrajectoryPoint& m) {
    hv::interface::PredictionTrajectoryPoint s;
    s.point = msg_to_struct(m.point);
    s.velocity = m.velocity;
    s.heading = m.heading;
    s.sigma = m.sigma;
    return s;
}


inline hv_prediction_msgs::msg::PredictionTrajectory struct_to_msg(const hv::interface::PredictionTrajectory& s) {
    hv_prediction_msgs::msg::PredictionTrajectory m;
    m.confidence = s.confidence;
    m.num_valid_points = s.num_valid_points;
    m.num_spaese_index = s.num_spaese_index;
    m.dense_sample_interval = s.dense_sample_interval;
    m.sparse_sample_interval = s.sparse_sample_interval;
    m.target_lane_id = s.target_lane_id;
    m.intent = static_cast<int32_t>(s.intent);
    m.type = static_cast<int32_t>(s.type);
    m.points.reserve(s.points.size());
    for (const auto& element : s.points) {
        m.points.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::PredictionTrajectory msg_to_struct(const hv_prediction_msgs::msg::PredictionTrajectory& m) {
    hv::interface::PredictionTrajectory s;
    s.confidence = m.confidence;
    s.num_valid_points = m.num_valid_points;
    s.num_spaese_index = m.num_spaese_index;
    s.dense_sample_interval = m.dense_sample_interval;
    s.sparse_sample_interval = m.sparse_sample_interval;
    s.target_lane_id = m.target_lane_id;
    s.intent = static_cast<hv::interface::PredictionTrajectoryIntent>(m.intent);
    s.type = static_cast<hv::interface::PredictionTrajectoryType>(m.type);
    s.points.reserve(m.points.size());
    for (const auto& element : m.points) s.points.push_back(msg_to_struct(element));
    return s;
}


inline hv_prediction_msgs::msg::PredictionAgent struct_to_msg(const hv::interface::PredictionAgent& s) {
    hv_prediction_msgs::msg::PredictionAgent m;
    m.agent_id = s.agent_id;
    m.prediction_trajectories.reserve(s.prediction_trajectories.size());
    for (const auto& element : s.prediction_trajectories) {
        m.prediction_trajectories.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::PredictionAgent msg_to_struct(const hv_prediction_msgs::msg::PredictionAgent& m) {
    hv::interface::PredictionAgent s;
    s.agent_id = m.agent_id;
    s.prediction_trajectories.reserve(m.prediction_trajectories.size());
    for (const auto& element : m.prediction_trajectories) s.prediction_trajectories.push_back(msg_to_struct(element));
    return s;
}


inline hv_prediction_msgs::msg::PredictionAgentList struct_to_msg(const hv::interface::PredictionAgentList& s) {
    hv_prediction_msgs::msg::PredictionAgentList m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.prediction_agents.reserve(s.prediction_agents.size());
    for (const auto& element : s.prediction_agents) {
        m.prediction_agents.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::PredictionAgentList msg_to_struct(const hv_prediction_msgs::msg::PredictionAgentList& m) {
    hv::interface::PredictionAgentList s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.prediction_agents.reserve(m.prediction_agents.size());
    for (const auto& element : m.prediction_agents) s.prediction_agents.push_back(msg_to_struct(element));
    return s;
}



} // namespace converter
} // namespace hv
