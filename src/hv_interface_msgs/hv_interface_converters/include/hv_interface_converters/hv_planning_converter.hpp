// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_planning_msgs/msg/estop.hpp"
#include "hv_planning_msgs/msg/critical_region.hpp"
#include "hv_planning_msgs/msg/task_stats.hpp"
#include "hv_planning_msgs/msg/trajectory_point.hpp"
#include "hv_planning_msgs/msg/object_decision.hpp"
#include "hv_planning_msgs/msg/planning_debug.hpp"
#include "hv_planning_msgs/msg/ego_intent.hpp"
#include "hv_planning_msgs/msg/planning_header.hpp"
#include "hv_planning_msgs/msg/latency_stats.hpp"
#include "hv_planning_msgs/msg/object_decisions.hpp"
#include "hv_planning_msgs/msg/main_decision.hpp"
#include "hv_planning_msgs/msg/object_decision_type.hpp"
#include "hv_planning_msgs/msg/trajectory.hpp"
#include "hv_planning_msgs/msg/vehicle_signal.hpp"
#include "hv_planning_msgs/msg/path_point.hpp"
#include "hv_planning_msgs/msg/engage_advice.hpp"
#include "hv_planning_msgs/msg/vehicle_decision_status.hpp"
#include "hv_planning_msgs/msg/route_horizon.hpp"
#include "hv_planning_msgs/msg/target_lane.hpp"
#include "hv_planning_msgs/msg/decision_result.hpp"
#include "hv_planning_msgs/msg/lane_speed_limit.hpp"

// Include interface headers
#include "hv_interface/planning/trajectory.h"
#include "hv_interface/planning/planning_debug.h"

// Include dependent converters
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_planning_msgs::msg::PlanningHeader struct_to_msg(const hv::interface::PlanningHeader& s);
inline hv::interface::PlanningHeader msg_to_struct(const hv_planning_msgs::msg::PlanningHeader& m);
inline hv_planning_msgs::msg::CriticalRegion struct_to_msg(const hv::interface::CriticalRegion& s);
inline hv::interface::CriticalRegion msg_to_struct(const hv_planning_msgs::msg::CriticalRegion& m);
inline hv_planning_msgs::msg::PathPoint struct_to_msg(const hv::interface::PathPoint& s);
inline hv::interface::PathPoint msg_to_struct(const hv_planning_msgs::msg::PathPoint& m);
inline hv_planning_msgs::msg::TrajectoryPoint struct_to_msg(const hv::interface::TrajectoryPoint& s);
inline hv::interface::TrajectoryPoint msg_to_struct(const hv_planning_msgs::msg::TrajectoryPoint& m);
inline hv_planning_msgs::msg::Estop struct_to_msg(const hv::interface::Estop& s);
inline hv::interface::Estop msg_to_struct(const hv_planning_msgs::msg::Estop& m);
inline hv_planning_msgs::msg::TargetLane struct_to_msg(const hv::interface::TargetLane& s);
inline hv::interface::TargetLane msg_to_struct(const hv_planning_msgs::msg::TargetLane& m);
inline hv_planning_msgs::msg::MainDecision struct_to_msg(const hv::interface::MainDecision& s);
inline hv::interface::MainDecision msg_to_struct(const hv_planning_msgs::msg::MainDecision& m);
inline hv_planning_msgs::msg::ObjectDecisionType struct_to_msg(const hv::interface::ObjectDecisionType& s);
inline hv::interface::ObjectDecisionType msg_to_struct(const hv_planning_msgs::msg::ObjectDecisionType& m);
inline hv_planning_msgs::msg::ObjectDecision struct_to_msg(const hv::interface::ObjectDecision& s);
inline hv::interface::ObjectDecision msg_to_struct(const hv_planning_msgs::msg::ObjectDecision& m);
inline hv_planning_msgs::msg::ObjectDecisions struct_to_msg(const hv::interface::ObjectDecisions& s);
inline hv::interface::ObjectDecisions msg_to_struct(const hv_planning_msgs::msg::ObjectDecisions& m);
inline hv_planning_msgs::msg::VehicleSignal struct_to_msg(const hv::interface::VehicleSignal& s);
inline hv::interface::VehicleSignal msg_to_struct(const hv_planning_msgs::msg::VehicleSignal& m);
inline hv_planning_msgs::msg::VehicleDecisionStatus struct_to_msg(const hv::interface::VehicleDecisionStatus& s);
inline hv::interface::VehicleDecisionStatus msg_to_struct(const hv_planning_msgs::msg::VehicleDecisionStatus& m);
inline hv_planning_msgs::msg::DecisionResult struct_to_msg(const hv::interface::DecisionResult& s);
inline hv::interface::DecisionResult msg_to_struct(const hv_planning_msgs::msg::DecisionResult& m);
inline hv_planning_msgs::msg::TaskStats struct_to_msg(const hv::interface::TaskStats& s);
inline hv::interface::TaskStats msg_to_struct(const hv_planning_msgs::msg::TaskStats& m);
inline hv_planning_msgs::msg::LatencyStats struct_to_msg(const hv::interface::LatencyStats& s);
inline hv::interface::LatencyStats msg_to_struct(const hv_planning_msgs::msg::LatencyStats& m);
inline hv_planning_msgs::msg::EngageAdvice struct_to_msg(const hv::interface::EngageAdvice& s);
inline hv::interface::EngageAdvice msg_to_struct(const hv_planning_msgs::msg::EngageAdvice& m);
inline hv_planning_msgs::msg::LaneSpeedLimit struct_to_msg(const hv::interface::LaneSpeedLimit& s);
inline hv::interface::LaneSpeedLimit msg_to_struct(const hv_planning_msgs::msg::LaneSpeedLimit& m);
inline hv_planning_msgs::msg::RouteHorizon struct_to_msg(const hv::interface::RouteHorizon& s);
inline hv::interface::RouteHorizon msg_to_struct(const hv_planning_msgs::msg::RouteHorizon& m);
inline hv_planning_msgs::msg::EgoIntent struct_to_msg(const hv::interface::EgoIntent& s);
inline hv::interface::EgoIntent msg_to_struct(const hv_planning_msgs::msg::EgoIntent& m);
inline hv_planning_msgs::msg::Trajectory struct_to_msg(const hv::interface::Trajectory& s);
inline hv::interface::Trajectory msg_to_struct(const hv_planning_msgs::msg::Trajectory& m);
inline hv_planning_msgs::msg::PlanningDebug struct_to_msg(const hv::interface::PlanningDebug& s);
inline hv::interface::PlanningDebug msg_to_struct(const hv_planning_msgs::msg::PlanningDebug& m);

inline hv_planning_msgs::msg::PlanningHeader struct_to_msg(const hv::interface::PlanningHeader& s) {
    hv_planning_msgs::msg::PlanningHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.module_name = s.module_name;
    m.frame_sequence = s.frame_sequence;
    m.version = s.version;
    m.frame_id = s.frame_id;
    return m;
}


inline hv::interface::PlanningHeader msg_to_struct(const hv_planning_msgs::msg::PlanningHeader& m) {
    hv::interface::PlanningHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.module_name = m.module_name;
    s.frame_sequence = m.frame_sequence;
    s.version = m.version;
    s.frame_id = m.frame_id;
    return s;
}


inline hv_planning_msgs::msg::CriticalRegion struct_to_msg(const hv::interface::CriticalRegion& s) {
    hv_planning_msgs::msg::CriticalRegion m;
    m.region.reserve(s.region.size());
    for (const auto& element : s.region) {
        m.region.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::CriticalRegion msg_to_struct(const hv_planning_msgs::msg::CriticalRegion& m) {
    hv::interface::CriticalRegion s;
    s.region.reserve(m.region.size());
    for (const auto& element : m.region) s.region.push_back(msg_to_struct(element));
    return s;
}


inline hv_planning_msgs::msg::PathPoint struct_to_msg(const hv::interface::PathPoint& s) {
    hv_planning_msgs::msg::PathPoint m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    m.theta = s.theta;
    m.kappa = s.kappa;
    m.s = s.s;
    m.dkappa = s.dkappa;
    m.ddkappa = s.ddkappa;
    m.lane_id = s.lane_id;
    m.x_derivative = s.x_derivative;
    m.y_derivative = s.y_derivative;
    return m;
}


inline hv::interface::PathPoint msg_to_struct(const hv_planning_msgs::msg::PathPoint& m) {
    hv::interface::PathPoint s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    s.theta = m.theta;
    s.kappa = m.kappa;
    s.s = m.s;
    s.dkappa = m.dkappa;
    s.ddkappa = m.ddkappa;
    s.lane_id = m.lane_id;
    s.x_derivative = m.x_derivative;
    s.y_derivative = m.y_derivative;
    return s;
}


inline hv_planning_msgs::msg::TrajectoryPoint struct_to_msg(const hv::interface::TrajectoryPoint& s) {
    hv_planning_msgs::msg::TrajectoryPoint m;
    m.path_point = struct_to_msg(s.path_point);
    m.v = s.v;
    m.a = s.a;
    m.relative_time = s.relative_time;
    m.da = s.da;
    m.steer = s.steer;
    return m;
}


inline hv::interface::TrajectoryPoint msg_to_struct(const hv_planning_msgs::msg::TrajectoryPoint& m) {
    hv::interface::TrajectoryPoint s;
    s.path_point = msg_to_struct(m.path_point);
    s.v = m.v;
    s.a = m.a;
    s.relative_time = m.relative_time;
    s.da = m.da;
    s.steer = m.steer;
    return s;
}


inline hv_planning_msgs::msg::Estop struct_to_msg(const hv::interface::Estop& s) {
    hv_planning_msgs::msg::Estop m;
    m.is_estop = s.is_estop;
    m.reason = s.reason;
    return m;
}


inline hv::interface::Estop msg_to_struct(const hv_planning_msgs::msg::Estop& m) {
    hv::interface::Estop s;
    s.is_estop = m.is_estop;
    s.reason = m.reason;
    return s;
}


inline hv_planning_msgs::msg::TargetLane struct_to_msg(const hv::interface::TargetLane& s) {
    hv_planning_msgs::msg::TargetLane m;
    m.id = s.id;
    m.start_s = s.start_s;
    m.end_s = s.end_s;
    m.speed_limit = s.speed_limit;
    return m;
}


inline hv::interface::TargetLane msg_to_struct(const hv_planning_msgs::msg::TargetLane& m) {
    hv::interface::TargetLane s;
    s.id = m.id;
    s.start_s = m.start_s;
    s.end_s = m.end_s;
    s.speed_limit = m.speed_limit;
    return s;
}


inline hv_planning_msgs::msg::MainDecision struct_to_msg(const hv::interface::MainDecision& s) {
    hv_planning_msgs::msg::MainDecision m;
    m.task = static_cast<int32_t>(s.task);
    m.target_lane.reserve(s.target_lane.size());
    for (const auto& element : s.target_lane) {
        m.target_lane.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::MainDecision msg_to_struct(const hv_planning_msgs::msg::MainDecision& m) {
    hv::interface::MainDecision s;
    s.task = static_cast<hv::interface::Task>(m.task);
    s.target_lane.reserve(m.target_lane.size());
    for (const auto& element : m.target_lane) s.target_lane.push_back(msg_to_struct(element));
    return s;
}


inline hv_planning_msgs::msg::ObjectDecisionType struct_to_msg(const hv::interface::ObjectDecisionType& s) {
    hv_planning_msgs::msg::ObjectDecisionType m;
    m.object_tag = static_cast<int32_t>(s.object_tag);
    return m;
}


inline hv::interface::ObjectDecisionType msg_to_struct(const hv_planning_msgs::msg::ObjectDecisionType& m) {
    hv::interface::ObjectDecisionType s;
    s.object_tag = static_cast<hv::interface::ObjectTag>(m.object_tag);
    return s;
}


inline hv_planning_msgs::msg::ObjectDecision struct_to_msg(const hv::interface::ObjectDecision& s) {
    hv_planning_msgs::msg::ObjectDecision m;
    m.id = s.id;
    m.perception_id = s.perception_id;
    m.object_decisions.reserve(s.object_decisions.size());
    for (const auto& element : s.object_decisions) {
        m.object_decisions.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::ObjectDecision msg_to_struct(const hv_planning_msgs::msg::ObjectDecision& m) {
    hv::interface::ObjectDecision s;
    s.id = m.id;
    s.perception_id = m.perception_id;
    s.object_decisions.reserve(m.object_decisions.size());
    for (const auto& element : m.object_decisions) s.object_decisions.push_back(msg_to_struct(element));
    return s;
}


inline hv_planning_msgs::msg::ObjectDecisions struct_to_msg(const hv::interface::ObjectDecisions& s) {
    hv_planning_msgs::msg::ObjectDecisions m;
    m.decision.reserve(s.decision.size());
    for (const auto& element : s.decision) {
        m.decision.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::ObjectDecisions msg_to_struct(const hv_planning_msgs::msg::ObjectDecisions& m) {
    hv::interface::ObjectDecisions s;
    s.decision.reserve(m.decision.size());
    for (const auto& element : m.decision) s.decision.push_back(msg_to_struct(element));
    return s;
}


inline hv_planning_msgs::msg::VehicleSignal struct_to_msg(const hv::interface::VehicleSignal& s) {
    hv_planning_msgs::msg::VehicleSignal m;
    m.turn_signal = static_cast<int32_t>(s.turn_signal);
    m.high_beam = s.high_beam;
    m.low_beam = s.low_beam;
    m.horn = s.horn;
    m.emergency_light = s.emergency_light;
    return m;
}


inline hv::interface::VehicleSignal msg_to_struct(const hv_planning_msgs::msg::VehicleSignal& m) {
    hv::interface::VehicleSignal s;
    s.turn_signal = static_cast<hv::interface::TurnSignal>(m.turn_signal);
    s.high_beam = m.high_beam;
    s.low_beam = m.low_beam;
    s.horn = m.horn;
    s.emergency_light = m.emergency_light;
    return s;
}


inline hv_planning_msgs::msg::VehicleDecisionStatus struct_to_msg(const hv::interface::VehicleDecisionStatus& s) {
    hv_planning_msgs::msg::VehicleDecisionStatus m;
    m.timestamp = s.timestamp;
    m.scene = static_cast<int32_t>(s.scene);
    m.decision = static_cast<int32_t>(s.decision);
    return m;
}


inline hv::interface::VehicleDecisionStatus msg_to_struct(const hv_planning_msgs::msg::VehicleDecisionStatus& m) {
    hv::interface::VehicleDecisionStatus s;
    s.timestamp = m.timestamp;
    s.scene = static_cast<hv::interface::Scene>(m.scene);
    s.decision = static_cast<hv::interface::Decision>(m.decision);
    return s;
}


inline hv_planning_msgs::msg::DecisionResult struct_to_msg(const hv::interface::DecisionResult& s) {
    hv_planning_msgs::msg::DecisionResult m;
    m.main_decision = struct_to_msg(s.main_decision);
    m.object_decision = struct_to_msg(s.object_decision);
    m.vehicle_signal = struct_to_msg(s.vehicle_signal);
    return m;
}


inline hv::interface::DecisionResult msg_to_struct(const hv_planning_msgs::msg::DecisionResult& m) {
    hv::interface::DecisionResult s;
    s.main_decision = msg_to_struct(m.main_decision);
    s.object_decision = msg_to_struct(m.object_decision);
    s.vehicle_signal = msg_to_struct(m.vehicle_signal);
    return s;
}


inline hv_planning_msgs::msg::TaskStats struct_to_msg(const hv::interface::TaskStats& s) {
    hv_planning_msgs::msg::TaskStats m;
    m.name = s.name;
    m.time_ms = s.time_ms;
    return m;
}


inline hv::interface::TaskStats msg_to_struct(const hv_planning_msgs::msg::TaskStats& m) {
    hv::interface::TaskStats s;
    s.name = m.name;
    s.time_ms = m.time_ms;
    return s;
}


inline hv_planning_msgs::msg::LatencyStats struct_to_msg(const hv::interface::LatencyStats& s) {
    hv_planning_msgs::msg::LatencyStats m;
    m.total_time_ms = s.total_time_ms;
    m.task_stats.reserve(s.task_stats.size());
    for (const auto& element : s.task_stats) {
        m.task_stats.push_back(struct_to_msg(element));
    }
    m.init_frame_time_ms = s.init_frame_time_ms;
    return m;
}


inline hv::interface::LatencyStats msg_to_struct(const hv_planning_msgs::msg::LatencyStats& m) {
    hv::interface::LatencyStats s;
    s.total_time_ms = m.total_time_ms;
    s.task_stats.reserve(m.task_stats.size());
    for (const auto& element : m.task_stats) s.task_stats.push_back(msg_to_struct(element));
    s.init_frame_time_ms = m.init_frame_time_ms;
    return s;
}


inline hv_planning_msgs::msg::EngageAdvice struct_to_msg(const hv::interface::EngageAdvice& s) {
    hv_planning_msgs::msg::EngageAdvice m;
    m.advice = static_cast<int32_t>(s.advice);
    m.reason = s.reason;
    return m;
}


inline hv::interface::EngageAdvice msg_to_struct(const hv_planning_msgs::msg::EngageAdvice& m) {
    hv::interface::EngageAdvice s;
    s.advice = static_cast<hv::interface::Advice>(m.advice);
    s.reason = m.reason;
    return s;
}


inline hv_planning_msgs::msg::LaneSpeedLimit struct_to_msg(const hv::interface::LaneSpeedLimit& s) {
    hv_planning_msgs::msg::LaneSpeedLimit m;
    m.speed_limit = s.speed_limit;
    return m;
}


inline hv::interface::LaneSpeedLimit msg_to_struct(const hv_planning_msgs::msg::LaneSpeedLimit& m) {
    hv::interface::LaneSpeedLimit s;
    s.speed_limit = m.speed_limit;
    return s;
}


inline hv_planning_msgs::msg::RouteHorizon struct_to_msg(const hv::interface::RouteHorizon& s) {
    hv_planning_msgs::msg::RouteHorizon m;
    m.timestamp = s.timestamp;
    m.lane_ids = s.lane_ids;
    return m;
}


inline hv::interface::RouteHorizon msg_to_struct(const hv_planning_msgs::msg::RouteHorizon& m) {
    hv::interface::RouteHorizon s;
    s.timestamp = m.timestamp;
    s.lane_ids = m.lane_ids;
    return s;
}


inline hv_planning_msgs::msg::EgoIntent struct_to_msg(const hv::interface::EgoIntent& s) {
    hv_planning_msgs::msg::EgoIntent m;
    m.lateral_intents.reserve(s.lateral_intents.size());
    for (const auto& element : s.lateral_intents) {
        m.lateral_intents.push_back(static_cast<int32_t>(element));
    }
    m.lateral_index = s.lateral_index;
    m.longitudinal_intens.reserve(s.longitudinal_intens.size());
    for (const auto& element : s.longitudinal_intens) {
        m.longitudinal_intens.push_back(static_cast<int32_t>(element));
    }
    m.longitudinal_index = s.longitudinal_index;
    return m;
}


inline hv::interface::EgoIntent msg_to_struct(const hv_planning_msgs::msg::EgoIntent& m) {
    hv::interface::EgoIntent s;
    s.lateral_intents.reserve(m.lateral_intents.size());
    for (const auto& element : m.lateral_intents) s.lateral_intents.push_back(static_cast<hv::interface::LateralIntent>(element));
    s.lateral_index = m.lateral_index;
    s.longitudinal_intens.reserve(m.longitudinal_intens.size());
    for (const auto& element : m.longitudinal_intens) s.longitudinal_intens.push_back(static_cast<hv::interface::LongitudinalIntent>(element));
    s.longitudinal_index = m.longitudinal_index;
    return s;
}


inline hv_planning_msgs::msg::Trajectory struct_to_msg(const hv::interface::Trajectory& s) {
    hv_planning_msgs::msg::Trajectory m;
    m.header = struct_to_msg(s.header);
    m.planning_header = struct_to_msg(s.planning_header);
    m.total_path_length = s.total_path_length;
    m.total_path_time = s.total_path_time;
    m.trajectory_point.reserve(s.trajectory_point.size());
    for (const auto& element : s.trajectory_point) {
        m.trajectory_point.push_back(struct_to_msg(element));
    }
    m.estop = struct_to_msg(s.estop);
    m.path_point.reserve(s.path_point.size());
    for (const auto& element : s.path_point) {
        m.path_point.push_back(struct_to_msg(element));
    }
    m.is_replan = s.is_replan;
    m.replan_reason = s.replan_reason;
    m.is_uturn = s.is_uturn;
    m.gear = static_cast<int32_t>(s.gear);
    m.decision = struct_to_msg(s.decision);
    m.latency_stats = struct_to_msg(s.latency_stats);
    m.routing_header = struct_to_msg(s.routing_header);
    m.right_of_way_status = static_cast<int32_t>(s.right_of_way_status);
    m.lane_id = s.lane_id;
    m.engage_advice = struct_to_msg(s.engage_advice);
    m.critical_region = struct_to_msg(s.critical_region);
    m.trajectory_type = static_cast<int32_t>(s.trajectory_type);
    m.target_lane_id = s.target_lane_id;
    m.ego_intent = struct_to_msg(s.ego_intent);
    return m;
}


inline hv::interface::Trajectory msg_to_struct(const hv_planning_msgs::msg::Trajectory& m) {
    hv::interface::Trajectory s;
    s.header = msg_to_struct(m.header);
    s.planning_header = msg_to_struct(m.planning_header);
    s.total_path_length = m.total_path_length;
    s.total_path_time = m.total_path_time;
    s.trajectory_point.reserve(m.trajectory_point.size());
    for (const auto& element : m.trajectory_point) s.trajectory_point.push_back(msg_to_struct(element));
    s.estop = msg_to_struct(m.estop);
    s.path_point.reserve(m.path_point.size());
    for (const auto& element : m.path_point) s.path_point.push_back(msg_to_struct(element));
    s.is_replan = m.is_replan;
    s.replan_reason = m.replan_reason;
    s.is_uturn = m.is_uturn;
    s.gear = static_cast<hv::interface::GearPosition>(m.gear);
    s.decision = msg_to_struct(m.decision);
    s.latency_stats = msg_to_struct(m.latency_stats);
    s.routing_header = msg_to_struct(m.routing_header);
    s.right_of_way_status = static_cast<hv::interface::RightOfWayStatus>(m.right_of_way_status);
    s.lane_id = m.lane_id;
    s.engage_advice = msg_to_struct(m.engage_advice);
    s.critical_region = msg_to_struct(m.critical_region);
    s.trajectory_type = static_cast<hv::interface::TrajectoryType>(m.trajectory_type);
    s.target_lane_id = m.target_lane_id;
    s.ego_intent = msg_to_struct(m.ego_intent);
    return s;
}


inline hv_planning_msgs::msg::PlanningDebug struct_to_msg(const hv::interface::PlanningDebug& s) {
    hv_planning_msgs::msg::PlanningDebug m;
    m.header = struct_to_msg(s.header);
    m.data = s.data;
    return m;
}


inline hv::interface::PlanningDebug msg_to_struct(const hv_planning_msgs::msg::PlanningDebug& m) {
    hv::interface::PlanningDebug s;
    s.header = msg_to_struct(m.header);
    s.data = m.data;
    return s;
}



} // namespace converter
} // namespace hv
