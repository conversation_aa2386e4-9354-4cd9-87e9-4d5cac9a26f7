// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_remote_msgs/msg/remote_header.hpp"
#include "hv_remote_msgs/msg/remote_window_control.hpp"
#include "hv_remote_msgs/msg/remote_comfort_control.hpp"
#include "hv_remote_msgs/msg/remote_chassis_control.hpp"
#include "hv_remote_msgs/msg/remote_door_control.hpp"
#include "hv_remote_msgs/msg/remote_seat_control.hpp"
#include "hv_remote_msgs/msg/remote_air_conditioning_control.hpp"
#include "hv_remote_msgs/msg/remote_lighting_control.hpp"

// Include interface headers
#include "hv_interface/remote/remote_control.h"

// Include dependent converters
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_remote_msgs::msg::RemoteHeader struct_to_msg(const hv::interface::RemoteHeader& s);
inline hv::interface::RemoteHeader msg_to_struct(const hv_remote_msgs::msg::RemoteHeader& m);
inline hv_remote_msgs::msg::RemoteChassisControl struct_to_msg(const hv::interface::RemoteChassisControl& s);
inline hv::interface::RemoteChassisControl msg_to_struct(const hv_remote_msgs::msg::RemoteChassisControl& m);
inline hv_remote_msgs::msg::RemoteLightingControl struct_to_msg(const hv::interface::RemoteLightingControl& s);
inline hv::interface::RemoteLightingControl msg_to_struct(const hv_remote_msgs::msg::RemoteLightingControl& m);
inline hv_remote_msgs::msg::RemoteDoorControl struct_to_msg(const hv::interface::RemoteDoorControl& s);
inline hv::interface::RemoteDoorControl msg_to_struct(const hv_remote_msgs::msg::RemoteDoorControl& m);
inline hv_remote_msgs::msg::RemoteWindowControl struct_to_msg(const hv::interface::RemoteWindowControl& s);
inline hv::interface::RemoteWindowControl msg_to_struct(const hv_remote_msgs::msg::RemoteWindowControl& m);
inline hv_remote_msgs::msg::RemoteSeatControl struct_to_msg(const hv::interface::RemoteSeatControl& s);
inline hv::interface::RemoteSeatControl msg_to_struct(const hv_remote_msgs::msg::RemoteSeatControl& m);
inline hv_remote_msgs::msg::RemoteComfortControl struct_to_msg(const hv::interface::RemoteComfortControl& s);
inline hv::interface::RemoteComfortControl msg_to_struct(const hv_remote_msgs::msg::RemoteComfortControl& m);
inline hv_remote_msgs::msg::RemoteAirConditioningControl struct_to_msg(const hv::interface::RemoteAirConditioningControl& s);
inline hv::interface::RemoteAirConditioningControl msg_to_struct(const hv_remote_msgs::msg::RemoteAirConditioningControl& m);

inline hv_remote_msgs::msg::RemoteHeader struct_to_msg(const hv::interface::RemoteHeader& s) {
    hv_remote_msgs::msg::RemoteHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.frame_sequence = s.frame_sequence;
    return m;
}


inline hv::interface::RemoteHeader msg_to_struct(const hv_remote_msgs::msg::RemoteHeader& m) {
    hv::interface::RemoteHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.frame_sequence = m.frame_sequence;
    return s;
}


inline hv_remote_msgs::msg::RemoteChassisControl struct_to_msg(const hv::interface::RemoteChassisControl& s) {
    hv_remote_msgs::msg::RemoteChassisControl m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.command_type = static_cast<int32_t>(s.command_type);
    m.gear_position = s.gear_position;
    m.brake_position = s.brake_position;
    m.throttle_position = s.throttle_position;
    m.target_steering_angle = s.target_steering_angle;
    return m;
}


inline hv::interface::RemoteChassisControl msg_to_struct(const hv_remote_msgs::msg::RemoteChassisControl& m) {
    hv::interface::RemoteChassisControl s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.command_type = static_cast<hv::interface::RemoteCommandType>(m.command_type);
    s.gear_position = m.gear_position;
    s.brake_position = m.brake_position;
    s.throttle_position = m.throttle_position;
    s.target_steering_angle = m.target_steering_angle;
    return s;
}


inline hv_remote_msgs::msg::RemoteLightingControl struct_to_msg(const hv::interface::RemoteLightingControl& s) {
    hv_remote_msgs::msg::RemoteLightingControl m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.front_light = s.front_light;
    m.hazard_light = s.hazard_light;
    m.width_light = s.width_light;
    m.fog_light = s.fog_light;
    m.turn_light = s.turn_light;
    m.dome_light = s.dome_light;
    return m;
}


inline hv::interface::RemoteLightingControl msg_to_struct(const hv_remote_msgs::msg::RemoteLightingControl& m) {
    hv::interface::RemoteLightingControl s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.front_light = m.front_light;
    s.hazard_light = m.hazard_light;
    s.width_light = m.width_light;
    s.fog_light = m.fog_light;
    s.turn_light = m.turn_light;
    s.dome_light = m.dome_light;
    return s;
}


inline hv_remote_msgs::msg::RemoteDoorControl struct_to_msg(const hv::interface::RemoteDoorControl& s) {
    hv_remote_msgs::msg::RemoteDoorControl m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.fl_door_open = s.fl_door_open;
    m.fr_door_open = s.fr_door_open;
    m.rl_door_open = s.rl_door_open;
    m.rr_door_open = s.rr_door_open;
    m.hood_open = s.hood_open;
    m.trunk_open = s.trunk_open;
    m.charging_door_open = s.charging_door_open;
    return m;
}


inline hv::interface::RemoteDoorControl msg_to_struct(const hv_remote_msgs::msg::RemoteDoorControl& m) {
    hv::interface::RemoteDoorControl s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.fl_door_open = m.fl_door_open;
    s.fr_door_open = m.fr_door_open;
    s.rl_door_open = m.rl_door_open;
    s.rr_door_open = m.rr_door_open;
    s.hood_open = m.hood_open;
    s.trunk_open = m.trunk_open;
    s.charging_door_open = m.charging_door_open;
    return s;
}


inline hv_remote_msgs::msg::RemoteWindowControl struct_to_msg(const hv::interface::RemoteWindowControl& s) {
    hv_remote_msgs::msg::RemoteWindowControl m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.fl_window_position = s.fl_window_position;
    m.fr_window_position = s.fr_window_position;
    m.rl_window_position = s.rl_window_position;
    m.rr_window_position = s.rr_window_position;
    m.sun_roof_position = s.sun_roof_position;
    return m;
}


inline hv::interface::RemoteWindowControl msg_to_struct(const hv_remote_msgs::msg::RemoteWindowControl& m) {
    hv::interface::RemoteWindowControl s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.fl_window_position = m.fl_window_position;
    s.fr_window_position = m.fr_window_position;
    s.rl_window_position = m.rl_window_position;
    s.rr_window_position = m.rr_window_position;
    s.sun_roof_position = m.sun_roof_position;
    return s;
}


inline hv_remote_msgs::msg::RemoteSeatControl struct_to_msg(const hv::interface::RemoteSeatControl& s) {
    hv_remote_msgs::msg::RemoteSeatControl m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.fl_seat_position = s.fl_seat_position;
    m.fr_seat_position = s.fr_seat_position;
    m.rl_seat_position = s.rl_seat_position;
    m.rr_seat_position = s.rr_seat_position;
    m.rm_seat_position = s.rm_seat_position;
    m.fl_seatback_position = s.fl_seatback_position;
    m.fr_seatback_position = s.fr_seatback_position;
    m.rl_seatback_position = s.rl_seatback_position;
    m.rr_seatback_position = s.rr_seatback_position;
    m.rm_seatback_position = s.rm_seatback_position;
    return m;
}


inline hv::interface::RemoteSeatControl msg_to_struct(const hv_remote_msgs::msg::RemoteSeatControl& m) {
    hv::interface::RemoteSeatControl s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.fl_seat_position = m.fl_seat_position;
    s.fr_seat_position = m.fr_seat_position;
    s.rl_seat_position = m.rl_seat_position;
    s.rr_seat_position = m.rr_seat_position;
    s.rm_seat_position = m.rm_seat_position;
    s.fl_seatback_position = m.fl_seatback_position;
    s.fr_seatback_position = m.fr_seatback_position;
    s.rl_seatback_position = m.rl_seatback_position;
    s.rr_seatback_position = m.rr_seatback_position;
    s.rm_seatback_position = m.rm_seatback_position;
    return s;
}


inline hv_remote_msgs::msg::RemoteComfortControl struct_to_msg(const hv::interface::RemoteComfortControl& s) {
    hv_remote_msgs::msg::RemoteComfortControl m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.fl_seat_heating = s.fl_seat_heating;
    m.fr_seat_heating = s.fr_seat_heating;
    m.rl_seat_heating = s.rl_seat_heating;
    m.rr_seat_heating = s.rr_seat_heating;
    m.rm_seat_heating = s.rm_seat_heating;
    m.fl_seat_cooling = s.fl_seat_cooling;
    m.fr_seat_cooling = s.fr_seat_cooling;
    m.rl_seat_cooling = s.rl_seat_cooling;
    m.rr_seat_cooling = s.rr_seat_cooling;
    m.rm_seat_cooling = s.rm_seat_cooling;
    return m;
}


inline hv::interface::RemoteComfortControl msg_to_struct(const hv_remote_msgs::msg::RemoteComfortControl& m) {
    hv::interface::RemoteComfortControl s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.fl_seat_heating = m.fl_seat_heating;
    s.fr_seat_heating = m.fr_seat_heating;
    s.rl_seat_heating = m.rl_seat_heating;
    s.rr_seat_heating = m.rr_seat_heating;
    s.rm_seat_heating = m.rm_seat_heating;
    s.fl_seat_cooling = m.fl_seat_cooling;
    s.fr_seat_cooling = m.fr_seat_cooling;
    s.rl_seat_cooling = m.rl_seat_cooling;
    s.rr_seat_cooling = m.rr_seat_cooling;
    s.rm_seat_cooling = m.rm_seat_cooling;
    return s;
}


inline hv_remote_msgs::msg::RemoteAirConditioningControl struct_to_msg(const hv::interface::RemoteAirConditioningControl& s) {
    hv_remote_msgs::msg::RemoteAirConditioningControl m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.ac_function_status = s.ac_function_status;
    m.ac_mode = s.ac_mode;
    m.ac_temp = s.ac_temp;
    m.ac_fanspeed = s.ac_fanspeed;
    m.ac_air_circulation_mode = s.ac_air_circulation_mode;
    return m;
}


inline hv::interface::RemoteAirConditioningControl msg_to_struct(const hv_remote_msgs::msg::RemoteAirConditioningControl& m) {
    hv::interface::RemoteAirConditioningControl s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.ac_function_status = m.ac_function_status;
    s.ac_mode = m.ac_mode;
    s.ac_temp = m.ac_temp;
    s.ac_fanspeed = m.ac_fanspeed;
    s.ac_air_circulation_mode = m.ac_air_circulation_mode;
    return s;
}



} // namespace converter
} // namespace hv
