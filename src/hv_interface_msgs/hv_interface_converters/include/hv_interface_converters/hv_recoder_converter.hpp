// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_recoder_msgs/msg/recoder_request.hpp"
#include "hv_recoder_msgs/msg/recoder_header.hpp"
#include "hv_recoder_msgs/msg/recoder_request_header.hpp"
#include "hv_recoder_msgs/msg/recoder_event.hpp"
#include "hv_recoder_msgs/msg/single_job_status.hpp"
#include "hv_recoder_msgs/msg/recoder_request_tag.hpp"
#include "hv_recoder_msgs/msg/moudle_version.hpp"

// Include interface headers
#include "hv_interface/recoder/recoder_request.h"
#include "hv_interface/recoder/recoder.h"

// Include dependent converters
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_recoder_msgs::msg::RecoderRequestHeader struct_to_msg(const hv::interface::RecoderRequestHeader& s);
inline hv::interface::RecoderRequestHeader msg_to_struct(const hv_recoder_msgs::msg::RecoderRequestHeader& m);
inline hv_recoder_msgs::msg::RecoderRequestTag struct_to_msg(const hv::interface::RecoderRequestTag& s);
inline hv::interface::RecoderRequestTag msg_to_struct(const hv_recoder_msgs::msg::RecoderRequestTag& m);
inline hv_recoder_msgs::msg::RecoderRequest struct_to_msg(const hv::interface::RecoderRequest& s);
inline hv::interface::RecoderRequest msg_to_struct(const hv_recoder_msgs::msg::RecoderRequest& m);
inline hv_recoder_msgs::msg::MoudleVersion struct_to_msg(const hv::interface::MoudleVersion& s);
inline hv::interface::MoudleVersion msg_to_struct(const hv_recoder_msgs::msg::MoudleVersion& m);
inline hv_recoder_msgs::msg::RecoderHeader struct_to_msg(const hv::interface::RecoderHeader& s);
inline hv::interface::RecoderHeader msg_to_struct(const hv_recoder_msgs::msg::RecoderHeader& m);
inline hv_recoder_msgs::msg::SingleJobStatus struct_to_msg(const hv::interface::SingleJobStatus& s);
inline hv::interface::SingleJobStatus msg_to_struct(const hv_recoder_msgs::msg::SingleJobStatus& m);
inline hv_recoder_msgs::msg::RecoderEvent struct_to_msg(const hv::interface::RecoderEvent& s);
inline hv::interface::RecoderEvent msg_to_struct(const hv_recoder_msgs::msg::RecoderEvent& m);

inline hv_recoder_msgs::msg::RecoderRequestHeader struct_to_msg(const hv::interface::RecoderRequestHeader& s) {
    hv_recoder_msgs::msg::RecoderRequestHeader m;
    m.trigger_timestamp = s.trigger_timestamp;
    m.start_timestamp = s.start_timestamp;
    m.end_timestamp = s.end_timestamp;
    return m;
}


inline hv::interface::RecoderRequestHeader msg_to_struct(const hv_recoder_msgs::msg::RecoderRequestHeader& m) {
    hv::interface::RecoderRequestHeader s;
    s.trigger_timestamp = m.trigger_timestamp;
    s.start_timestamp = m.start_timestamp;
    s.end_timestamp = m.end_timestamp;
    return s;
}


inline hv_recoder_msgs::msg::RecoderRequestTag struct_to_msg(const hv::interface::RecoderRequestTag& s) {
    hv_recoder_msgs::msg::RecoderRequestTag m;
    m.name = s.name;
    m.detail = s.detail;
    return m;
}


inline hv::interface::RecoderRequestTag msg_to_struct(const hv_recoder_msgs::msg::RecoderRequestTag& m) {
    hv::interface::RecoderRequestTag s;
    s.name = m.name;
    s.detail = m.detail;
    return s;
}


inline hv_recoder_msgs::msg::RecoderRequest struct_to_msg(const hv::interface::RecoderRequest& s) {
    hv_recoder_msgs::msg::RecoderRequest m;
    m.header = struct_to_msg(s.header);
    m.recoder_request_header = struct_to_msg(s.recoder_request_header);
    m.source = static_cast<int32_t>(s.source);
    m.details = s.details;
    m.exclude_topics = s.exclude_topics;
    m.include_topics = s.include_topics;
    m.record_time_before = s.record_time_before;
    m.record_time_after = s.record_time_after;
    m.tags.reserve(s.tags.size());
    for (const auto& element : s.tags) {
        m.tags.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::RecoderRequest msg_to_struct(const hv_recoder_msgs::msg::RecoderRequest& m) {
    hv::interface::RecoderRequest s;
    s.header = msg_to_struct(m.header);
    s.recoder_request_header = msg_to_struct(m.recoder_request_header);
    s.source = static_cast<hv::interface::RecoderRequestSource>(m.source);
    s.details = m.details;
    s.exclude_topics = m.exclude_topics;
    s.include_topics = m.include_topics;
    s.record_time_before = m.record_time_before;
    s.record_time_after = m.record_time_after;
    s.tags.reserve(m.tags.size());
    for (const auto& element : m.tags) s.tags.push_back(msg_to_struct(element));
    return s;
}


inline hv_recoder_msgs::msg::MoudleVersion struct_to_msg(const hv::interface::MoudleVersion& s) {
    hv_recoder_msgs::msg::MoudleVersion m;
    m.moudle_name = s.moudle_name;
    m.moudle_version = s.moudle_version;
    return m;
}


inline hv::interface::MoudleVersion msg_to_struct(const hv_recoder_msgs::msg::MoudleVersion& m) {
    hv::interface::MoudleVersion s;
    s.moudle_name = m.moudle_name;
    s.moudle_version = m.moudle_version;
    return s;
}


inline hv_recoder_msgs::msg::RecoderHeader struct_to_msg(const hv::interface::RecoderHeader& s) {
    hv_recoder_msgs::msg::RecoderHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.interface_version = s.interface_version;
    m.moudle_version.reserve(s.moudle_version.size());
    for (const auto& element : s.moudle_version) {
        m.moudle_version.push_back(struct_to_msg(element));
    }
    m.hardware_version = s.hardware_version;
    m.extrinsic_parameters_version = s.extrinsic_parameters_version;
    return m;
}


inline hv::interface::RecoderHeader msg_to_struct(const hv_recoder_msgs::msg::RecoderHeader& m) {
    hv::interface::RecoderHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.interface_version = m.interface_version;
    s.moudle_version.reserve(m.moudle_version.size());
    for (const auto& element : m.moudle_version) s.moudle_version.push_back(msg_to_struct(element));
    s.hardware_version = m.hardware_version;
    s.extrinsic_parameters_version = m.extrinsic_parameters_version;
    return s;
}


inline hv_recoder_msgs::msg::SingleJobStatus struct_to_msg(const hv::interface::SingleJobStatus& s) {
    hv_recoder_msgs::msg::SingleJobStatus m;
    m.start_time = s.start_time;
    m.triger_index = s.triger_index;
    m.filename = s.filename;
    m.status = static_cast<int32_t>(s.status);
    m.details = s.details;
    return m;
}


inline hv::interface::SingleJobStatus msg_to_struct(const hv_recoder_msgs::msg::SingleJobStatus& m) {
    hv::interface::SingleJobStatus s;
    s.start_time = m.start_time;
    s.triger_index = m.triger_index;
    s.filename = m.filename;
    s.status = static_cast<hv::interface::JobStatus>(m.status);
    s.details = m.details;
    return s;
}


inline hv_recoder_msgs::msg::RecoderEvent struct_to_msg(const hv::interface::RecoderEvent& s) {
    hv_recoder_msgs::msg::RecoderEvent m;
    m.header = struct_to_msg(s.header);
    m.list_job_status.reserve(s.list_job_status.size());
    for (const auto& element : s.list_job_status) {
        m.list_job_status.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::RecoderEvent msg_to_struct(const hv_recoder_msgs::msg::RecoderEvent& m) {
    hv::interface::RecoderEvent s;
    s.header = msg_to_struct(m.header);
    s.list_job_status.reserve(m.list_job_status.size());
    for (const auto& element : m.list_job_status) s.list_job_status.push_back(msg_to_struct(element));
    return s;
}



} // namespace converter
} // namespace hv
