// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_map_msgs/msg/map_lane_sample_association.hpp"
#include "hv_map_msgs/msg/map_area.hpp"
#include "hv_map_msgs/msg/map_yield_sign.hpp"
#include "hv_map_msgs/msg/map_signal.hpp"
#include "hv_map_msgs/msg/map_stop_sign.hpp"
#include "hv_map_msgs/msg/map_boundary_polygon.hpp"
#include "hv_map_msgs/msg/map_crosswalk.hpp"
#include "hv_map_msgs/msg/map_header.hpp"
#include "hv_map_msgs/msg/map.hpp"
#include "hv_map_msgs/msg/map_road_section.hpp"
#include "hv_map_msgs/msg/map_road_r_o_i_boundary.hpp"
#include "hv_map_msgs/msg/map_overlap.hpp"
#include "hv_map_msgs/msg/map_junction.hpp"
#include "hv_map_msgs/msg/map_lane_boundary_type_info.hpp"
#include "hv_map_msgs/msg/map_curve.hpp"
#include "hv_map_msgs/msg/map_clear_area.hpp"
#include "hv_map_msgs/msg/map_speed_control.hpp"
#include "hv_map_msgs/msg/map_lane_boundary.hpp"
#include "hv_map_msgs/msg/map_lane.hpp"
#include "hv_map_msgs/msg/map_r_s_u.hpp"
#include "hv_map_msgs/msg/map_speed_bump.hpp"
#include "hv_map_msgs/msg/map_boundary_edge.hpp"
#include "hv_map_msgs/msg/map_barrier_gate.hpp"
#include "hv_map_msgs/msg/map_curve_segment.hpp"
#include "hv_map_msgs/msg/map_road.hpp"
#include "hv_map_msgs/msg/map_parking_space.hpp"
#include "hv_map_msgs/msg/map_road_boundary.hpp"
#include "hv_map_msgs/msg/map_p_n_c_junction.hpp"

// Include interface headers
#include "hv_interface/map/map.h"

// Include dependent converters
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_map_msgs::msg::MapHeader struct_to_msg(const hv::interface::MapHeader& s);
inline hv::interface::MapHeader msg_to_struct(const hv_map_msgs::msg::MapHeader& m);
inline hv_map_msgs::msg::MapCurveSegment struct_to_msg(const hv::interface::MapCurveSegment& s);
inline hv::interface::MapCurveSegment msg_to_struct(const hv_map_msgs::msg::MapCurveSegment& m);
inline hv_map_msgs::msg::MapCurve struct_to_msg(const hv::interface::MapCurve& s);
inline hv::interface::MapCurve msg_to_struct(const hv_map_msgs::msg::MapCurve& m);
inline hv_map_msgs::msg::MapLaneBoundaryTypeInfo struct_to_msg(const hv::interface::MapLaneBoundaryTypeInfo& s);
inline hv::interface::MapLaneBoundaryTypeInfo msg_to_struct(const hv_map_msgs::msg::MapLaneBoundaryTypeInfo& m);
inline hv_map_msgs::msg::MapLaneBoundary struct_to_msg(const hv::interface::MapLaneBoundary& s);
inline hv::interface::MapLaneBoundary msg_to_struct(const hv_map_msgs::msg::MapLaneBoundary& m);
inline hv_map_msgs::msg::MapLaneSampleAssociation struct_to_msg(const hv::interface::MapLaneSampleAssociation& s);
inline hv::interface::MapLaneSampleAssociation msg_to_struct(const hv_map_msgs::msg::MapLaneSampleAssociation& m);
inline hv_map_msgs::msg::MapLane struct_to_msg(const hv::interface::MapLane& s);
inline hv::interface::MapLane msg_to_struct(const hv_map_msgs::msg::MapLane& m);
inline hv_map_msgs::msg::MapBoundaryEdge struct_to_msg(const hv::interface::MapBoundaryEdge& s);
inline hv::interface::MapBoundaryEdge msg_to_struct(const hv_map_msgs::msg::MapBoundaryEdge& m);
inline hv_map_msgs::msg::MapBoundaryPolygon struct_to_msg(const hv::interface::MapBoundaryPolygon& s);
inline hv::interface::MapBoundaryPolygon msg_to_struct(const hv_map_msgs::msg::MapBoundaryPolygon& m);
inline hv_map_msgs::msg::MapRoadBoundary struct_to_msg(const hv::interface::MapRoadBoundary& s);
inline hv::interface::MapRoadBoundary msg_to_struct(const hv_map_msgs::msg::MapRoadBoundary& m);
inline hv_map_msgs::msg::MapRoadROIBoundary struct_to_msg(const hv::interface::MapRoadROIBoundary& s);
inline hv::interface::MapRoadROIBoundary msg_to_struct(const hv_map_msgs::msg::MapRoadROIBoundary& m);
inline hv_map_msgs::msg::MapRoadSection struct_to_msg(const hv::interface::MapRoadSection& s);
inline hv::interface::MapRoadSection msg_to_struct(const hv_map_msgs::msg::MapRoadSection& m);
inline hv_map_msgs::msg::MapRoad struct_to_msg(const hv::interface::MapRoad& s);
inline hv::interface::MapRoad msg_to_struct(const hv_map_msgs::msg::MapRoad& m);
inline hv_map_msgs::msg::MapJunction struct_to_msg(const hv::interface::MapJunction& s);
inline hv::interface::MapJunction msg_to_struct(const hv_map_msgs::msg::MapJunction& m);
inline hv_map_msgs::msg::MapOverlap struct_to_msg(const hv::interface::MapOverlap& s);
inline hv::interface::MapOverlap msg_to_struct(const hv_map_msgs::msg::MapOverlap& m);
inline hv_map_msgs::msg::MapSignal struct_to_msg(const hv::interface::MapSignal& s);
inline hv::interface::MapSignal msg_to_struct(const hv_map_msgs::msg::MapSignal& m);
inline hv_map_msgs::msg::MapStopSign struct_to_msg(const hv::interface::MapStopSign& s);
inline hv::interface::MapStopSign msg_to_struct(const hv_map_msgs::msg::MapStopSign& m);
inline hv_map_msgs::msg::MapYieldSign struct_to_msg(const hv::interface::MapYieldSign& s);
inline hv::interface::MapYieldSign msg_to_struct(const hv_map_msgs::msg::MapYieldSign& m);
inline hv_map_msgs::msg::MapCrosswalk struct_to_msg(const hv::interface::MapCrosswalk& s);
inline hv::interface::MapCrosswalk msg_to_struct(const hv_map_msgs::msg::MapCrosswalk& m);
inline hv_map_msgs::msg::MapParkingSpace struct_to_msg(const hv::interface::MapParkingSpace& s);
inline hv::interface::MapParkingSpace msg_to_struct(const hv_map_msgs::msg::MapParkingSpace& m);
inline hv_map_msgs::msg::MapPNCJunction struct_to_msg(const hv::interface::MapPNCJunction& s);
inline hv::interface::MapPNCJunction msg_to_struct(const hv_map_msgs::msg::MapPNCJunction& m);
inline hv_map_msgs::msg::MapRSU struct_to_msg(const hv::interface::MapRSU& s);
inline hv::interface::MapRSU msg_to_struct(const hv_map_msgs::msg::MapRSU& m);
inline hv_map_msgs::msg::MapArea struct_to_msg(const hv::interface::MapArea& s);
inline hv::interface::MapArea msg_to_struct(const hv_map_msgs::msg::MapArea& m);
inline hv_map_msgs::msg::MapSpeedBump struct_to_msg(const hv::interface::MapSpeedBump& s);
inline hv::interface::MapSpeedBump msg_to_struct(const hv_map_msgs::msg::MapSpeedBump& m);
inline hv_map_msgs::msg::MapSpeedControl struct_to_msg(const hv::interface::MapSpeedControl& s);
inline hv::interface::MapSpeedControl msg_to_struct(const hv_map_msgs::msg::MapSpeedControl& m);
inline hv_map_msgs::msg::MapClearArea struct_to_msg(const hv::interface::MapClearArea& s);
inline hv::interface::MapClearArea msg_to_struct(const hv_map_msgs::msg::MapClearArea& m);
inline hv_map_msgs::msg::MapBarrierGate struct_to_msg(const hv::interface::MapBarrierGate& s);
inline hv::interface::MapBarrierGate msg_to_struct(const hv_map_msgs::msg::MapBarrierGate& m);
inline hv_map_msgs::msg::Map struct_to_msg(const hv::interface::Map& s);
inline hv::interface::Map msg_to_struct(const hv_map_msgs::msg::Map& m);

inline hv_map_msgs::msg::MapHeader struct_to_msg(const hv::interface::MapHeader& s) {
    hv_map_msgs::msg::MapHeader m;
    m.version = s.version;
    m.date = s.date;
    m.projection = s.projection;
    m.district = s.district;
    m.generation = s.generation;
    m.rev_major = s.rev_major;
    m.rev_minor = s.rev_minor;
    m.left = s.left;
    m.top = s.top;
    m.right = s.right;
    m.bottom = s.bottom;
    m.vendor = s.vendor;
    return m;
}


inline hv::interface::MapHeader msg_to_struct(const hv_map_msgs::msg::MapHeader& m) {
    hv::interface::MapHeader s;
    s.version = m.version;
    s.date = m.date;
    s.projection = m.projection;
    s.district = m.district;
    s.generation = m.generation;
    s.rev_major = m.rev_major;
    s.rev_minor = m.rev_minor;
    s.left = m.left;
    s.top = m.top;
    s.right = m.right;
    s.bottom = m.bottom;
    s.vendor = m.vendor;
    return s;
}


inline hv_map_msgs::msg::MapCurveSegment struct_to_msg(const hv::interface::MapCurveSegment& s) {
    hv_map_msgs::msg::MapCurveSegment m;
    m.line_segment = struct_to_msg(s.line_segment);
    m.s = s.s;
    m.start_position = struct_to_msg(s.start_position);
    m.heading = s.heading;
    m.length = s.length;
    return m;
}


inline hv::interface::MapCurveSegment msg_to_struct(const hv_map_msgs::msg::MapCurveSegment& m) {
    hv::interface::MapCurveSegment s;
    s.line_segment = msg_to_struct(m.line_segment);
    s.s = m.s;
    s.start_position = msg_to_struct(m.start_position);
    s.heading = m.heading;
    s.length = m.length;
    return s;
}


inline hv_map_msgs::msg::MapCurve struct_to_msg(const hv::interface::MapCurve& s) {
    hv_map_msgs::msg::MapCurve m;
    m.segments.reserve(s.segments.size());
    for (const auto& element : s.segments) {
        m.segments.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::MapCurve msg_to_struct(const hv_map_msgs::msg::MapCurve& m) {
    hv::interface::MapCurve s;
    s.segments.reserve(m.segments.size());
    for (const auto& element : m.segments) s.segments.push_back(msg_to_struct(element));
    return s;
}


inline hv_map_msgs::msg::MapLaneBoundaryTypeInfo struct_to_msg(const hv::interface::MapLaneBoundaryTypeInfo& s) {
    hv_map_msgs::msg::MapLaneBoundaryTypeInfo m;
    m.s = s.s;
    m.types.reserve(s.types.size());
    for (const auto& element : s.types) {
        m.types.push_back(static_cast<int32_t>(element));
    }
    return m;
}


inline hv::interface::MapLaneBoundaryTypeInfo msg_to_struct(const hv_map_msgs::msg::MapLaneBoundaryTypeInfo& m) {
    hv::interface::MapLaneBoundaryTypeInfo s;
    s.s = m.s;
    s.types.reserve(m.types.size());
    for (const auto& element : m.types) s.types.push_back(static_cast<hv::interface::MapLaneBoundaryType>(element));
    return s;
}


inline hv_map_msgs::msg::MapLaneBoundary struct_to_msg(const hv::interface::MapLaneBoundary& s) {
    hv_map_msgs::msg::MapLaneBoundary m;
    m.curve = struct_to_msg(s.curve);
    m.length = s.length;
    m.virtual_boundary = s.virtual_boundary;
    m.boundary_types.reserve(s.boundary_types.size());
    for (const auto& element : s.boundary_types) {
        m.boundary_types.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::MapLaneBoundary msg_to_struct(const hv_map_msgs::msg::MapLaneBoundary& m) {
    hv::interface::MapLaneBoundary s;
    s.curve = msg_to_struct(m.curve);
    s.length = m.length;
    s.virtual_boundary = m.virtual_boundary;
    s.boundary_types.reserve(m.boundary_types.size());
    for (const auto& element : m.boundary_types) s.boundary_types.push_back(msg_to_struct(element));
    return s;
}


inline hv_map_msgs::msg::MapLaneSampleAssociation struct_to_msg(const hv::interface::MapLaneSampleAssociation& s) {
    hv_map_msgs::msg::MapLaneSampleAssociation m;
    m.s = s.s;
    m.width = s.width;
    return m;
}


inline hv::interface::MapLaneSampleAssociation msg_to_struct(const hv_map_msgs::msg::MapLaneSampleAssociation& m) {
    hv::interface::MapLaneSampleAssociation s;
    s.s = m.s;
    s.width = m.width;
    return s;
}


inline hv_map_msgs::msg::MapLane struct_to_msg(const hv::interface::MapLane& s) {
    hv_map_msgs::msg::MapLane m;
    m.id = s.id;
    m.central_curve = struct_to_msg(s.central_curve);
    m.left_boundary = struct_to_msg(s.left_boundary);
    m.right_boundary = struct_to_msg(s.right_boundary);
    m.length = s.length;
    m.speed_limit = s.speed_limit;
    m.overlap_ids = s.overlap_ids;
    m.predecessor_ids = s.predecessor_ids;
    m.successor_ids = s.successor_ids;
    m.left_neighbor_forward_lane_ids = s.left_neighbor_forward_lane_ids;
    m.right_neighbor_forward_lane_ids = s.right_neighbor_forward_lane_ids;
    m.type = static_cast<int32_t>(s.type);
    m.turn = static_cast<int32_t>(s.turn);
    m.left_neighbor_reverse_lane_ids = s.left_neighbor_reverse_lane_ids;
    m.right_neighbor_reverse_lane_ids = s.right_neighbor_reverse_lane_ids;
    m.junction_id = s.junction_id;
    m.left_samples.reserve(s.left_samples.size());
    for (const auto& element : s.left_samples) {
        m.left_samples.push_back(struct_to_msg(element));
    }
    m.right_samples.reserve(s.right_samples.size());
    for (const auto& element : s.right_samples) {
        m.right_samples.push_back(struct_to_msg(element));
    }
    m.direction = static_cast<int32_t>(s.direction);
    m.left_road_samples.reserve(s.left_road_samples.size());
    for (const auto& element : s.left_road_samples) {
        m.left_road_samples.push_back(struct_to_msg(element));
    }
    m.right_road_samples.reserve(s.right_road_samples.size());
    for (const auto& element : s.right_road_samples) {
        m.right_road_samples.push_back(struct_to_msg(element));
    }
    m.self_reverse_lane_ids = s.self_reverse_lane_ids;
    return m;
}


inline hv::interface::MapLane msg_to_struct(const hv_map_msgs::msg::MapLane& m) {
    hv::interface::MapLane s;
    s.id = m.id;
    s.central_curve = msg_to_struct(m.central_curve);
    s.left_boundary = msg_to_struct(m.left_boundary);
    s.right_boundary = msg_to_struct(m.right_boundary);
    s.length = m.length;
    s.speed_limit = m.speed_limit;
    s.overlap_ids = m.overlap_ids;
    s.predecessor_ids = m.predecessor_ids;
    s.successor_ids = m.successor_ids;
    s.left_neighbor_forward_lane_ids = m.left_neighbor_forward_lane_ids;
    s.right_neighbor_forward_lane_ids = m.right_neighbor_forward_lane_ids;
    s.type = static_cast<hv::interface::MapLaneType>(m.type);
    s.turn = static_cast<hv::interface::MapLaneTurn>(m.turn);
    s.left_neighbor_reverse_lane_ids = m.left_neighbor_reverse_lane_ids;
    s.right_neighbor_reverse_lane_ids = m.right_neighbor_reverse_lane_ids;
    s.junction_id = m.junction_id;
    s.left_samples.reserve(m.left_samples.size());
    for (const auto& element : m.left_samples) s.left_samples.push_back(msg_to_struct(element));
    s.right_samples.reserve(m.right_samples.size());
    for (const auto& element : m.right_samples) s.right_samples.push_back(msg_to_struct(element));
    s.direction = static_cast<hv::interface::MapLaneDirection>(m.direction);
    s.left_road_samples.reserve(m.left_road_samples.size());
    for (const auto& element : m.left_road_samples) s.left_road_samples.push_back(msg_to_struct(element));
    s.right_road_samples.reserve(m.right_road_samples.size());
    for (const auto& element : m.right_road_samples) s.right_road_samples.push_back(msg_to_struct(element));
    s.self_reverse_lane_ids = m.self_reverse_lane_ids;
    return s;
}


inline hv_map_msgs::msg::MapBoundaryEdge struct_to_msg(const hv::interface::MapBoundaryEdge& s) {
    hv_map_msgs::msg::MapBoundaryEdge m;
    m.curve = struct_to_msg(s.curve);
    m.type = static_cast<int32_t>(s.type);
    return m;
}


inline hv::interface::MapBoundaryEdge msg_to_struct(const hv_map_msgs::msg::MapBoundaryEdge& m) {
    hv::interface::MapBoundaryEdge s;
    s.curve = msg_to_struct(m.curve);
    s.type = static_cast<hv::interface::MapBoundaryEdgeType>(m.type);
    return s;
}


inline hv_map_msgs::msg::MapBoundaryPolygon struct_to_msg(const hv::interface::MapBoundaryPolygon& s) {
    hv_map_msgs::msg::MapBoundaryPolygon m;
    m.edges.reserve(s.edges.size());
    for (const auto& element : s.edges) {
        m.edges.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::MapBoundaryPolygon msg_to_struct(const hv_map_msgs::msg::MapBoundaryPolygon& m) {
    hv::interface::MapBoundaryPolygon s;
    s.edges.reserve(m.edges.size());
    for (const auto& element : m.edges) s.edges.push_back(msg_to_struct(element));
    return s;
}


inline hv_map_msgs::msg::MapRoadBoundary struct_to_msg(const hv::interface::MapRoadBoundary& s) {
    hv_map_msgs::msg::MapRoadBoundary m;
    m.outer_polygon = struct_to_msg(s.outer_polygon);
    m.holes.reserve(s.holes.size());
    for (const auto& element : s.holes) {
        m.holes.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::MapRoadBoundary msg_to_struct(const hv_map_msgs::msg::MapRoadBoundary& m) {
    hv::interface::MapRoadBoundary s;
    s.outer_polygon = msg_to_struct(m.outer_polygon);
    s.holes.reserve(m.holes.size());
    for (const auto& element : m.holes) s.holes.push_back(msg_to_struct(element));
    return s;
}


inline hv_map_msgs::msg::MapRoadROIBoundary struct_to_msg(const hv::interface::MapRoadROIBoundary& s) {
    hv_map_msgs::msg::MapRoadROIBoundary m;
    m.id = s.id;
    m.road_boundaries.reserve(s.road_boundaries.size());
    for (const auto& element : s.road_boundaries) {
        m.road_boundaries.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::MapRoadROIBoundary msg_to_struct(const hv_map_msgs::msg::MapRoadROIBoundary& m) {
    hv::interface::MapRoadROIBoundary s;
    s.id = m.id;
    s.road_boundaries.reserve(m.road_boundaries.size());
    for (const auto& element : m.road_boundaries) s.road_boundaries.push_back(msg_to_struct(element));
    return s;
}


inline hv_map_msgs::msg::MapRoadSection struct_to_msg(const hv::interface::MapRoadSection& s) {
    hv_map_msgs::msg::MapRoadSection m;
    m.id = s.id;
    m.lane_ids = s.lane_ids;
    m.boundary = struct_to_msg(s.boundary);
    return m;
}


inline hv::interface::MapRoadSection msg_to_struct(const hv_map_msgs::msg::MapRoadSection& m) {
    hv::interface::MapRoadSection s;
    s.id = m.id;
    s.lane_ids = m.lane_ids;
    s.boundary = msg_to_struct(m.boundary);
    return s;
}


inline hv_map_msgs::msg::MapRoad struct_to_msg(const hv::interface::MapRoad& s) {
    hv_map_msgs::msg::MapRoad m;
    m.id = s.id;
    m.sections.reserve(s.sections.size());
    for (const auto& element : s.sections) {
        m.sections.push_back(struct_to_msg(element));
    }
    m.junction_id = s.junction_id;
    m.type = static_cast<int32_t>(s.type);
    return m;
}


inline hv::interface::MapRoad msg_to_struct(const hv_map_msgs::msg::MapRoad& m) {
    hv::interface::MapRoad s;
    s.id = m.id;
    s.sections.reserve(m.sections.size());
    for (const auto& element : m.sections) s.sections.push_back(msg_to_struct(element));
    s.junction_id = m.junction_id;
    s.type = static_cast<hv::interface::MapRoadType>(m.type);
    return s;
}


inline hv_map_msgs::msg::MapJunction struct_to_msg(const hv::interface::MapJunction& s) {
    hv_map_msgs::msg::MapJunction m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    return m;
}


inline hv::interface::MapJunction msg_to_struct(const hv_map_msgs::msg::MapJunction& m) {
    hv::interface::MapJunction s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    return s;
}


inline hv_map_msgs::msg::MapOverlap struct_to_msg(const hv::interface::MapOverlap& s) {
    hv_map_msgs::msg::MapOverlap m;
    m.id = s.id;
    m.object_ids = s.object_ids;
    return m;
}


inline hv::interface::MapOverlap msg_to_struct(const hv_map_msgs::msg::MapOverlap& m) {
    hv::interface::MapOverlap s;
    s.id = m.id;
    s.object_ids = m.object_ids;
    return s;
}


inline hv_map_msgs::msg::MapSignal struct_to_msg(const hv::interface::MapSignal& s) {
    hv_map_msgs::msg::MapSignal m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    m.sub_signal_ids = s.sub_signal_ids;
    return m;
}


inline hv::interface::MapSignal msg_to_struct(const hv_map_msgs::msg::MapSignal& m) {
    hv::interface::MapSignal s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    s.sub_signal_ids = m.sub_signal_ids;
    return s;
}


inline hv_map_msgs::msg::MapStopSign struct_to_msg(const hv::interface::MapStopSign& s) {
    hv_map_msgs::msg::MapStopSign m;
    m.id = s.id;
    m.overlap_ids = s.overlap_ids;
    m.polygon = struct_to_msg(s.polygon);
    m.stop_line_ids = s.stop_line_ids;
    return m;
}


inline hv::interface::MapStopSign msg_to_struct(const hv_map_msgs::msg::MapStopSign& m) {
    hv::interface::MapStopSign s;
    s.id = m.id;
    s.overlap_ids = m.overlap_ids;
    s.polygon = msg_to_struct(m.polygon);
    s.stop_line_ids = m.stop_line_ids;
    return s;
}


inline hv_map_msgs::msg::MapYieldSign struct_to_msg(const hv::interface::MapYieldSign& s) {
    hv_map_msgs::msg::MapYieldSign m;
    m.id = s.id;
    m.overlap_ids = s.overlap_ids;
    m.polygon = struct_to_msg(s.polygon);
    m.stop_line_ids = s.stop_line_ids;
    return m;
}


inline hv::interface::MapYieldSign msg_to_struct(const hv_map_msgs::msg::MapYieldSign& m) {
    hv::interface::MapYieldSign s;
    s.id = m.id;
    s.overlap_ids = m.overlap_ids;
    s.polygon = msg_to_struct(m.polygon);
    s.stop_line_ids = m.stop_line_ids;
    return s;
}


inline hv_map_msgs::msg::MapCrosswalk struct_to_msg(const hv::interface::MapCrosswalk& s) {
    hv_map_msgs::msg::MapCrosswalk m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    return m;
}


inline hv::interface::MapCrosswalk msg_to_struct(const hv_map_msgs::msg::MapCrosswalk& m) {
    hv::interface::MapCrosswalk s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    return s;
}


inline hv_map_msgs::msg::MapParkingSpace struct_to_msg(const hv::interface::MapParkingSpace& s) {
    hv_map_msgs::msg::MapParkingSpace m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    m.heading = s.heading;
    return m;
}


inline hv::interface::MapParkingSpace msg_to_struct(const hv_map_msgs::msg::MapParkingSpace& m) {
    hv::interface::MapParkingSpace s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    s.heading = m.heading;
    return s;
}


inline hv_map_msgs::msg::MapPNCJunction struct_to_msg(const hv::interface::MapPNCJunction& s) {
    hv_map_msgs::msg::MapPNCJunction m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    m.passage_group_ids = s.passage_group_ids;
    return m;
}


inline hv::interface::MapPNCJunction msg_to_struct(const hv_map_msgs::msg::MapPNCJunction& m) {
    hv::interface::MapPNCJunction s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    s.passage_group_ids = m.passage_group_ids;
    return s;
}


inline hv_map_msgs::msg::MapRSU struct_to_msg(const hv::interface::MapRSU& s) {
    hv_map_msgs::msg::MapRSU m;
    m.id = s.id;
    m.position = struct_to_msg(s.position);
    return m;
}


inline hv::interface::MapRSU msg_to_struct(const hv_map_msgs::msg::MapRSU& m) {
    hv::interface::MapRSU s;
    s.id = m.id;
    s.position = msg_to_struct(m.position);
    return s;
}


inline hv_map_msgs::msg::MapArea struct_to_msg(const hv::interface::MapArea& s) {
    hv_map_msgs::msg::MapArea m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    return m;
}


inline hv::interface::MapArea msg_to_struct(const hv_map_msgs::msg::MapArea& m) {
    hv::interface::MapArea s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    return s;
}


inline hv_map_msgs::msg::MapSpeedBump struct_to_msg(const hv::interface::MapSpeedBump& s) {
    hv_map_msgs::msg::MapSpeedBump m;
    m.id = s.id;
    m.overlap_ids = s.overlap_ids;
    m.position_ids = s.position_ids;
    return m;
}


inline hv::interface::MapSpeedBump msg_to_struct(const hv_map_msgs::msg::MapSpeedBump& m) {
    hv::interface::MapSpeedBump s;
    s.id = m.id;
    s.overlap_ids = m.overlap_ids;
    s.position_ids = m.position_ids;
    return s;
}


inline hv_map_msgs::msg::MapSpeedControl struct_to_msg(const hv::interface::MapSpeedControl& s) {
    hv_map_msgs::msg::MapSpeedControl m;
    m.id = s.id;
    m.overlap_ids = s.overlap_ids;
    m.position_ids = s.position_ids;
    m.speed_limit = s.speed_limit;
    return m;
}


inline hv::interface::MapSpeedControl msg_to_struct(const hv_map_msgs::msg::MapSpeedControl& m) {
    hv::interface::MapSpeedControl s;
    s.id = m.id;
    s.overlap_ids = m.overlap_ids;
    s.position_ids = m.position_ids;
    s.speed_limit = m.speed_limit;
    return s;
}


inline hv_map_msgs::msg::MapClearArea struct_to_msg(const hv::interface::MapClearArea& s) {
    hv_map_msgs::msg::MapClearArea m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    return m;
}


inline hv::interface::MapClearArea msg_to_struct(const hv_map_msgs::msg::MapClearArea& m) {
    hv::interface::MapClearArea s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    return s;
}


inline hv_map_msgs::msg::MapBarrierGate struct_to_msg(const hv::interface::MapBarrierGate& s) {
    hv_map_msgs::msg::MapBarrierGate m;
    m.id = s.id;
    m.polygon = struct_to_msg(s.polygon);
    m.overlap_ids = s.overlap_ids;
    return m;
}


inline hv::interface::MapBarrierGate msg_to_struct(const hv_map_msgs::msg::MapBarrierGate& m) {
    hv::interface::MapBarrierGate s;
    s.id = m.id;
    s.polygon = msg_to_struct(m.polygon);
    s.overlap_ids = m.overlap_ids;
    return s;
}


inline hv_map_msgs::msg::Map struct_to_msg(const hv::interface::Map& s) {
    hv_map_msgs::msg::Map m;
    m.header = struct_to_msg(s.header);
    m.metaheader = struct_to_msg(s.metaheader);
    m.crosswalks.reserve(s.crosswalks.size());
    for (const auto& element : s.crosswalks) {
        m.crosswalks.push_back(struct_to_msg(element));
    }
    m.junctions.reserve(s.junctions.size());
    for (const auto& element : s.junctions) {
        m.junctions.push_back(struct_to_msg(element));
    }
    m.lanes.reserve(s.lanes.size());
    for (const auto& element : s.lanes) {
        m.lanes.push_back(struct_to_msg(element));
    }
    m.stop_signs.reserve(s.stop_signs.size());
    for (const auto& element : s.stop_signs) {
        m.stop_signs.push_back(struct_to_msg(element));
    }
    m.signals.reserve(s.signals.size());
    for (const auto& element : s.signals) {
        m.signals.push_back(struct_to_msg(element));
    }
    m.yield_signs.reserve(s.yield_signs.size());
    for (const auto& element : s.yield_signs) {
        m.yield_signs.push_back(struct_to_msg(element));
    }
    m.overlaps.reserve(s.overlaps.size());
    for (const auto& element : s.overlaps) {
        m.overlaps.push_back(struct_to_msg(element));
    }
    m.clear_areas.reserve(s.clear_areas.size());
    for (const auto& element : s.clear_areas) {
        m.clear_areas.push_back(struct_to_msg(element));
    }
    m.speed_bumps.reserve(s.speed_bumps.size());
    for (const auto& element : s.speed_bumps) {
        m.speed_bumps.push_back(struct_to_msg(element));
    }
    m.roads.reserve(s.roads.size());
    for (const auto& element : s.roads) {
        m.roads.push_back(struct_to_msg(element));
    }
    m.parking_spaces.reserve(s.parking_spaces.size());
    for (const auto& element : s.parking_spaces) {
        m.parking_spaces.push_back(struct_to_msg(element));
    }
    m.pnc_junctions.reserve(s.pnc_junctions.size());
    for (const auto& element : s.pnc_junctions) {
        m.pnc_junctions.push_back(struct_to_msg(element));
    }
    m.rsus.reserve(s.rsus.size());
    for (const auto& element : s.rsus) {
        m.rsus.push_back(struct_to_msg(element));
    }
    m.areas.reserve(s.areas.size());
    for (const auto& element : s.areas) {
        m.areas.push_back(struct_to_msg(element));
    }
    m.barrier_gates.reserve(s.barrier_gates.size());
    for (const auto& element : s.barrier_gates) {
        m.barrier_gates.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::Map msg_to_struct(const hv_map_msgs::msg::Map& m) {
    hv::interface::Map s;
    s.header = msg_to_struct(m.header);
    s.metaheader = msg_to_struct(m.metaheader);
    s.crosswalks.reserve(m.crosswalks.size());
    for (const auto& element : m.crosswalks) s.crosswalks.push_back(msg_to_struct(element));
    s.junctions.reserve(m.junctions.size());
    for (const auto& element : m.junctions) s.junctions.push_back(msg_to_struct(element));
    s.lanes.reserve(m.lanes.size());
    for (const auto& element : m.lanes) s.lanes.push_back(msg_to_struct(element));
    s.stop_signs.reserve(m.stop_signs.size());
    for (const auto& element : m.stop_signs) s.stop_signs.push_back(msg_to_struct(element));
    s.signals.reserve(m.signals.size());
    for (const auto& element : m.signals) s.signals.push_back(msg_to_struct(element));
    s.yield_signs.reserve(m.yield_signs.size());
    for (const auto& element : m.yield_signs) s.yield_signs.push_back(msg_to_struct(element));
    s.overlaps.reserve(m.overlaps.size());
    for (const auto& element : m.overlaps) s.overlaps.push_back(msg_to_struct(element));
    s.clear_areas.reserve(m.clear_areas.size());
    for (const auto& element : m.clear_areas) s.clear_areas.push_back(msg_to_struct(element));
    s.speed_bumps.reserve(m.speed_bumps.size());
    for (const auto& element : m.speed_bumps) s.speed_bumps.push_back(msg_to_struct(element));
    s.roads.reserve(m.roads.size());
    for (const auto& element : m.roads) s.roads.push_back(msg_to_struct(element));
    s.parking_spaces.reserve(m.parking_spaces.size());
    for (const auto& element : m.parking_spaces) s.parking_spaces.push_back(msg_to_struct(element));
    s.pnc_junctions.reserve(m.pnc_junctions.size());
    for (const auto& element : m.pnc_junctions) s.pnc_junctions.push_back(msg_to_struct(element));
    s.rsus.reserve(m.rsus.size());
    for (const auto& element : m.rsus) s.rsus.push_back(msg_to_struct(element));
    s.areas.reserve(m.areas.size());
    for (const auto& element : m.areas) s.areas.push_back(msg_to_struct(element));
    s.barrier_gates.reserve(m.barrier_gates.size());
    for (const auto& element : m.barrier_gates) s.barrier_gates.push_back(msg_to_struct(element));
    return s;
}



} // namespace converter
} // namespace hv
