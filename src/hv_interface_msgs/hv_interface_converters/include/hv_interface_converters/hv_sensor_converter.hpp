// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_sensor_msgs/msg/recoder_camera_frame.hpp"
#include "hv_sensor_msgs/msg/recoder_lidar_frame.hpp"
#include "hv_sensor_msgs/msg/heading_measurement.hpp"
#include "hv_sensor_msgs/msg/imu_measurement.hpp"
#include "hv_sensor_msgs/msg/sensor_header.hpp"
#include "hv_sensor_msgs/msg/point_cloud.hpp"
#include "hv_sensor_msgs/msg/gnss_measurement.hpp"
#include "hv_sensor_msgs/msg/pointxyzit.hpp"

// Include interface headers
#include "hv_interface/sensor/gnss_heading.h"
#include "hv_interface/sensor/sensor_common.h"
#include "hv_interface/sensor/gnss_rtk.h"
#include "hv_interface/sensor/gnss_imu.h"
#include "hv_interface/sensor/gnss_types.h"
#include "hv_interface/sensor/lidar_recoder.h"
#include "hv_interface/sensor/lidar_pointcloud.h"
#include "hv_interface/sensor/camera_recoder.h"

// Include dependent converters
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_sensor_msgs::msg::HeadingMeasurement struct_to_msg(const hv::interface::HeadingMeasurement& s);
inline hv::interface::HeadingMeasurement msg_to_struct(const hv_sensor_msgs::msg::HeadingMeasurement& m);
inline hv_sensor_msgs::msg::SensorHeader struct_to_msg(const hv::interface::SensorHeader& s);
inline hv::interface::SensorHeader msg_to_struct(const hv_sensor_msgs::msg::SensorHeader& m);
inline hv_sensor_msgs::msg::GnssMeasurement struct_to_msg(const hv::interface::GnssMeasurement& s);
inline hv::interface::GnssMeasurement msg_to_struct(const hv_sensor_msgs::msg::GnssMeasurement& m);
inline hv_sensor_msgs::msg::ImuMeasurement struct_to_msg(const hv::interface::ImuMeasurement& s);
inline hv::interface::ImuMeasurement msg_to_struct(const hv_sensor_msgs::msg::ImuMeasurement& m);
inline hv_sensor_msgs::msg::RecoderLidarFrame struct_to_msg(const hv::interface::RecoderLidarFrame& s);
inline hv::interface::RecoderLidarFrame msg_to_struct(const hv_sensor_msgs::msg::RecoderLidarFrame& m);
inline hv_sensor_msgs::msg::Pointxyzit struct_to_msg(const hv::interface::Pointxyzit& s);
inline hv::interface::Pointxyzit msg_to_struct(const hv_sensor_msgs::msg::Pointxyzit& m);
inline hv_sensor_msgs::msg::PointCloud struct_to_msg(const hv::interface::PointCloud& s);
inline hv::interface::PointCloud msg_to_struct(const hv_sensor_msgs::msg::PointCloud& m);
inline hv_sensor_msgs::msg::RecoderCameraFrame struct_to_msg(const hv::interface::RecoderCameraFrame& s);
inline hv::interface::RecoderCameraFrame msg_to_struct(const hv_sensor_msgs::msg::RecoderCameraFrame& m);

inline hv_sensor_msgs::msg::HeadingMeasurement struct_to_msg(const hv::interface::HeadingMeasurement& s) {
    hv_sensor_msgs::msg::HeadingMeasurement m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.solution_status = static_cast<int32_t>(s.solution_status);
    m.heading_type = static_cast<int32_t>(s.heading_type);
    m.antenna_distance = s.antenna_distance;
    m.heading = s.heading;
    m.heading_stddev = s.heading_stddev;
    m.num_sat_tracked = s.num_sat_tracked;
    m.num_sat_used = s.num_sat_used;
    m.galileo_bd_mask = s.galileo_bd_mask;
    m.gps_glonass_mask = s.gps_glonass_mask;
    return m;
}


inline hv::interface::HeadingMeasurement msg_to_struct(const hv_sensor_msgs::msg::HeadingMeasurement& m) {
    hv::interface::HeadingMeasurement s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.solution_status = static_cast<hv::interface::SolutionStatus>(m.solution_status);
    s.heading_type = static_cast<hv::interface::PositionVelocityType>(m.heading_type);
    s.antenna_distance = m.antenna_distance;
    s.heading = m.heading;
    s.heading_stddev = m.heading_stddev;
    s.num_sat_tracked = m.num_sat_tracked;
    s.num_sat_used = m.num_sat_used;
    s.galileo_bd_mask = m.galileo_bd_mask;
    s.gps_glonass_mask = m.gps_glonass_mask;
    return s;
}


inline hv_sensor_msgs::msg::SensorHeader struct_to_msg(const hv::interface::SensorHeader& s) {
    hv_sensor_msgs::msg::SensorHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.sensor_id = static_cast<int32_t>(s.sensor_id);
    m.frame_sequence = s.frame_sequence;
    return m;
}


inline hv::interface::SensorHeader msg_to_struct(const hv_sensor_msgs::msg::SensorHeader& m) {
    hv::interface::SensorHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.sensor_id = static_cast<hv::interface::SensorId>(m.sensor_id);
    s.frame_sequence = m.frame_sequence;
    return s;
}


inline hv_sensor_msgs::msg::GnssMeasurement struct_to_msg(const hv::interface::GnssMeasurement& s) {
    hv_sensor_msgs::msg::GnssMeasurement m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.position_solution_status = static_cast<int32_t>(s.position_solution_status);
    m.position_type = static_cast<int32_t>(s.position_type);
    m.position = struct_to_msg(s.position);
    m.position_stddev = struct_to_msg(s.position_stddev);
    m.undulation = s.undulation;
    m.num_sat_tracked = s.num_sat_tracked;
    m.num_sat_used = s.num_sat_used;
    m.position_diff_age = s.position_diff_age;
    m.solution_age = s.solution_age;
    m.velocity_solution_status = static_cast<int32_t>(s.velocity_solution_status);
    m.velocity_type = static_cast<int32_t>(s.velocity_type);
    m.velocity = struct_to_msg(s.velocity);
    m.velocity_stddev = struct_to_msg(s.velocity_stddev);
    m.velocity_latency = s.velocity_latency;
    m.velocity_diff_age = s.velocity_diff_age;
    m.base_station_id = s.base_station_id;
    m.datum_id = s.datum_id;
    m.galileo_bd_mask = s.galileo_bd_mask;
    m.gps_glonass_mask = s.gps_glonass_mask;
    m.gdop = s.gdop;
    m.pdop = s.pdop;
    m.hdop = s.hdop;
    m.htdop = s.htdop;
    m.tdop = s.tdop;
    m.elev_mask = s.elev_mask;
    m.dops_num_sat_tracked = s.dops_num_sat_tracked;
    m.signal_to_noise_ratio = s.signal_to_noise_ratio;
    return m;
}


inline hv::interface::GnssMeasurement msg_to_struct(const hv_sensor_msgs::msg::GnssMeasurement& m) {
    hv::interface::GnssMeasurement s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.position_solution_status = static_cast<hv::interface::SolutionStatus>(m.position_solution_status);
    s.position_type = static_cast<hv::interface::PositionVelocityType>(m.position_type);
    s.position = msg_to_struct(m.position);
    s.position_stddev = msg_to_struct(m.position_stddev);
    s.undulation = m.undulation;
    s.num_sat_tracked = m.num_sat_tracked;
    s.num_sat_used = m.num_sat_used;
    s.position_diff_age = m.position_diff_age;
    s.solution_age = m.solution_age;
    s.velocity_solution_status = static_cast<hv::interface::SolutionStatus>(m.velocity_solution_status);
    s.velocity_type = static_cast<hv::interface::PositionVelocityType>(m.velocity_type);
    s.velocity = msg_to_struct(m.velocity);
    s.velocity_stddev = msg_to_struct(m.velocity_stddev);
    s.velocity_latency = m.velocity_latency;
    s.velocity_diff_age = m.velocity_diff_age;
    s.base_station_id = m.base_station_id;
    s.datum_id = m.datum_id;
    s.galileo_bd_mask = m.galileo_bd_mask;
    s.gps_glonass_mask = m.gps_glonass_mask;
    s.gdop = m.gdop;
    s.pdop = m.pdop;
    s.hdop = m.hdop;
    s.htdop = m.htdop;
    s.tdop = m.tdop;
    s.elev_mask = m.elev_mask;
    s.dops_num_sat_tracked = m.dops_num_sat_tracked;
    s.signal_to_noise_ratio = m.signal_to_noise_ratio;
    return s;
}


inline hv_sensor_msgs::msg::ImuMeasurement struct_to_msg(const hv::interface::ImuMeasurement& s) {
    hv_sensor_msgs::msg::ImuMeasurement m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.acceleration_vcs = struct_to_msg(s.acceleration_vcs);
    m.angular_velocity_vcs = struct_to_msg(s.angular_velocity_vcs);
    m.accelerometer_temperature = s.accelerometer_temperature;
    m.gyroscope_temperature = s.gyroscope_temperature;
    return m;
}


inline hv::interface::ImuMeasurement msg_to_struct(const hv_sensor_msgs::msg::ImuMeasurement& m) {
    hv::interface::ImuMeasurement s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.acceleration_vcs = msg_to_struct(m.acceleration_vcs);
    s.angular_velocity_vcs = msg_to_struct(m.angular_velocity_vcs);
    s.accelerometer_temperature = m.accelerometer_temperature;
    s.gyroscope_temperature = m.gyroscope_temperature;
    return s;
}


inline hv_sensor_msgs::msg::RecoderLidarFrame struct_to_msg(const hv::interface::RecoderLidarFrame& s) {
    hv_sensor_msgs::msg::RecoderLidarFrame m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.point_cloud_format = static_cast<int32_t>(s.point_cloud_format);
    m.height = s.height;
    m.width = s.width;
    m.point_step = s.point_step;
    m.row_step = s.row_step;
    m.frame_payload = s.frame_payload;
    return m;
}


inline hv::interface::RecoderLidarFrame msg_to_struct(const hv_sensor_msgs::msg::RecoderLidarFrame& m) {
    hv::interface::RecoderLidarFrame s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.point_cloud_format = static_cast<hv::interface::PointCloudFormat>(m.point_cloud_format);
    s.height = m.height;
    s.width = m.width;
    s.point_step = m.point_step;
    s.row_step = m.row_step;
    s.frame_payload = m.frame_payload;
    return s;
}


inline hv_sensor_msgs::msg::Pointxyzit struct_to_msg(const hv::interface::Pointxyzit& s) {
    hv_sensor_msgs::msg::Pointxyzit m;
    m.x = s.x;
    m.y = s.y;
    m.z = s.z;
    m.intensity = s.intensity;
    m.time_offset = s.time_offset;
    return m;
}


inline hv::interface::Pointxyzit msg_to_struct(const hv_sensor_msgs::msg::Pointxyzit& m) {
    hv::interface::Pointxyzit s;
    s.x = m.x;
    s.y = m.y;
    s.z = m.z;
    s.intensity = m.intensity;
    s.time_offset = m.time_offset;
    return s;
}


inline hv_sensor_msgs::msg::PointCloud struct_to_msg(const hv::interface::PointCloud& s) {
    hv_sensor_msgs::msg::PointCloud m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.last_frame_timestamp = s.last_frame_timestamp;
    m.points.reserve(s.points.size());
    for (const auto& element : s.points) {
        m.points.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::PointCloud msg_to_struct(const hv_sensor_msgs::msg::PointCloud& m) {
    hv::interface::PointCloud s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.last_frame_timestamp = m.last_frame_timestamp;
    s.points.reserve(m.points.size());
    for (const auto& element : m.points) s.points.push_back(msg_to_struct(element));
    return s;
}


inline hv_sensor_msgs::msg::RecoderCameraFrame struct_to_msg(const hv::interface::RecoderCameraFrame& s) {
    hv_sensor_msgs::msg::RecoderCameraFrame m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.encoding_type = static_cast<int32_t>(s.encoding_type);
    m.exposure_start_time = s.exposure_start_time;
    m.exposure_end_time = s.exposure_end_time;
    m.height = s.height;
    m.width = s.width;
    m.frame_payload_size = s.frame_payload_size;
    m.frame_payload = s.frame_payload;
    return m;
}


inline hv::interface::RecoderCameraFrame msg_to_struct(const hv_sensor_msgs::msg::RecoderCameraFrame& m) {
    hv::interface::RecoderCameraFrame s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.encoding_type = static_cast<hv::interface::EncodingType>(m.encoding_type);
    s.exposure_start_time = m.exposure_start_time;
    s.exposure_end_time = m.exposure_end_time;
    s.height = m.height;
    s.width = m.width;
    s.frame_payload_size = m.frame_payload_size;
    s.frame_payload = m.frame_payload;
    return s;
}



} // namespace converter
} // namespace hv
