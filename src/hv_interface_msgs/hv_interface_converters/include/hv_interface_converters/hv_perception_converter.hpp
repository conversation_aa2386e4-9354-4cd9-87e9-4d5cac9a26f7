// Generated automatically by generate_converters.py
#pragma once

#include <memory>
#include <string>
#include <vector>
#include <algorithm>

// Include ROS message headers
#include "hv_perception_msgs/msg/keep_clear_area.hpp"
#include "hv_perception_msgs/msg/bicycle_light_status.hpp"
#include "hv_perception_msgs/msg/perception_header.hpp"
#include "hv_perception_msgs/msg/traffic_cone_flow.hpp"
#include "hv_perception_msgs/msg/parking_space.hpp"
#include "hv_perception_msgs/msg/toll_lane.hpp"
#include "hv_perception_msgs/msg/lidar_corner_point.hpp"
#include "hv_perception_msgs/msg/other_land_mark.hpp"
#include "hv_perception_msgs/msg/bicycle_content.hpp"
#include "hv_perception_msgs/msg/traffic_sign.hpp"
#include "hv_perception_msgs/msg/vehicle_door_open_status.hpp"
#include "hv_perception_msgs/msg/static_obstacle_content.hpp"
#include "hv_perception_msgs/msg/speed_bump.hpp"
#include "hv_perception_msgs/msg/traffic_light.hpp"
#include "hv_perception_msgs/msg/lane_arrow.hpp"
#include "hv_perception_msgs/msg/internal_percep_obstacle_list.hpp"
#include "hv_perception_msgs/msg/animal_content.hpp"
#include "hv_perception_msgs/msg/obstacle_list.hpp"
#include "hv_perception_msgs/msg/waiting_area_lane.hpp"
#include "hv_perception_msgs/msg/stop_line.hpp"
#include "hv_perception_msgs/msg/traffic_light_component.hpp"
#include "hv_perception_msgs/msg/toll_station.hpp"
#include "hv_perception_msgs/msg/vehicle_light_status.hpp"
#include "hv_perception_msgs/msg/junction.hpp"
#include "hv_perception_msgs/msg/pedestrian_content.hpp"
#include "hv_perception_msgs/msg/traffic_cone_content.hpp"
#include "hv_perception_msgs/msg/lidar_occupancy_grid.hpp"
#include "hv_perception_msgs/msg/obstacle.hpp"
#include "hv_perception_msgs/msg/lane_line.hpp"
#include "hv_perception_msgs/msg/vehicle_content.hpp"
#include "hv_perception_msgs/msg/lidar_occupancy_grid_list.hpp"
#include "hv_perception_msgs/msg/curb.hpp"
#include "hv_perception_msgs/msg/unknown_content.hpp"
#include "hv_perception_msgs/msg/lane_line_point.hpp"
#include "hv_perception_msgs/msg/perception_object3_d.hpp"
#include "hv_perception_msgs/msg/crosswalk.hpp"
#include "hv_perception_msgs/msg/perception_box3_d.hpp"
#include "hv_perception_msgs/msg/lane.hpp"
#include "hv_perception_msgs/msg/junction_connection.hpp"
#include "hv_perception_msgs/msg/vehicle_engine_status.hpp"
#include "hv_perception_msgs/msg/camera_scene_list.hpp"
#include "hv_perception_msgs/msg/type_history.hpp"

// Include interface headers
#include "hv_interface/perception/camera_scene.h"
#include "hv_interface/perception/perception_obstacle.h"
#include "hv_interface/perception/perception_common.h"
#include "hv_interface/perception/fusion_obstacle.h"
#include "hv_interface/perception/lidar_oppcupancy.h"

// Include dependent converters
#include "hv_interface_converters/hv_sensor_converter.hpp"
#include "hv_interface_converters/hv_common_converter.hpp"

namespace hv {
namespace converter {

inline hv_perception_msgs::msg::TrafficSign struct_to_msg(const hv::interface::TrafficSign& s);
inline hv::interface::TrafficSign msg_to_struct(const hv_perception_msgs::msg::TrafficSign& m);
inline hv_perception_msgs::msg::LaneLinePoint struct_to_msg(const hv::interface::LaneLinePoint& s);
inline hv::interface::LaneLinePoint msg_to_struct(const hv_perception_msgs::msg::LaneLinePoint& m);
inline hv_perception_msgs::msg::LaneLine struct_to_msg(const hv::interface::LaneLine& s);
inline hv::interface::LaneLine msg_to_struct(const hv_perception_msgs::msg::LaneLine& m);
inline hv_perception_msgs::msg::LaneArrow struct_to_msg(const hv::interface::LaneArrow& s);
inline hv::interface::LaneArrow msg_to_struct(const hv_perception_msgs::msg::LaneArrow& m);
inline hv_perception_msgs::msg::OtherLandMark struct_to_msg(const hv::interface::OtherLandMark& s);
inline hv::interface::OtherLandMark msg_to_struct(const hv_perception_msgs::msg::OtherLandMark& m);
inline hv_perception_msgs::msg::Lane struct_to_msg(const hv::interface::Lane& s);
inline hv::interface::Lane msg_to_struct(const hv_perception_msgs::msg::Lane& m);
inline hv_perception_msgs::msg::TrafficConeFlow struct_to_msg(const hv::interface::TrafficConeFlow& s);
inline hv::interface::TrafficConeFlow msg_to_struct(const hv_perception_msgs::msg::TrafficConeFlow& m);
inline hv_perception_msgs::msg::Curb struct_to_msg(const hv::interface::Curb& s);
inline hv::interface::Curb msg_to_struct(const hv_perception_msgs::msg::Curb& m);
inline hv_perception_msgs::msg::Crosswalk struct_to_msg(const hv::interface::Crosswalk& s);
inline hv::interface::Crosswalk msg_to_struct(const hv_perception_msgs::msg::Crosswalk& m);
inline hv_perception_msgs::msg::ParkingSpace struct_to_msg(const hv::interface::ParkingSpace& s);
inline hv::interface::ParkingSpace msg_to_struct(const hv_perception_msgs::msg::ParkingSpace& m);
inline hv_perception_msgs::msg::TollLane struct_to_msg(const hv::interface::TollLane& s);
inline hv::interface::TollLane msg_to_struct(const hv_perception_msgs::msg::TollLane& m);
inline hv_perception_msgs::msg::TollStation struct_to_msg(const hv::interface::TollStation& s);
inline hv::interface::TollStation msg_to_struct(const hv_perception_msgs::msg::TollStation& m);
inline hv_perception_msgs::msg::TrafficLightComponent struct_to_msg(const hv::interface::TrafficLightComponent& s);
inline hv::interface::TrafficLightComponent msg_to_struct(const hv_perception_msgs::msg::TrafficLightComponent& m);
inline hv_perception_msgs::msg::TrafficLight struct_to_msg(const hv::interface::TrafficLight& s);
inline hv::interface::TrafficLight msg_to_struct(const hv_perception_msgs::msg::TrafficLight& m);
inline hv_perception_msgs::msg::SpeedBump struct_to_msg(const hv::interface::SpeedBump& s);
inline hv::interface::SpeedBump msg_to_struct(const hv_perception_msgs::msg::SpeedBump& m);
inline hv_perception_msgs::msg::KeepClearArea struct_to_msg(const hv::interface::KeepClearArea& s);
inline hv::interface::KeepClearArea msg_to_struct(const hv_perception_msgs::msg::KeepClearArea& m);
inline hv_perception_msgs::msg::StopLine struct_to_msg(const hv::interface::StopLine& s);
inline hv::interface::StopLine msg_to_struct(const hv_perception_msgs::msg::StopLine& m);
inline hv_perception_msgs::msg::WaitingAreaLane struct_to_msg(const hv::interface::WaitingAreaLane& s);
inline hv::interface::WaitingAreaLane msg_to_struct(const hv_perception_msgs::msg::WaitingAreaLane& m);
inline hv_perception_msgs::msg::JunctionConnection struct_to_msg(const hv::interface::JunctionConnection& s);
inline hv::interface::JunctionConnection msg_to_struct(const hv_perception_msgs::msg::JunctionConnection& m);
inline hv_perception_msgs::msg::Junction struct_to_msg(const hv::interface::Junction& s);
inline hv::interface::Junction msg_to_struct(const hv_perception_msgs::msg::Junction& m);
inline hv_perception_msgs::msg::CameraSceneList struct_to_msg(const hv::interface::CameraSceneList& s);
inline hv::interface::CameraSceneList msg_to_struct(const hv_perception_msgs::msg::CameraSceneList& m);
inline hv_perception_msgs::msg::PerceptionBox3D struct_to_msg(const hv::interface::PerceptionBox3D& s);
inline hv::interface::PerceptionBox3D msg_to_struct(const hv_perception_msgs::msg::PerceptionBox3D& m);
inline hv_perception_msgs::msg::PerceptionObject3D struct_to_msg(const hv::interface::PerceptionObject3D& s);
inline hv::interface::PerceptionObject3D msg_to_struct(const hv_perception_msgs::msg::PerceptionObject3D& m);
inline hv_perception_msgs::msg::InternalPercepObstacleList struct_to_msg(const hv::interface::InternalPercepObstacleList& s);
inline hv::interface::InternalPercepObstacleList msg_to_struct(const hv_perception_msgs::msg::InternalPercepObstacleList& m);
inline hv_perception_msgs::msg::PerceptionHeader struct_to_msg(const hv::interface::PerceptionHeader& s);
inline hv::interface::PerceptionHeader msg_to_struct(const hv_perception_msgs::msg::PerceptionHeader& m);
inline hv_perception_msgs::msg::VehicleLightStatus struct_to_msg(const hv::interface::VehicleLightStatus& s);
inline hv::interface::VehicleLightStatus msg_to_struct(const hv_perception_msgs::msg::VehicleLightStatus& m);
inline hv_perception_msgs::msg::LidarCornerPoint struct_to_msg(const hv::interface::LidarCornerPoint& s);
inline hv::interface::LidarCornerPoint msg_to_struct(const hv_perception_msgs::msg::LidarCornerPoint& m);
inline hv_perception_msgs::msg::VehicleDoorOpenStatus struct_to_msg(const hv::interface::VehicleDoorOpenStatus& s);
inline hv::interface::VehicleDoorOpenStatus msg_to_struct(const hv_perception_msgs::msg::VehicleDoorOpenStatus& m);
inline hv_perception_msgs::msg::VehicleEngineStatus struct_to_msg(const hv::interface::VehicleEngineStatus& s);
inline hv::interface::VehicleEngineStatus msg_to_struct(const hv_perception_msgs::msg::VehicleEngineStatus& m);
inline hv_perception_msgs::msg::VehicleContent struct_to_msg(const hv::interface::VehicleContent& s);
inline hv::interface::VehicleContent msg_to_struct(const hv_perception_msgs::msg::VehicleContent& m);
inline hv_perception_msgs::msg::PedestrianContent struct_to_msg(const hv::interface::PedestrianContent& s);
inline hv::interface::PedestrianContent msg_to_struct(const hv_perception_msgs::msg::PedestrianContent& m);
inline hv_perception_msgs::msg::BicycleLightStatus struct_to_msg(const hv::interface::BicycleLightStatus& s);
inline hv::interface::BicycleLightStatus msg_to_struct(const hv_perception_msgs::msg::BicycleLightStatus& m);
inline hv_perception_msgs::msg::BicycleContent struct_to_msg(const hv::interface::BicycleContent& s);
inline hv::interface::BicycleContent msg_to_struct(const hv_perception_msgs::msg::BicycleContent& m);
inline hv_perception_msgs::msg::AnimalContent struct_to_msg(const hv::interface::AnimalContent& s);
inline hv::interface::AnimalContent msg_to_struct(const hv_perception_msgs::msg::AnimalContent& m);
inline hv_perception_msgs::msg::UnknownContent struct_to_msg(const hv::interface::UnknownContent& s);
inline hv::interface::UnknownContent msg_to_struct(const hv_perception_msgs::msg::UnknownContent& m);
inline hv_perception_msgs::msg::StaticObstacleContent struct_to_msg(const hv::interface::StaticObstacleContent& s);
inline hv::interface::StaticObstacleContent msg_to_struct(const hv_perception_msgs::msg::StaticObstacleContent& m);
inline hv_perception_msgs::msg::TrafficConeContent struct_to_msg(const hv::interface::TrafficConeContent& s);
inline hv::interface::TrafficConeContent msg_to_struct(const hv_perception_msgs::msg::TrafficConeContent& m);
inline hv_perception_msgs::msg::TypeHistory struct_to_msg(const hv::interface::TypeHistory& s);
inline hv::interface::TypeHistory msg_to_struct(const hv_perception_msgs::msg::TypeHistory& m);
inline hv_perception_msgs::msg::Obstacle struct_to_msg(const hv::interface::Obstacle& s);
inline hv::interface::Obstacle msg_to_struct(const hv_perception_msgs::msg::Obstacle& m);
inline hv_perception_msgs::msg::ObstacleList struct_to_msg(const hv::interface::ObstacleList& s);
inline hv::interface::ObstacleList msg_to_struct(const hv_perception_msgs::msg::ObstacleList& m);
inline hv_perception_msgs::msg::LidarOccupancyGrid struct_to_msg(const hv::interface::LidarOccupancyGrid& s);
inline hv::interface::LidarOccupancyGrid msg_to_struct(const hv_perception_msgs::msg::LidarOccupancyGrid& m);
inline hv_perception_msgs::msg::LidarOccupancyGridList struct_to_msg(const hv::interface::LidarOccupancyGridList& s);
inline hv::interface::LidarOccupancyGridList msg_to_struct(const hv_perception_msgs::msg::LidarOccupancyGridList& m);

inline hv_perception_msgs::msg::TrafficSign struct_to_msg(const hv::interface::TrafficSign& s) {
    hv_perception_msgs::msg::TrafficSign m;
    m.detection_timestamp = s.detection_timestamp;
    m.sign_id = s.sign_id;
    m.sign_type = static_cast<int32_t>(s.sign_type);
    m.sign_status = static_cast<int32_t>(s.sign_status);
    m.confidence = s.confidence;
    m.position = struct_to_msg(s.position);
    m.heading = s.heading;
    m.width = s.width;
    m.height = s.height;
    m.sign_text = s.sign_text;
    m.sign_value = s.sign_value;
    m.is_illuminated = s.is_illuminated;
    m.distance = s.distance;
    return m;
}


inline hv::interface::TrafficSign msg_to_struct(const hv_perception_msgs::msg::TrafficSign& m) {
    hv::interface::TrafficSign s;
    s.detection_timestamp = m.detection_timestamp;
    s.sign_id = m.sign_id;
    s.sign_type = static_cast<hv::interface::TrafficSignType>(m.sign_type);
    s.sign_status = static_cast<hv::interface::TrafficSignStatus>(m.sign_status);
    s.confidence = m.confidence;
    s.position = msg_to_struct(m.position);
    s.heading = m.heading;
    s.width = m.width;
    s.height = m.height;
    s.sign_text = m.sign_text;
    s.sign_value = m.sign_value;
    s.is_illuminated = m.is_illuminated;
    s.distance = m.distance;
    return s;
}


inline hv_perception_msgs::msg::LaneLinePoint struct_to_msg(const hv::interface::LaneLinePoint& s) {
    hv_perception_msgs::msg::LaneLinePoint m;
    m.position = struct_to_msg(s.position);
    m.curvature = s.curvature;
    m.heading = s.heading;
    m.quality = s.quality;
    return m;
}


inline hv::interface::LaneLinePoint msg_to_struct(const hv_perception_msgs::msg::LaneLinePoint& m) {
    hv::interface::LaneLinePoint s;
    s.position = msg_to_struct(m.position);
    s.curvature = m.curvature;
    s.heading = m.heading;
    s.quality = m.quality;
    return s;
}


inline hv_perception_msgs::msg::LaneLine struct_to_msg(const hv::interface::LaneLine& s) {
    hv_perception_msgs::msg::LaneLine m;
    m.detection_timestamp = s.detection_timestamp;
    m.line_id = s.line_id;
    m.line_type = static_cast<int32_t>(s.line_type);
    m.line_color = static_cast<int32_t>(s.line_color);
    m.confidence = s.confidence;
    m.points.reserve(s.points.size());
    for (const auto& element : s.points) {
        m.points.push_back(struct_to_msg(element));
    }
    m.width = s.width;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::LaneLine msg_to_struct(const hv_perception_msgs::msg::LaneLine& m) {
    hv::interface::LaneLine s;
    s.detection_timestamp = m.detection_timestamp;
    s.line_id = m.line_id;
    s.line_type = static_cast<hv::interface::LaneLineType>(m.line_type);
    s.line_color = static_cast<hv::interface::LaneLineColor>(m.line_color);
    s.confidence = m.confidence;
    s.points.reserve(m.points.size());
    for (const auto& element : m.points) s.points.push_back(msg_to_struct(element));
    s.width = m.width;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::LaneArrow struct_to_msg(const hv::interface::LaneArrow& s) {
    hv_perception_msgs::msg::LaneArrow m;
    m.detection_timestamp = s.detection_timestamp;
    m.arrow_id = s.arrow_id;
    m.arrow_type = static_cast<int32_t>(s.arrow_type);
    m.confidence = s.confidence;
    m.position = struct_to_msg(s.position);
    m.heading = s.heading;
    m.width = s.width;
    m.length = s.length;
    m.is_visible = s.is_visible;
    return m;
}


inline hv::interface::LaneArrow msg_to_struct(const hv_perception_msgs::msg::LaneArrow& m) {
    hv::interface::LaneArrow s;
    s.detection_timestamp = m.detection_timestamp;
    s.arrow_id = m.arrow_id;
    s.arrow_type = static_cast<hv::interface::LaneArrowType>(m.arrow_type);
    s.confidence = m.confidence;
    s.position = msg_to_struct(m.position);
    s.heading = m.heading;
    s.width = m.width;
    s.length = m.length;
    s.is_visible = m.is_visible;
    return s;
}


inline hv_perception_msgs::msg::OtherLandMark struct_to_msg(const hv::interface::OtherLandMark& s) {
    hv_perception_msgs::msg::OtherLandMark m;
    m.detection_timestamp = s.detection_timestamp;
    m.land_mark_id = s.land_mark_id;
    m.land_mark_type = static_cast<int32_t>(s.land_mark_type);
    m.position = struct_to_msg(s.position);
    m.heading = s.heading;
    m.width = s.width;
    m.length = s.length;
    m.speed_limit_lower_bound = s.speed_limit_lower_bound;
    m.speed_limit_upper_bound = s.speed_limit_upper_bound;
    m.confidence = s.confidence;
    return m;
}


inline hv::interface::OtherLandMark msg_to_struct(const hv_perception_msgs::msg::OtherLandMark& m) {
    hv::interface::OtherLandMark s;
    s.detection_timestamp = m.detection_timestamp;
    s.land_mark_id = m.land_mark_id;
    s.land_mark_type = static_cast<hv::interface::OtherLandMarkType>(m.land_mark_type);
    s.position = msg_to_struct(m.position);
    s.heading = m.heading;
    s.width = m.width;
    s.length = m.length;
    s.speed_limit_lower_bound = m.speed_limit_lower_bound;
    s.speed_limit_upper_bound = m.speed_limit_upper_bound;
    s.confidence = m.confidence;
    return s;
}


inline hv_perception_msgs::msg::Lane struct_to_msg(const hv::interface::Lane& s) {
    hv_perception_msgs::msg::Lane m;
    m.detection_timestamp = s.detection_timestamp;
    m.lane_id = s.lane_id;
    m.lane_type = s.lane_type;
    m.lane_direction = s.lane_direction;
    m.confidence = s.confidence;
    m.center_line = struct_to_msg(s.center_line);
    m.lane_width = s.lane_width;
    m.left_line_id = s.left_line_id;
    m.right_line_id = s.right_line_id;
    m.left_curb_id = s.left_curb_id;
    m.right_curb_id = s.right_curb_id;
    m.lane_arrows.reserve(s.lane_arrows.size());
    for (const auto& element : s.lane_arrows) {
        m.lane_arrows.push_back(struct_to_msg(element));
    }
    m.other_land_marks.reserve(s.other_land_marks.size());
    for (const auto& element : s.other_land_marks) {
        m.other_land_marks.push_back(struct_to_msg(element));
    }
    m.speed_limit = s.speed_limit;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::Lane msg_to_struct(const hv_perception_msgs::msg::Lane& m) {
    hv::interface::Lane s;
    s.detection_timestamp = m.detection_timestamp;
    s.lane_id = m.lane_id;
    s.lane_type = m.lane_type;
    s.lane_direction = m.lane_direction;
    s.confidence = m.confidence;
    s.center_line = msg_to_struct(m.center_line);
    s.lane_width = m.lane_width;
    s.left_line_id = m.left_line_id;
    s.right_line_id = m.right_line_id;
    s.left_curb_id = m.left_curb_id;
    s.right_curb_id = m.right_curb_id;
    s.lane_arrows.reserve(m.lane_arrows.size());
    for (const auto& element : m.lane_arrows) s.lane_arrows.push_back(msg_to_struct(element));
    s.other_land_marks.reserve(m.other_land_marks.size());
    for (const auto& element : m.other_land_marks) s.other_land_marks.push_back(msg_to_struct(element));
    s.speed_limit = m.speed_limit;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::TrafficConeFlow struct_to_msg(const hv::interface::TrafficConeFlow& s) {
    hv_perception_msgs::msg::TrafficConeFlow m;
    m.setup_timestamp = s.setup_timestamp;
    m.detection_timestamp = s.detection_timestamp;
    m.flow_id = s.flow_id;
    m.flow_type = static_cast<int32_t>(s.flow_type);
    m.confidence = s.confidence;
    m.flow_boundary = struct_to_msg(s.flow_boundary);
    m.cone_count = s.cone_count;
    return m;
}


inline hv::interface::TrafficConeFlow msg_to_struct(const hv_perception_msgs::msg::TrafficConeFlow& m) {
    hv::interface::TrafficConeFlow s;
    s.setup_timestamp = m.setup_timestamp;
    s.detection_timestamp = m.detection_timestamp;
    s.flow_id = m.flow_id;
    s.flow_type = static_cast<hv::interface::TrafficConeFlowType>(m.flow_type);
    s.confidence = m.confidence;
    s.flow_boundary = msg_to_struct(m.flow_boundary);
    s.cone_count = m.cone_count;
    return s;
}


inline hv_perception_msgs::msg::Curb struct_to_msg(const hv::interface::Curb& s) {
    hv_perception_msgs::msg::Curb m;
    m.detection_timestamp = s.detection_timestamp;
    m.curb_id = s.curb_id;
    m.confidence = s.confidence;
    m.points = struct_to_msg(s.points);
    m.height = s.height;
    m.is_passable = s.is_passable;
    return m;
}


inline hv::interface::Curb msg_to_struct(const hv_perception_msgs::msg::Curb& m) {
    hv::interface::Curb s;
    s.detection_timestamp = m.detection_timestamp;
    s.curb_id = m.curb_id;
    s.confidence = m.confidence;
    s.points = msg_to_struct(m.points);
    s.height = m.height;
    s.is_passable = m.is_passable;
    return s;
}


inline hv_perception_msgs::msg::Crosswalk struct_to_msg(const hv::interface::Crosswalk& s) {
    hv_perception_msgs::msg::Crosswalk m;
    m.detection_timestamp = s.detection_timestamp;
    m.crosswalk_id = s.crosswalk_id;
    m.confidence = s.confidence;
    m.corners.reserve(s.corners.size());
    for (const auto& element : s.corners) {
        m.corners.push_back(struct_to_msg(element));
    }
    m.width = s.width;
    m.length = s.length;
    m.distance = s.distance;
    return m;
}


inline hv::interface::Crosswalk msg_to_struct(const hv_perception_msgs::msg::Crosswalk& m) {
    hv::interface::Crosswalk s;
    s.detection_timestamp = m.detection_timestamp;
    s.crosswalk_id = m.crosswalk_id;
    s.confidence = m.confidence;
    s.corners.reserve(m.corners.size());
    for (const auto& element : m.corners) s.corners.push_back(msg_to_struct(element));
    s.width = m.width;
    s.length = m.length;
    s.distance = m.distance;
    return s;
}


inline hv_perception_msgs::msg::ParkingSpace struct_to_msg(const hv::interface::ParkingSpace& s) {
    hv_perception_msgs::msg::ParkingSpace m;
    m.detection_timestamp = s.detection_timestamp;
    m.parking_id = s.parking_id;
    m.confidence = s.confidence;
    m.corners.reserve(s.corners.size());
    for (const auto& element : s.corners) {
        m.corners.push_back(struct_to_msg(element));
    }
    m.width = s.width;
    m.length = s.length;
    m.is_occupied = s.is_occupied;
    m.is_available = s.is_available;
    m.parking_type = s.parking_type;
    return m;
}


inline hv::interface::ParkingSpace msg_to_struct(const hv_perception_msgs::msg::ParkingSpace& m) {
    hv::interface::ParkingSpace s;
    s.detection_timestamp = m.detection_timestamp;
    s.parking_id = m.parking_id;
    s.confidence = m.confidence;
    s.corners.reserve(m.corners.size());
    for (const auto& element : m.corners) s.corners.push_back(msg_to_struct(element));
    s.width = m.width;
    s.length = m.length;
    s.is_occupied = m.is_occupied;
    s.is_available = m.is_available;
    s.parking_type = m.parking_type;
    return s;
}


inline hv_perception_msgs::msg::TollLane struct_to_msg(const hv::interface::TollLane& s) {
    hv_perception_msgs::msg::TollLane m;
    m.detection_timestamp = s.detection_timestamp;
    m.toll_lane_id = s.toll_lane_id;
    m.lane_type = static_cast<int32_t>(s.lane_type);
    m.lane_state = static_cast<int32_t>(s.lane_state);
    m.confidence = s.confidence;
    m.left_laneline_points.reserve(s.left_laneline_points.size());
    for (const auto& element : s.left_laneline_points) {
        m.left_laneline_points.push_back(struct_to_msg(element));
    }
    m.right_laneline_points.reserve(s.right_laneline_points.size());
    for (const auto& element : s.right_laneline_points) {
        m.right_laneline_points.push_back(struct_to_msg(element));
    }
    m.left_curb_points.reserve(s.left_curb_points.size());
    for (const auto& element : s.left_curb_points) {
        m.left_curb_points.push_back(struct_to_msg(element));
    }
    m.right_curb_points.reserve(s.right_curb_points.size());
    for (const auto& element : s.right_curb_points) {
        m.right_curb_points.push_back(struct_to_msg(element));
    }
    m.lane_width = s.lane_width;
    m.lane_length = s.lane_length;
    m.has_barrier = s.has_barrier;
    m.barrier_is_opened = s.barrier_is_opened;
    m.distance_to_barrier = s.distance_to_barrier;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::TollLane msg_to_struct(const hv_perception_msgs::msg::TollLane& m) {
    hv::interface::TollLane s;
    s.detection_timestamp = m.detection_timestamp;
    s.toll_lane_id = m.toll_lane_id;
    s.lane_type = static_cast<hv::interface::TollLaneType>(m.lane_type);
    s.lane_state = static_cast<hv::interface::TollLaneState>(m.lane_state);
    s.confidence = m.confidence;
    s.left_laneline_points.reserve(m.left_laneline_points.size());
    for (const auto& element : m.left_laneline_points) s.left_laneline_points.push_back(msg_to_struct(element));
    s.right_laneline_points.reserve(m.right_laneline_points.size());
    for (const auto& element : m.right_laneline_points) s.right_laneline_points.push_back(msg_to_struct(element));
    s.left_curb_points.reserve(m.left_curb_points.size());
    for (const auto& element : m.left_curb_points) s.left_curb_points.push_back(msg_to_struct(element));
    s.right_curb_points.reserve(m.right_curb_points.size());
    for (const auto& element : m.right_curb_points) s.right_curb_points.push_back(msg_to_struct(element));
    s.lane_width = m.lane_width;
    s.lane_length = m.lane_length;
    s.has_barrier = m.has_barrier;
    s.barrier_is_opened = m.barrier_is_opened;
    s.distance_to_barrier = m.distance_to_barrier;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::TollStation struct_to_msg(const hv::interface::TollStation& s) {
    hv_perception_msgs::msg::TollStation m;
    m.detection_timestamp = s.detection_timestamp;
    m.toll_station_id = s.toll_station_id;
    m.confidence = s.confidence;
    m.corners.reserve(s.corners.size());
    for (const auto& element : s.corners) {
        m.corners.push_back(struct_to_msg(element));
    }
    m.station_width = s.station_width;
    m.station_length = s.station_length;
    m.station_heading = s.station_heading;
    m.toll_lanes.reserve(s.toll_lanes.size());
    for (const auto& element : s.toll_lanes) {
        m.toll_lanes.push_back(struct_to_msg(element));
    }
    m.total_lanes = s.total_lanes;
    m.open_lanes = s.open_lanes;
    m.distance = s.distance;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::TollStation msg_to_struct(const hv_perception_msgs::msg::TollStation& m) {
    hv::interface::TollStation s;
    s.detection_timestamp = m.detection_timestamp;
    s.toll_station_id = m.toll_station_id;
    s.confidence = m.confidence;
    s.corners.reserve(m.corners.size());
    for (const auto& element : m.corners) s.corners.push_back(msg_to_struct(element));
    s.station_width = m.station_width;
    s.station_length = m.station_length;
    s.station_heading = m.station_heading;
    s.toll_lanes.reserve(m.toll_lanes.size());
    for (const auto& element : m.toll_lanes) s.toll_lanes.push_back(msg_to_struct(element));
    s.total_lanes = m.total_lanes;
    s.open_lanes = m.open_lanes;
    s.distance = m.distance;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::TrafficLightComponent struct_to_msg(const hv::interface::TrafficLightComponent& s) {
    hv_perception_msgs::msg::TrafficLightComponent m;
    m.detection_timestamp = s.detection_timestamp;
    m.state_change_timestamp = s.state_change_timestamp;
    m.component_id = s.component_id;
    m.component_type = static_cast<int32_t>(s.component_type);
    m.current_state = static_cast<int32_t>(s.current_state);
    m.remaining_time = s.remaining_time;
    m.is_blinking = s.is_blinking;
    return m;
}


inline hv::interface::TrafficLightComponent msg_to_struct(const hv_perception_msgs::msg::TrafficLightComponent& m) {
    hv::interface::TrafficLightComponent s;
    s.detection_timestamp = m.detection_timestamp;
    s.state_change_timestamp = m.state_change_timestamp;
    s.component_id = m.component_id;
    s.component_type = static_cast<hv::interface::TrafficLightComponentType>(m.component_type);
    s.current_state = static_cast<hv::interface::TrafficLightState>(m.current_state);
    s.remaining_time = m.remaining_time;
    s.is_blinking = m.is_blinking;
    return s;
}


inline hv_perception_msgs::msg::TrafficLight struct_to_msg(const hv::interface::TrafficLight& s) {
    hv_perception_msgs::msg::TrafficLight m;
    m.detection_timestamp = s.detection_timestamp;
    m.light_id = s.light_id;
    m.traffic_light_components.reserve(s.traffic_light_components.size());
    for (const auto& element : s.traffic_light_components) {
        m.traffic_light_components.push_back(struct_to_msg(element));
    }
    m.distance = s.distance;
    m.is_working = s.is_working;
    return m;
}


inline hv::interface::TrafficLight msg_to_struct(const hv_perception_msgs::msg::TrafficLight& m) {
    hv::interface::TrafficLight s;
    s.detection_timestamp = m.detection_timestamp;
    s.light_id = m.light_id;
    s.traffic_light_components.reserve(m.traffic_light_components.size());
    for (const auto& element : m.traffic_light_components) s.traffic_light_components.push_back(msg_to_struct(element));
    s.distance = m.distance;
    s.is_working = m.is_working;
    return s;
}


inline hv_perception_msgs::msg::SpeedBump struct_to_msg(const hv::interface::SpeedBump& s) {
    hv_perception_msgs::msg::SpeedBump m;
    m.detection_timestamp = s.detection_timestamp;
    m.speed_bump_id = s.speed_bump_id;
    m.speed_bump_type = static_cast<int32_t>(s.speed_bump_type);
    m.confidence = s.confidence;
    m.points = struct_to_msg(s.points);
    m.width = s.width;
    m.height = s.height;
    m.length = s.length;
    m.distance = s.distance;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::SpeedBump msg_to_struct(const hv_perception_msgs::msg::SpeedBump& m) {
    hv::interface::SpeedBump s;
    s.detection_timestamp = m.detection_timestamp;
    s.speed_bump_id = m.speed_bump_id;
    s.speed_bump_type = static_cast<hv::interface::SpeedBumpType>(m.speed_bump_type);
    s.confidence = m.confidence;
    s.points = msg_to_struct(m.points);
    s.width = m.width;
    s.height = m.height;
    s.length = m.length;
    s.distance = m.distance;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::KeepClearArea struct_to_msg(const hv::interface::KeepClearArea& s) {
    hv_perception_msgs::msg::KeepClearArea m;
    m.detection_timestamp = s.detection_timestamp;
    m.keep_clear_area_id = s.keep_clear_area_id;
    m.confidence = s.confidence;
    m.points = struct_to_msg(s.points);
    m.width = s.width;
    m.length = s.length;
    m.distance = s.distance;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::KeepClearArea msg_to_struct(const hv_perception_msgs::msg::KeepClearArea& m) {
    hv::interface::KeepClearArea s;
    s.detection_timestamp = m.detection_timestamp;
    s.keep_clear_area_id = m.keep_clear_area_id;
    s.confidence = m.confidence;
    s.points = msg_to_struct(m.points);
    s.width = m.width;
    s.length = m.length;
    s.distance = m.distance;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::StopLine struct_to_msg(const hv::interface::StopLine& s) {
    hv_perception_msgs::msg::StopLine m;
    m.detection_timestamp = s.detection_timestamp;
    m.stop_line_id = s.stop_line_id;
    m.confidence = s.confidence;
    m.points = struct_to_msg(s.points);
    m.width = s.width;
    m.distance = s.distance;
    m.is_valid = s.is_valid;
    m.has_traffic_light = s.has_traffic_light;
    m.associated_traffic_light_id = s.associated_traffic_light_id;
    m.stop_line_type = s.stop_line_type;
    return m;
}


inline hv::interface::StopLine msg_to_struct(const hv_perception_msgs::msg::StopLine& m) {
    hv::interface::StopLine s;
    s.detection_timestamp = m.detection_timestamp;
    s.stop_line_id = m.stop_line_id;
    s.confidence = m.confidence;
    s.points = msg_to_struct(m.points);
    s.width = m.width;
    s.distance = m.distance;
    s.is_valid = m.is_valid;
    s.has_traffic_light = m.has_traffic_light;
    s.associated_traffic_light_id = m.associated_traffic_light_id;
    s.stop_line_type = m.stop_line_type;
    return s;
}


inline hv_perception_msgs::msg::WaitingAreaLane struct_to_msg(const hv::interface::WaitingAreaLane& s) {
    hv_perception_msgs::msg::WaitingAreaLane m;
    m.detection_timestamp = s.detection_timestamp;
    m.waiting_area_id = s.waiting_area_id;
    m.area_type = static_cast<int32_t>(s.area_type);
    m.confidence = s.confidence;
    m.left_laneline_id = s.left_laneline_id;
    m.right_laneline_id = s.right_laneline_id;
    m.center_laneline_points.reserve(s.center_laneline_points.size());
    for (const auto& element : s.center_laneline_points) {
        m.center_laneline_points.push_back(struct_to_msg(element));
    }
    m.associated_traffic_light_id = s.associated_traffic_light_id;
    m.associated_stop_line_id = s.associated_stop_line_id;
    m.direction_indicator = s.direction_indicator;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::WaitingAreaLane msg_to_struct(const hv_perception_msgs::msg::WaitingAreaLane& m) {
    hv::interface::WaitingAreaLane s;
    s.detection_timestamp = m.detection_timestamp;
    s.waiting_area_id = m.waiting_area_id;
    s.area_type = static_cast<hv::interface::WaitingAreaType>(m.area_type);
    s.confidence = m.confidence;
    s.left_laneline_id = m.left_laneline_id;
    s.right_laneline_id = m.right_laneline_id;
    s.center_laneline_points.reserve(m.center_laneline_points.size());
    for (const auto& element : m.center_laneline_points) s.center_laneline_points.push_back(msg_to_struct(element));
    s.associated_traffic_light_id = m.associated_traffic_light_id;
    s.associated_stop_line_id = m.associated_stop_line_id;
    s.direction_indicator = m.direction_indicator;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::JunctionConnection struct_to_msg(const hv::interface::JunctionConnection& s) {
    hv_perception_msgs::msg::JunctionConnection m;
    m.detection_timestamp = s.detection_timestamp;
    m.connection_id = s.connection_id;
    m.connection_type = static_cast<int32_t>(s.connection_type);
    m.confidence = s.confidence;
    m.center_line.reserve(s.center_line.size());
    for (const auto& element : s.center_line) {
        m.center_line.push_back(struct_to_msg(element));
    }
    m.connection_width = s.connection_width;
    m.left_line_ids = s.left_line_ids;
    m.right_line_ids = s.right_line_ids;
    m.arrow_ids = s.arrow_ids;
    m.entry_point = struct_to_msg(s.entry_point);
    m.exit_point = struct_to_msg(s.exit_point);
    m.entry_heading = s.entry_heading;
    m.exit_heading = s.exit_heading;
    m.incoming_lane_id = s.incoming_lane_id;
    m.outgoing_lane_id = s.outgoing_lane_id;
    m.has_traffic_light = s.has_traffic_light;
    m.associated_traffic_light_id = s.associated_traffic_light_id;
    m.has_stop_line = s.has_stop_line;
    m.associated_stop_line_id = s.associated_stop_line_id;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::JunctionConnection msg_to_struct(const hv_perception_msgs::msg::JunctionConnection& m) {
    hv::interface::JunctionConnection s;
    s.detection_timestamp = m.detection_timestamp;
    s.connection_id = m.connection_id;
    s.connection_type = static_cast<hv::interface::JunctionConnectionType>(m.connection_type);
    s.confidence = m.confidence;
    s.center_line.reserve(m.center_line.size());
    for (const auto& element : m.center_line) s.center_line.push_back(msg_to_struct(element));
    s.connection_width = m.connection_width;
    s.left_line_ids = m.left_line_ids;
    s.right_line_ids = m.right_line_ids;
    s.arrow_ids = m.arrow_ids;
    s.entry_point = msg_to_struct(m.entry_point);
    s.exit_point = msg_to_struct(m.exit_point);
    s.entry_heading = m.entry_heading;
    s.exit_heading = m.exit_heading;
    s.incoming_lane_id = m.incoming_lane_id;
    s.outgoing_lane_id = m.outgoing_lane_id;
    s.has_traffic_light = m.has_traffic_light;
    s.associated_traffic_light_id = m.associated_traffic_light_id;
    s.has_stop_line = m.has_stop_line;
    s.associated_stop_line_id = m.associated_stop_line_id;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::Junction struct_to_msg(const hv::interface::Junction& s) {
    hv_perception_msgs::msg::Junction m;
    m.detection_timestamp = s.detection_timestamp;
    m.junction_id = s.junction_id;
    m.junction_type = static_cast<int32_t>(s.junction_type);
    m.confidence = s.confidence;
    m.center_position = struct_to_msg(s.center_position);
    m.junction_length = s.junction_length;
    m.junction_width = s.junction_width;
    m.junction_heading = s.junction_heading;
    m.distance = s.distance;
    m.junction_connections.reserve(s.junction_connections.size());
    for (const auto& element : s.junction_connections) {
        m.junction_connections.push_back(struct_to_msg(element));
    }
    m.traffic_light_ids = s.traffic_light_ids;
    m.traffic_sign_ids = s.traffic_sign_ids;
    m.stop_line_ids = s.stop_line_ids;
    m.crosswalk_ids = s.crosswalk_ids;
    m.waiting_area_lanes.reserve(s.waiting_area_lanes.size());
    for (const auto& element : s.waiting_area_lanes) {
        m.waiting_area_lanes.push_back(struct_to_msg(element));
    }
    m.laneline_ids = s.laneline_ids;
    m.curb_ids = s.curb_ids;
    m.occupancy_ids = s.occupancy_ids;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::Junction msg_to_struct(const hv_perception_msgs::msg::Junction& m) {
    hv::interface::Junction s;
    s.detection_timestamp = m.detection_timestamp;
    s.junction_id = m.junction_id;
    s.junction_type = static_cast<hv::interface::JunctionType>(m.junction_type);
    s.confidence = m.confidence;
    s.center_position = msg_to_struct(m.center_position);
    s.junction_length = m.junction_length;
    s.junction_width = m.junction_width;
    s.junction_heading = m.junction_heading;
    s.distance = m.distance;
    s.junction_connections.reserve(m.junction_connections.size());
    for (const auto& element : m.junction_connections) s.junction_connections.push_back(msg_to_struct(element));
    s.traffic_light_ids = m.traffic_light_ids;
    s.traffic_sign_ids = m.traffic_sign_ids;
    s.stop_line_ids = m.stop_line_ids;
    s.crosswalk_ids = m.crosswalk_ids;
    s.waiting_area_lanes.reserve(m.waiting_area_lanes.size());
    for (const auto& element : m.waiting_area_lanes) s.waiting_area_lanes.push_back(msg_to_struct(element));
    s.laneline_ids = m.laneline_ids;
    s.curb_ids = m.curb_ids;
    s.occupancy_ids = m.occupancy_ids;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::CameraSceneList struct_to_msg(const hv::interface::CameraSceneList& s) {
    hv_perception_msgs::msg::CameraSceneList m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.lane_lines.reserve(s.lane_lines.size());
    for (const auto& element : s.lane_lines) {
        m.lane_lines.push_back(struct_to_msg(element));
    }
    m.lanes.reserve(s.lanes.size());
    for (const auto& element : s.lanes) {
        m.lanes.push_back(struct_to_msg(element));
    }
    m.curbs.reserve(s.curbs.size());
    for (const auto& element : s.curbs) {
        m.curbs.push_back(struct_to_msg(element));
    }
    m.stop_lines.reserve(s.stop_lines.size());
    for (const auto& element : s.stop_lines) {
        m.stop_lines.push_back(struct_to_msg(element));
    }
    m.crosswalks.reserve(s.crosswalks.size());
    for (const auto& element : s.crosswalks) {
        m.crosswalks.push_back(struct_to_msg(element));
    }
    m.parking_spaces.reserve(s.parking_spaces.size());
    for (const auto& element : s.parking_spaces) {
        m.parking_spaces.push_back(struct_to_msg(element));
    }
    m.speed_bumps.reserve(s.speed_bumps.size());
    for (const auto& element : s.speed_bumps) {
        m.speed_bumps.push_back(struct_to_msg(element));
    }
    m.keep_clear_areas.reserve(s.keep_clear_areas.size());
    for (const auto& element : s.keep_clear_areas) {
        m.keep_clear_areas.push_back(struct_to_msg(element));
    }
    m.junctions.reserve(s.junctions.size());
    for (const auto& element : s.junctions) {
        m.junctions.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::CameraSceneList msg_to_struct(const hv_perception_msgs::msg::CameraSceneList& m) {
    hv::interface::CameraSceneList s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.lane_lines.reserve(m.lane_lines.size());
    for (const auto& element : m.lane_lines) s.lane_lines.push_back(msg_to_struct(element));
    s.lanes.reserve(m.lanes.size());
    for (const auto& element : m.lanes) s.lanes.push_back(msg_to_struct(element));
    s.curbs.reserve(m.curbs.size());
    for (const auto& element : m.curbs) s.curbs.push_back(msg_to_struct(element));
    s.stop_lines.reserve(m.stop_lines.size());
    for (const auto& element : m.stop_lines) s.stop_lines.push_back(msg_to_struct(element));
    s.crosswalks.reserve(m.crosswalks.size());
    for (const auto& element : m.crosswalks) s.crosswalks.push_back(msg_to_struct(element));
    s.parking_spaces.reserve(m.parking_spaces.size());
    for (const auto& element : m.parking_spaces) s.parking_spaces.push_back(msg_to_struct(element));
    s.speed_bumps.reserve(m.speed_bumps.size());
    for (const auto& element : m.speed_bumps) s.speed_bumps.push_back(msg_to_struct(element));
    s.keep_clear_areas.reserve(m.keep_clear_areas.size());
    for (const auto& element : m.keep_clear_areas) s.keep_clear_areas.push_back(msg_to_struct(element));
    s.junctions.reserve(m.junctions.size());
    for (const auto& element : m.junctions) s.junctions.push_back(msg_to_struct(element));
    return s;
}


inline hv_perception_msgs::msg::PerceptionObject3D struct_to_msg(const hv::interface::PerceptionObject3D& s) {
    hv_perception_msgs::msg::PerceptionObject3D m;
    m.bbox = struct_to_msg(s.bbox);
    m.polygon = s.polygon;
    m.track_id = struct_to_msg(s.track_id);
    m.yawrate = s.yawrate;
    m.tracking_time = s.tracking_time;
    m.latest_tracked_time = s.latest_tracked_time;
    m.timestamp = s.timestamp;
    m.confidence = s.confidence;
    m.bboxdr = struct_to_msg(s.bboxdr);
    m.polygondr = s.polygondr;
    return m;
}


inline hv::interface::PerceptionObject3D msg_to_struct(const hv_perception_msgs::msg::PerceptionObject3D& m) {
    hv::interface::PerceptionObject3D s;
    s.bbox = msg_to_struct(m.bbox);
    s.polygon = m.polygon;
    s.track_id = msg_to_struct(m.track_id);
    s.yawrate = m.yawrate;
    s.tracking_time = m.tracking_time;
    s.latest_tracked_time = m.latest_tracked_time;
    s.timestamp = m.timestamp;
    s.confidence = m.confidence;
    s.bboxdr = msg_to_struct(m.bboxdr);
    s.polygondr = m.polygondr;
    return s;
}


inline hv_perception_msgs::msg::InternalPercepObstacleList struct_to_msg(const hv::interface::InternalPercepObstacleList& s) {
    hv_perception_msgs::msg::InternalPercepObstacleList m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.obstacles.reserve(s.obstacles.size());
    for (const auto& element : s.obstacles) {
        m.obstacles.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::InternalPercepObstacleList msg_to_struct(const hv_perception_msgs::msg::InternalPercepObstacleList& m) {
    hv::interface::InternalPercepObstacleList s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.obstacles.reserve(m.obstacles.size());
    for (const auto& element : m.obstacles) s.obstacles.push_back(msg_to_struct(element));
    return s;
}


inline hv_perception_msgs::msg::PerceptionHeader struct_to_msg(const hv::interface::PerceptionHeader& s) {
    hv_perception_msgs::msg::PerceptionHeader m;
    m.global_timestamp = s.global_timestamp;
    m.local_timestamp = s.local_timestamp;
    m.frame_sequence = s.frame_sequence;
    m.frame_id = s.frame_id;
    m.camera_infos.reserve(s.camera_infos.size());
    for (const auto& element : s.camera_infos) {
        m.camera_infos.push_back(struct_to_msg(element));
    }
    m.lidar_infos.reserve(s.lidar_infos.size());
    for (const auto& element : s.lidar_infos) {
        m.lidar_infos.push_back(struct_to_msg(element));
    }
    m.radar_infos.reserve(s.radar_infos.size());
    for (const auto& element : s.radar_infos) {
        m.radar_infos.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::PerceptionHeader msg_to_struct(const hv_perception_msgs::msg::PerceptionHeader& m) {
    hv::interface::PerceptionHeader s;
    s.global_timestamp = m.global_timestamp;
    s.local_timestamp = m.local_timestamp;
    s.frame_sequence = m.frame_sequence;
    s.frame_id = m.frame_id;
    s.camera_infos.reserve(m.camera_infos.size());
    for (const auto& element : m.camera_infos) s.camera_infos.push_back(msg_to_struct(element));
    s.lidar_infos.reserve(m.lidar_infos.size());
    for (const auto& element : m.lidar_infos) s.lidar_infos.push_back(msg_to_struct(element));
    s.radar_infos.reserve(m.radar_infos.size());
    for (const auto& element : m.radar_infos) s.radar_infos.push_back(msg_to_struct(element));
    return s;
}


inline hv_perception_msgs::msg::VehicleLightStatus struct_to_msg(const hv::interface::VehicleLightStatus& s) {
    hv_perception_msgs::msg::VehicleLightStatus m;
    m.vehicle_blinker_status = s.vehicle_blinker_status;
    m.vehicle_blinker_visible = s.vehicle_blinker_visible;
    m.vehicle_brake_light_status = s.vehicle_brake_light_status;
    m.vehicle_brake_light_visible = s.vehicle_brake_light_visible;
    m.vehicle_corner_lamp_status = s.vehicle_corner_lamp_status;
    m.vehicle_corner_lamp_visible = s.vehicle_corner_lamp_visible;
    return m;
}


inline hv::interface::VehicleLightStatus msg_to_struct(const hv_perception_msgs::msg::VehicleLightStatus& m) {
    hv::interface::VehicleLightStatus s;
    s.vehicle_blinker_status = m.vehicle_blinker_status;
    s.vehicle_blinker_visible = m.vehicle_blinker_visible;
    s.vehicle_brake_light_status = m.vehicle_brake_light_status;
    s.vehicle_brake_light_visible = m.vehicle_brake_light_visible;
    s.vehicle_corner_lamp_status = m.vehicle_corner_lamp_status;
    s.vehicle_corner_lamp_visible = m.vehicle_corner_lamp_visible;
    return s;
}


inline hv_perception_msgs::msg::LidarCornerPoint struct_to_msg(const hv::interface::LidarCornerPoint& s) {
    hv_perception_msgs::msg::LidarCornerPoint m;
    m.pos_in_world = struct_to_msg(s.pos_in_world);
    return m;
}


inline hv::interface::LidarCornerPoint msg_to_struct(const hv_perception_msgs::msg::LidarCornerPoint& m) {
    hv::interface::LidarCornerPoint s;
    s.pos_in_world = msg_to_struct(m.pos_in_world);
    return s;
}


inline hv_perception_msgs::msg::VehicleDoorOpenStatus struct_to_msg(const hv::interface::VehicleDoorOpenStatus& s) {
    hv_perception_msgs::msg::VehicleDoorOpenStatus m;
    m.left_door_open = s.left_door_open;
    m.right_door_open = s.right_door_open;
    m.hood_open = s.hood_open;
    m.trunk_open = s.trunk_open;
    return m;
}


inline hv::interface::VehicleDoorOpenStatus msg_to_struct(const hv_perception_msgs::msg::VehicleDoorOpenStatus& m) {
    hv::interface::VehicleDoorOpenStatus s;
    s.left_door_open = m.left_door_open;
    s.right_door_open = m.right_door_open;
    s.hood_open = m.hood_open;
    s.trunk_open = m.trunk_open;
    return s;
}


inline hv_perception_msgs::msg::VehicleEngineStatus struct_to_msg(const hv::interface::VehicleEngineStatus& s) {
    hv_perception_msgs::msg::VehicleEngineStatus m;
    m.engine_status_in_parkinglots = s.engine_status_in_parkinglots;
    m.engine_status_out_of_parkinglots = s.engine_status_out_of_parkinglots;
    m.vehicle_engine_at_night = s.vehicle_engine_at_night;
    return m;
}


inline hv::interface::VehicleEngineStatus msg_to_struct(const hv_perception_msgs::msg::VehicleEngineStatus& m) {
    hv::interface::VehicleEngineStatus s;
    s.engine_status_in_parkinglots = m.engine_status_in_parkinglots;
    s.engine_status_out_of_parkinglots = m.engine_status_out_of_parkinglots;
    s.vehicle_engine_at_night = m.vehicle_engine_at_night;
    return s;
}


inline hv_perception_msgs::msg::VehicleContent struct_to_msg(const hv::interface::VehicleContent& s) {
    hv_perception_msgs::msg::VehicleContent m;
    m.vehicle_classification = s.vehicle_classification;
    m.number_of_trailers = s.number_of_trailers;
    m.vehicle_engine_status = struct_to_msg(s.vehicle_engine_status);
    m.vehicle_door_open_status = struct_to_msg(s.vehicle_door_open_status);
    m.vehicle_light_status = struct_to_msg(s.vehicle_light_status);
    m.lidar_corner_points.reserve(s.lidar_corner_points.size());
    for (const auto& element : s.lidar_corner_points) {
        m.lidar_corner_points.push_back(struct_to_msg(element));
    }
    m.blocking_ratio = s.blocking_ratio;
    m.wheel_angle_to_body = s.wheel_angle_to_body;
    return m;
}


inline hv::interface::VehicleContent msg_to_struct(const hv_perception_msgs::msg::VehicleContent& m) {
    hv::interface::VehicleContent s;
    s.vehicle_classification = m.vehicle_classification;
    s.number_of_trailers = m.number_of_trailers;
    s.vehicle_engine_status = msg_to_struct(m.vehicle_engine_status);
    s.vehicle_door_open_status = msg_to_struct(m.vehicle_door_open_status);
    s.vehicle_light_status = msg_to_struct(m.vehicle_light_status);
    s.lidar_corner_points.reserve(m.lidar_corner_points.size());
    for (const auto& element : m.lidar_corner_points) s.lidar_corner_points.push_back(msg_to_struct(element));
    s.blocking_ratio = m.blocking_ratio;
    s.wheel_angle_to_body = m.wheel_angle_to_body;
    return s;
}


inline hv_perception_msgs::msg::PedestrianContent struct_to_msg(const hv::interface::PedestrianContent& s) {
    hv_perception_msgs::msg::PedestrianContent m;
    m.age = static_cast<int32_t>(s.age);
    m.pose = static_cast<int32_t>(s.pose);
    m.facing = static_cast<int32_t>(s.facing);
    m.identity = static_cast<int32_t>(s.identity);
    m.hold_sign = static_cast<int32_t>(s.hold_sign);
    m.covering_body_part = s.covering_body_part;
    m.is_with_attachment = s.is_with_attachment;
    m.is_crowed = s.is_crowed;
    m.bev_bounding_box.reserve(s.bev_bounding_box.size());
    for (const auto& element : s.bev_bounding_box) {
        m.bev_bounding_box.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::PedestrianContent msg_to_struct(const hv_perception_msgs::msg::PedestrianContent& m) {
    hv::interface::PedestrianContent s;
    s.age = static_cast<hv::interface::PedestrianAge>(m.age);
    s.pose = static_cast<hv::interface::PedestrianPose>(m.pose);
    s.facing = static_cast<hv::interface::PedestrianFacing>(m.facing);
    s.identity = static_cast<hv::interface::PedestrianIdentity>(m.identity);
    s.hold_sign = static_cast<hv::interface::PedestrianHoldSign>(m.hold_sign);
    s.covering_body_part = m.covering_body_part;
    s.is_with_attachment = m.is_with_attachment;
    s.is_crowed = m.is_crowed;
    s.bev_bounding_box.reserve(m.bev_bounding_box.size());
    for (const auto& element : m.bev_bounding_box) s.bev_bounding_box.push_back(msg_to_struct(element));
    return s;
}


inline hv_perception_msgs::msg::BicycleLightStatus struct_to_msg(const hv::interface::BicycleLightStatus& s) {
    hv_perception_msgs::msg::BicycleLightStatus m;
    m.bicycle_blinker_status = s.bicycle_blinker_status;
    m.bicycle_brake_light_status = s.bicycle_brake_light_status;
    m.bicycle_corner_lamp_status = s.bicycle_corner_lamp_status;
    return m;
}


inline hv::interface::BicycleLightStatus msg_to_struct(const hv_perception_msgs::msg::BicycleLightStatus& m) {
    hv::interface::BicycleLightStatus s;
    s.bicycle_blinker_status = m.bicycle_blinker_status;
    s.bicycle_brake_light_status = m.bicycle_brake_light_status;
    s.bicycle_corner_lamp_status = m.bicycle_corner_lamp_status;
    return s;
}


inline hv_perception_msgs::msg::BicycleContent struct_to_msg(const hv::interface::BicycleContent& s) {
    hv_perception_msgs::msg::BicycleContent m;
    m.bicycle_classification = s.bicycle_classification;
    m.is_bicycle_with_driver = s.is_bicycle_with_driver;
    m.driverfacing = static_cast<int32_t>(s.driverfacing);
    m.bicycle_light_status = struct_to_msg(s.bicycle_light_status);
    m.lidar_corner_points.reserve(s.lidar_corner_points.size());
    for (const auto& element : s.lidar_corner_points) {
        m.lidar_corner_points.push_back(struct_to_msg(element));
    }
    m.wheel_angle_to_body = s.wheel_angle_to_body;
    return m;
}


inline hv::interface::BicycleContent msg_to_struct(const hv_perception_msgs::msg::BicycleContent& m) {
    hv::interface::BicycleContent s;
    s.bicycle_classification = m.bicycle_classification;
    s.is_bicycle_with_driver = m.is_bicycle_with_driver;
    s.driverfacing = static_cast<hv::interface::PedestrianFacing>(m.driverfacing);
    s.bicycle_light_status = msg_to_struct(m.bicycle_light_status);
    s.lidar_corner_points.reserve(m.lidar_corner_points.size());
    for (const auto& element : m.lidar_corner_points) s.lidar_corner_points.push_back(msg_to_struct(element));
    s.wheel_angle_to_body = m.wheel_angle_to_body;
    return s;
}


inline hv_perception_msgs::msg::AnimalContent struct_to_msg(const hv::interface::AnimalContent& s) {
    hv_perception_msgs::msg::AnimalContent m;
    m.animal_classification = s.animal_classification;
    m.animal_behavior = s.animal_behavior;
    m.animal_size = s.animal_size;
    return m;
}


inline hv::interface::AnimalContent msg_to_struct(const hv_perception_msgs::msg::AnimalContent& m) {
    hv::interface::AnimalContent s;
    s.animal_classification = m.animal_classification;
    s.animal_behavior = m.animal_behavior;
    s.animal_size = m.animal_size;
    return s;
}


inline hv_perception_msgs::msg::UnknownContent struct_to_msg(const hv::interface::UnknownContent& s) {
    hv_perception_msgs::msg::UnknownContent m;
    m.unknown_behavior = s.unknown_behavior;
    m.allow_run_over = s.allow_run_over;
    m.bev_polygon = struct_to_msg(s.bev_polygon);
    return m;
}


inline hv::interface::UnknownContent msg_to_struct(const hv_perception_msgs::msg::UnknownContent& m) {
    hv::interface::UnknownContent s;
    s.unknown_behavior = m.unknown_behavior;
    s.allow_run_over = m.allow_run_over;
    s.bev_polygon = msg_to_struct(m.bev_polygon);
    return s;
}


inline hv_perception_msgs::msg::StaticObstacleContent struct_to_msg(const hv::interface::StaticObstacleContent& s) {
    hv_perception_msgs::msg::StaticObstacleContent m;
    m.obstacle_id = s.obstacle_id;
    m.obstacle_type = static_cast<int32_t>(s.obstacle_type);
    m.confidence = s.confidence;
    m.position = struct_to_msg(s.position);
    m.length = s.length;
    m.width = s.width;
    m.height = s.height;
    m.heading = s.heading;
    m.is_occluded = s.is_occluded;
    m.occlusion_ratio = s.occlusion_ratio;
    return m;
}


inline hv::interface::StaticObstacleContent msg_to_struct(const hv_perception_msgs::msg::StaticObstacleContent& m) {
    hv::interface::StaticObstacleContent s;
    s.obstacle_id = m.obstacle_id;
    s.obstacle_type = static_cast<hv::interface::StaticObstacleType>(m.obstacle_type);
    s.confidence = m.confidence;
    s.position = msg_to_struct(m.position);
    s.length = m.length;
    s.width = m.width;
    s.height = m.height;
    s.heading = m.heading;
    s.is_occluded = m.is_occluded;
    s.occlusion_ratio = m.occlusion_ratio;
    return s;
}


inline hv_perception_msgs::msg::TrafficConeContent struct_to_msg(const hv::interface::TrafficConeContent& s) {
    hv_perception_msgs::msg::TrafficConeContent m;
    m.cone_id = s.cone_id;
    m.cone_type = static_cast<int32_t>(s.cone_type);
    m.confidence = s.confidence;
    m.position = struct_to_msg(s.position);
    m.width = s.width;
    m.height = s.height;
    m.is_fallen = s.is_fallen;
    m.is_loaded = s.is_loaded;
    m.distance = s.distance;
    return m;
}


inline hv::interface::TrafficConeContent msg_to_struct(const hv_perception_msgs::msg::TrafficConeContent& m) {
    hv::interface::TrafficConeContent s;
    s.cone_id = m.cone_id;
    s.cone_type = static_cast<hv::interface::TrafficConeType>(m.cone_type);
    s.confidence = m.confidence;
    s.position = msg_to_struct(m.position);
    s.width = m.width;
    s.height = m.height;
    s.is_fallen = m.is_fallen;
    s.is_loaded = m.is_loaded;
    s.distance = m.distance;
    return s;
}


inline hv_perception_msgs::msg::TypeHistory struct_to_msg(const hv::interface::TypeHistory& s) {
    hv_perception_msgs::msg::TypeHistory m;
    m.frame_sequence = s.frame_sequence;
    m.type = s.type;
    return m;
}


inline hv::interface::TypeHistory msg_to_struct(const hv_perception_msgs::msg::TypeHistory& m) {
    hv::interface::TypeHistory s;
    s.frame_sequence = m.frame_sequence;
    s.type = m.type;
    return s;
}


inline hv_perception_msgs::msg::Obstacle struct_to_msg(const hv::interface::Obstacle& s) {
    hv_perception_msgs::msg::Obstacle m;
    m.track_id = s.track_id;
    m.track_status = s.track_status;
    m.track_age = s.track_age;
    m.track_time = s.track_time;
    m.sensor_fusion_source = s.sensor_fusion_source;
    m.type_history.reserve(s.type_history.size());
    for (const auto& element : s.type_history) {
        m.type_history.push_back(struct_to_msg(element));
    }
    m.confidence = s.confidence;
    m.position = struct_to_msg(s.position);
    m.velocity = struct_to_msg(s.velocity);
    m.acceleration = struct_to_msg(s.acceleration);
    m.abs_speed = s.abs_speed;
    m.abs_acceleration = s.abs_acceleration;
    m.heading_to_ego = s.heading_to_ego;
    m.heading_rate = s.heading_rate;
    m.position_boot = struct_to_msg(s.position_boot);
    m.position_boot_stddev = struct_to_msg(s.position_boot_stddev);
    m.velocity_boot = struct_to_msg(s.velocity_boot);
    m.velocity_boot_stddev = struct_to_msg(s.velocity_boot_stddev);
    m.acceleration_boot = struct_to_msg(s.acceleration_boot);
    m.acceleration_boot_stddev = struct_to_msg(s.acceleration_boot_stddev);
    m.yaw = s.yaw;
    m.yaw_stddev = s.yaw_stddev;
    m.yaw_rate = s.yaw_rate;
    m.yaw_rate_stddev = s.yaw_rate_stddev;
    m.length = s.length;
    m.width = s.width;
    m.height = s.height;
    m.motion_status = s.motion_status;
    m.vehicle_content = struct_to_msg(s.vehicle_content);
    m.pedestrian_content = struct_to_msg(s.pedestrian_content);
    m.bicycle_content = struct_to_msg(s.bicycle_content);
    m.animal_content = struct_to_msg(s.animal_content);
    m.unknown_content = struct_to_msg(s.unknown_content);
    m.static_obstacle_content = struct_to_msg(s.static_obstacle_content);
    m.traffic_cone_content = struct_to_msg(s.traffic_cone_content);
    return m;
}


inline hv::interface::Obstacle msg_to_struct(const hv_perception_msgs::msg::Obstacle& m) {
    hv::interface::Obstacle s;
    s.track_id = m.track_id;
    s.track_status = m.track_status;
    s.track_age = m.track_age;
    s.track_time = m.track_time;
    s.sensor_fusion_source = m.sensor_fusion_source;
    s.type_history.reserve(m.type_history.size());
    for (const auto& element : m.type_history) s.type_history.push_back(msg_to_struct(element));
    s.confidence = m.confidence;
    s.position = msg_to_struct(m.position);
    s.velocity = msg_to_struct(m.velocity);
    s.acceleration = msg_to_struct(m.acceleration);
    s.abs_speed = m.abs_speed;
    s.abs_acceleration = m.abs_acceleration;
    s.heading_to_ego = m.heading_to_ego;
    s.heading_rate = m.heading_rate;
    s.position_boot = msg_to_struct(m.position_boot);
    s.position_boot_stddev = msg_to_struct(m.position_boot_stddev);
    s.velocity_boot = msg_to_struct(m.velocity_boot);
    s.velocity_boot_stddev = msg_to_struct(m.velocity_boot_stddev);
    s.acceleration_boot = msg_to_struct(m.acceleration_boot);
    s.acceleration_boot_stddev = msg_to_struct(m.acceleration_boot_stddev);
    s.yaw = m.yaw;
    s.yaw_stddev = m.yaw_stddev;
    s.yaw_rate = m.yaw_rate;
    s.yaw_rate_stddev = m.yaw_rate_stddev;
    s.length = m.length;
    s.width = m.width;
    s.height = m.height;
    s.motion_status = m.motion_status;
    s.vehicle_content = msg_to_struct(m.vehicle_content);
    s.pedestrian_content = msg_to_struct(m.pedestrian_content);
    s.bicycle_content = msg_to_struct(m.bicycle_content);
    s.animal_content = msg_to_struct(m.animal_content);
    s.unknown_content = msg_to_struct(m.unknown_content);
    s.static_obstacle_content = msg_to_struct(m.static_obstacle_content);
    s.traffic_cone_content = msg_to_struct(m.traffic_cone_content);
    return s;
}


inline hv_perception_msgs::msg::ObstacleList struct_to_msg(const hv::interface::ObstacleList& s) {
    hv_perception_msgs::msg::ObstacleList m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.obstacles.reserve(s.obstacles.size());
    for (const auto& element : s.obstacles) {
        m.obstacles.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::ObstacleList msg_to_struct(const hv_perception_msgs::msg::ObstacleList& m) {
    hv::interface::ObstacleList s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.obstacles.reserve(m.obstacles.size());
    for (const auto& element : m.obstacles) s.obstacles.push_back(msg_to_struct(element));
    return s;
}


inline hv_perception_msgs::msg::LidarOccupancyGrid struct_to_msg(const hv::interface::LidarOccupancyGrid& s) {
    hv_perception_msgs::msg::LidarOccupancyGrid m;
    m.detection_timestamp = s.detection_timestamp;
    m.grid_id = s.grid_id;
    m.confidence = s.confidence;
    m.grid_row_num = s.grid_row_num;
    m.grid_col_num = s.grid_col_num;
    m.grid_packed_data = s.grid_packed_data;
    m.grid_resolution = s.grid_resolution;
    m.grid_origin = struct_to_msg(s.grid_origin);
    m.grid_width = s.grid_width;
    m.grid_height = s.grid_height;
    m.is_valid = s.is_valid;
    return m;
}


inline hv::interface::LidarOccupancyGrid msg_to_struct(const hv_perception_msgs::msg::LidarOccupancyGrid& m) {
    hv::interface::LidarOccupancyGrid s;
    s.detection_timestamp = m.detection_timestamp;
    s.grid_id = m.grid_id;
    s.confidence = m.confidence;
    s.grid_row_num = m.grid_row_num;
    s.grid_col_num = m.grid_col_num;
    s.grid_packed_data = m.grid_packed_data;
    s.grid_resolution = m.grid_resolution;
    s.grid_origin = msg_to_struct(m.grid_origin);
    s.grid_width = m.grid_width;
    s.grid_height = m.grid_height;
    s.is_valid = m.is_valid;
    return s;
}


inline hv_perception_msgs::msg::LidarOccupancyGridList struct_to_msg(const hv::interface::LidarOccupancyGridList& s) {
    hv_perception_msgs::msg::LidarOccupancyGridList m;
    m.header = struct_to_msg(s.header);
    m.meta_header = struct_to_msg(s.meta_header);
    m.occupancy_grids.reserve(s.occupancy_grids.size());
    for (const auto& element : s.occupancy_grids) {
        m.occupancy_grids.push_back(struct_to_msg(element));
    }
    return m;
}


inline hv::interface::LidarOccupancyGridList msg_to_struct(const hv_perception_msgs::msg::LidarOccupancyGridList& m) {
    hv::interface::LidarOccupancyGridList s;
    s.header = msg_to_struct(m.header);
    s.meta_header = msg_to_struct(m.meta_header);
    s.occupancy_grids.reserve(m.occupancy_grids.size());
    for (const auto& element : m.occupancy_grids) s.occupancy_grids.push_back(msg_to_struct(element));
    return s;
}



} // namespace converter
} // namespace hv
