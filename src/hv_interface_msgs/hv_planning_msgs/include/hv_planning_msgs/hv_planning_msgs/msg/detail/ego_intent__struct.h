// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__STRUCT_H_
#define HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'lateral_intents'
// Member 'lateral_index'
// Member 'longitudinal_intens'
// Member 'longitudinal_index'
#include "rosidl_runtime_c/primitives_sequence.h"

/// Struct defined in msg/EgoIntent in the package hv_planning_msgs.
/**
  * Generated from /home/<USER>/Workspace/1_Repo/0_Baseline/hv_interface/interface/planning/trajectory.h
  * Original struct: EgoIntent
 */
typedef struct hv_planning_msgs__msg__EgoIntent
{
  rosidl_runtime_c__uint8__Sequence lateral_intents;
  rosidl_runtime_c__uint32__Sequence lateral_index;
  rosidl_runtime_c__uint8__Sequence longitudinal_intens;
  rosidl_runtime_c__uint32__Sequence longitudinal_index;
} hv_planning_msgs__msg__EgoIntent;

// Struct for a sequence of hv_planning_msgs__msg__EgoIntent.
typedef struct hv_planning_msgs__msg__EgoIntent__Sequence
{
  hv_planning_msgs__msg__EgoIntent * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hv_planning_msgs__msg__EgoIntent__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__STRUCT_H_
