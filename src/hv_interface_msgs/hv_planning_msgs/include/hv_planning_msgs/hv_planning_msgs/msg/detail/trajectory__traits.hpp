// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hv_planning_msgs:msg/Trajectory.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__TRAITS_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hv_planning_msgs/msg/detail/trajectory__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__traits.hpp"
// Member 'planning_header'
// Member 'routing_header'
#include "hv_planning_msgs/msg/detail/planning_header__traits.hpp"
// Member 'trajectory_point'
#include "hv_planning_msgs/msg/detail/trajectory_point__traits.hpp"
// Member 'estop'
#include "hv_planning_msgs/msg/detail/estop__traits.hpp"
// Member 'path_point'
#include "hv_planning_msgs/msg/detail/path_point__traits.hpp"
// Member 'decision'
#include "hv_planning_msgs/msg/detail/decision_result__traits.hpp"
// Member 'latency_stats'
#include "hv_planning_msgs/msg/detail/latency_stats__traits.hpp"
// Member 'engage_advice'
#include "hv_planning_msgs/msg/detail/engage_advice__traits.hpp"
// Member 'critical_region'
#include "hv_planning_msgs/msg/detail/critical_region__traits.hpp"
// Member 'ego_intent'
#include "hv_planning_msgs/msg/detail/ego_intent__traits.hpp"

namespace hv_planning_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const Trajectory & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: planning_header
  {
    out << "planning_header: ";
    to_flow_style_yaml(msg.planning_header, out);
    out << ", ";
  }

  // member: total_path_length
  {
    out << "total_path_length: ";
    rosidl_generator_traits::value_to_yaml(msg.total_path_length, out);
    out << ", ";
  }

  // member: total_path_time
  {
    out << "total_path_time: ";
    rosidl_generator_traits::value_to_yaml(msg.total_path_time, out);
    out << ", ";
  }

  // member: trajectory_point
  {
    if (msg.trajectory_point.size() == 0) {
      out << "trajectory_point: []";
    } else {
      out << "trajectory_point: [";
      size_t pending_items = msg.trajectory_point.size();
      for (auto item : msg.trajectory_point) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: estop
  {
    out << "estop: ";
    to_flow_style_yaml(msg.estop, out);
    out << ", ";
  }

  // member: path_point
  {
    if (msg.path_point.size() == 0) {
      out << "path_point: []";
    } else {
      out << "path_point: [";
      size_t pending_items = msg.path_point.size();
      for (auto item : msg.path_point) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: is_replan
  {
    out << "is_replan: ";
    rosidl_generator_traits::value_to_yaml(msg.is_replan, out);
    out << ", ";
  }

  // member: replan_reason
  {
    out << "replan_reason: ";
    rosidl_generator_traits::value_to_yaml(msg.replan_reason, out);
    out << ", ";
  }

  // member: is_uturn
  {
    out << "is_uturn: ";
    rosidl_generator_traits::value_to_yaml(msg.is_uturn, out);
    out << ", ";
  }

  // member: gear
  {
    out << "gear: ";
    rosidl_generator_traits::value_to_yaml(msg.gear, out);
    out << ", ";
  }

  // member: decision
  {
    out << "decision: ";
    to_flow_style_yaml(msg.decision, out);
    out << ", ";
  }

  // member: latency_stats
  {
    out << "latency_stats: ";
    to_flow_style_yaml(msg.latency_stats, out);
    out << ", ";
  }

  // member: routing_header
  {
    out << "routing_header: ";
    to_flow_style_yaml(msg.routing_header, out);
    out << ", ";
  }

  // member: right_of_way_status
  {
    out << "right_of_way_status: ";
    rosidl_generator_traits::value_to_yaml(msg.right_of_way_status, out);
    out << ", ";
  }

  // member: lane_id
  {
    if (msg.lane_id.size() == 0) {
      out << "lane_id: []";
    } else {
      out << "lane_id: [";
      size_t pending_items = msg.lane_id.size();
      for (auto item : msg.lane_id) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: engage_advice
  {
    out << "engage_advice: ";
    to_flow_style_yaml(msg.engage_advice, out);
    out << ", ";
  }

  // member: critical_region
  {
    out << "critical_region: ";
    to_flow_style_yaml(msg.critical_region, out);
    out << ", ";
  }

  // member: trajectory_type
  {
    out << "trajectory_type: ";
    rosidl_generator_traits::value_to_yaml(msg.trajectory_type, out);
    out << ", ";
  }

  // member: target_lane_id
  {
    if (msg.target_lane_id.size() == 0) {
      out << "target_lane_id: []";
    } else {
      out << "target_lane_id: [";
      size_t pending_items = msg.target_lane_id.size();
      for (auto item : msg.target_lane_id) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: ego_intent
  {
    out << "ego_intent: ";
    to_flow_style_yaml(msg.ego_intent, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Trajectory & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: planning_header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "planning_header:\n";
    to_block_style_yaml(msg.planning_header, out, indentation + 2);
  }

  // member: total_path_length
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "total_path_length: ";
    rosidl_generator_traits::value_to_yaml(msg.total_path_length, out);
    out << "\n";
  }

  // member: total_path_time
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "total_path_time: ";
    rosidl_generator_traits::value_to_yaml(msg.total_path_time, out);
    out << "\n";
  }

  // member: trajectory_point
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.trajectory_point.size() == 0) {
      out << "trajectory_point: []\n";
    } else {
      out << "trajectory_point:\n";
      for (auto item : msg.trajectory_point) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: estop
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "estop:\n";
    to_block_style_yaml(msg.estop, out, indentation + 2);
  }

  // member: path_point
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.path_point.size() == 0) {
      out << "path_point: []\n";
    } else {
      out << "path_point:\n";
      for (auto item : msg.path_point) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: is_replan
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "is_replan: ";
    rosidl_generator_traits::value_to_yaml(msg.is_replan, out);
    out << "\n";
  }

  // member: replan_reason
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "replan_reason: ";
    rosidl_generator_traits::value_to_yaml(msg.replan_reason, out);
    out << "\n";
  }

  // member: is_uturn
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "is_uturn: ";
    rosidl_generator_traits::value_to_yaml(msg.is_uturn, out);
    out << "\n";
  }

  // member: gear
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gear: ";
    rosidl_generator_traits::value_to_yaml(msg.gear, out);
    out << "\n";
  }

  // member: decision
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "decision:\n";
    to_block_style_yaml(msg.decision, out, indentation + 2);
  }

  // member: latency_stats
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "latency_stats:\n";
    to_block_style_yaml(msg.latency_stats, out, indentation + 2);
  }

  // member: routing_header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "routing_header:\n";
    to_block_style_yaml(msg.routing_header, out, indentation + 2);
  }

  // member: right_of_way_status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "right_of_way_status: ";
    rosidl_generator_traits::value_to_yaml(msg.right_of_way_status, out);
    out << "\n";
  }

  // member: lane_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.lane_id.size() == 0) {
      out << "lane_id: []\n";
    } else {
      out << "lane_id:\n";
      for (auto item : msg.lane_id) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: engage_advice
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "engage_advice:\n";
    to_block_style_yaml(msg.engage_advice, out, indentation + 2);
  }

  // member: critical_region
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "critical_region:\n";
    to_block_style_yaml(msg.critical_region, out, indentation + 2);
  }

  // member: trajectory_type
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "trajectory_type: ";
    rosidl_generator_traits::value_to_yaml(msg.trajectory_type, out);
    out << "\n";
  }

  // member: target_lane_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.target_lane_id.size() == 0) {
      out << "target_lane_id: []\n";
    } else {
      out << "target_lane_id:\n";
      for (auto item : msg.target_lane_id) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: ego_intent
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "ego_intent:\n";
    to_block_style_yaml(msg.ego_intent, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Trajectory & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hv_planning_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hv_planning_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hv_planning_msgs::msg::Trajectory & msg,
  std::ostream & out, size_t indentation = 0)
{
  hv_planning_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hv_planning_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hv_planning_msgs::msg::Trajectory & msg)
{
  return hv_planning_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hv_planning_msgs::msg::Trajectory>()
{
  return "hv_planning_msgs::msg::Trajectory";
}

template<>
inline const char * name<hv_planning_msgs::msg::Trajectory>()
{
  return "hv_planning_msgs/msg/Trajectory";
}

template<>
struct has_fixed_size<hv_planning_msgs::msg::Trajectory>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<hv_planning_msgs::msg::Trajectory>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<hv_planning_msgs::msg::Trajectory>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__TRAITS_HPP_
