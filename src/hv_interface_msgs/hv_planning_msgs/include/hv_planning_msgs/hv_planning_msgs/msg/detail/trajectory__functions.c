// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from hv_planning_msgs:msg/Trajectory.idl
// generated code does not contain a copyright notice
#include "hv_planning_msgs/msg/detail/trajectory__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "hv_common_msgs/msg/detail/header__functions.h"
// Member `planning_header`
// Member `routing_header`
#include "hv_planning_msgs/msg/detail/planning_header__functions.h"
// Member `trajectory_point`
#include "hv_planning_msgs/msg/detail/trajectory_point__functions.h"
// Member `estop`
#include "hv_planning_msgs/msg/detail/estop__functions.h"
// Member `path_point`
#include "hv_planning_msgs/msg/detail/path_point__functions.h"
// Member `replan_reason`
// Member `lane_id`
// Member `target_lane_id`
#include "rosidl_runtime_c/string_functions.h"
// Member `decision`
#include "hv_planning_msgs/msg/detail/decision_result__functions.h"
// Member `latency_stats`
#include "hv_planning_msgs/msg/detail/latency_stats__functions.h"
// Member `engage_advice`
#include "hv_planning_msgs/msg/detail/engage_advice__functions.h"
// Member `critical_region`
#include "hv_planning_msgs/msg/detail/critical_region__functions.h"
// Member `ego_intent`
#include "hv_planning_msgs/msg/detail/ego_intent__functions.h"

bool
hv_planning_msgs__msg__Trajectory__init(hv_planning_msgs__msg__Trajectory * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__init(&msg->header)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // planning_header
  if (!hv_planning_msgs__msg__PlanningHeader__init(&msg->planning_header)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // total_path_length
  // total_path_time
  // trajectory_point
  if (!hv_planning_msgs__msg__TrajectoryPoint__Sequence__init(&msg->trajectory_point, 0)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // estop
  if (!hv_planning_msgs__msg__Estop__init(&msg->estop)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // path_point
  if (!hv_planning_msgs__msg__PathPoint__Sequence__init(&msg->path_point, 0)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // is_replan
  // replan_reason
  if (!rosidl_runtime_c__String__init(&msg->replan_reason)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // is_uturn
  // gear
  // decision
  if (!hv_planning_msgs__msg__DecisionResult__init(&msg->decision)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // latency_stats
  if (!hv_planning_msgs__msg__LatencyStats__init(&msg->latency_stats)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // routing_header
  if (!hv_planning_msgs__msg__PlanningHeader__init(&msg->routing_header)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // right_of_way_status
  // lane_id
  if (!rosidl_runtime_c__String__Sequence__init(&msg->lane_id, 0)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // engage_advice
  if (!hv_planning_msgs__msg__EngageAdvice__init(&msg->engage_advice)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // critical_region
  if (!hv_planning_msgs__msg__CriticalRegion__init(&msg->critical_region)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // trajectory_type
  // target_lane_id
  if (!rosidl_runtime_c__String__Sequence__init(&msg->target_lane_id, 0)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  // ego_intent
  if (!hv_planning_msgs__msg__EgoIntent__init(&msg->ego_intent)) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
    return false;
  }
  return true;
}

void
hv_planning_msgs__msg__Trajectory__fini(hv_planning_msgs__msg__Trajectory * msg)
{
  if (!msg) {
    return;
  }
  // header
  hv_common_msgs__msg__Header__fini(&msg->header);
  // planning_header
  hv_planning_msgs__msg__PlanningHeader__fini(&msg->planning_header);
  // total_path_length
  // total_path_time
  // trajectory_point
  hv_planning_msgs__msg__TrajectoryPoint__Sequence__fini(&msg->trajectory_point);
  // estop
  hv_planning_msgs__msg__Estop__fini(&msg->estop);
  // path_point
  hv_planning_msgs__msg__PathPoint__Sequence__fini(&msg->path_point);
  // is_replan
  // replan_reason
  rosidl_runtime_c__String__fini(&msg->replan_reason);
  // is_uturn
  // gear
  // decision
  hv_planning_msgs__msg__DecisionResult__fini(&msg->decision);
  // latency_stats
  hv_planning_msgs__msg__LatencyStats__fini(&msg->latency_stats);
  // routing_header
  hv_planning_msgs__msg__PlanningHeader__fini(&msg->routing_header);
  // right_of_way_status
  // lane_id
  rosidl_runtime_c__String__Sequence__fini(&msg->lane_id);
  // engage_advice
  hv_planning_msgs__msg__EngageAdvice__fini(&msg->engage_advice);
  // critical_region
  hv_planning_msgs__msg__CriticalRegion__fini(&msg->critical_region);
  // trajectory_type
  // target_lane_id
  rosidl_runtime_c__String__Sequence__fini(&msg->target_lane_id);
  // ego_intent
  hv_planning_msgs__msg__EgoIntent__fini(&msg->ego_intent);
}

bool
hv_planning_msgs__msg__Trajectory__are_equal(const hv_planning_msgs__msg__Trajectory * lhs, const hv_planning_msgs__msg__Trajectory * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // planning_header
  if (!hv_planning_msgs__msg__PlanningHeader__are_equal(
      &(lhs->planning_header), &(rhs->planning_header)))
  {
    return false;
  }
  // total_path_length
  if (lhs->total_path_length != rhs->total_path_length) {
    return false;
  }
  // total_path_time
  if (lhs->total_path_time != rhs->total_path_time) {
    return false;
  }
  // trajectory_point
  if (!hv_planning_msgs__msg__TrajectoryPoint__Sequence__are_equal(
      &(lhs->trajectory_point), &(rhs->trajectory_point)))
  {
    return false;
  }
  // estop
  if (!hv_planning_msgs__msg__Estop__are_equal(
      &(lhs->estop), &(rhs->estop)))
  {
    return false;
  }
  // path_point
  if (!hv_planning_msgs__msg__PathPoint__Sequence__are_equal(
      &(lhs->path_point), &(rhs->path_point)))
  {
    return false;
  }
  // is_replan
  if (lhs->is_replan != rhs->is_replan) {
    return false;
  }
  // replan_reason
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->replan_reason), &(rhs->replan_reason)))
  {
    return false;
  }
  // is_uturn
  if (lhs->is_uturn != rhs->is_uturn) {
    return false;
  }
  // gear
  if (lhs->gear != rhs->gear) {
    return false;
  }
  // decision
  if (!hv_planning_msgs__msg__DecisionResult__are_equal(
      &(lhs->decision), &(rhs->decision)))
  {
    return false;
  }
  // latency_stats
  if (!hv_planning_msgs__msg__LatencyStats__are_equal(
      &(lhs->latency_stats), &(rhs->latency_stats)))
  {
    return false;
  }
  // routing_header
  if (!hv_planning_msgs__msg__PlanningHeader__are_equal(
      &(lhs->routing_header), &(rhs->routing_header)))
  {
    return false;
  }
  // right_of_way_status
  if (lhs->right_of_way_status != rhs->right_of_way_status) {
    return false;
  }
  // lane_id
  if (!rosidl_runtime_c__String__Sequence__are_equal(
      &(lhs->lane_id), &(rhs->lane_id)))
  {
    return false;
  }
  // engage_advice
  if (!hv_planning_msgs__msg__EngageAdvice__are_equal(
      &(lhs->engage_advice), &(rhs->engage_advice)))
  {
    return false;
  }
  // critical_region
  if (!hv_planning_msgs__msg__CriticalRegion__are_equal(
      &(lhs->critical_region), &(rhs->critical_region)))
  {
    return false;
  }
  // trajectory_type
  if (lhs->trajectory_type != rhs->trajectory_type) {
    return false;
  }
  // target_lane_id
  if (!rosidl_runtime_c__String__Sequence__are_equal(
      &(lhs->target_lane_id), &(rhs->target_lane_id)))
  {
    return false;
  }
  // ego_intent
  if (!hv_planning_msgs__msg__EgoIntent__are_equal(
      &(lhs->ego_intent), &(rhs->ego_intent)))
  {
    return false;
  }
  return true;
}

bool
hv_planning_msgs__msg__Trajectory__copy(
  const hv_planning_msgs__msg__Trajectory * input,
  hv_planning_msgs__msg__Trajectory * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // planning_header
  if (!hv_planning_msgs__msg__PlanningHeader__copy(
      &(input->planning_header), &(output->planning_header)))
  {
    return false;
  }
  // total_path_length
  output->total_path_length = input->total_path_length;
  // total_path_time
  output->total_path_time = input->total_path_time;
  // trajectory_point
  if (!hv_planning_msgs__msg__TrajectoryPoint__Sequence__copy(
      &(input->trajectory_point), &(output->trajectory_point)))
  {
    return false;
  }
  // estop
  if (!hv_planning_msgs__msg__Estop__copy(
      &(input->estop), &(output->estop)))
  {
    return false;
  }
  // path_point
  if (!hv_planning_msgs__msg__PathPoint__Sequence__copy(
      &(input->path_point), &(output->path_point)))
  {
    return false;
  }
  // is_replan
  output->is_replan = input->is_replan;
  // replan_reason
  if (!rosidl_runtime_c__String__copy(
      &(input->replan_reason), &(output->replan_reason)))
  {
    return false;
  }
  // is_uturn
  output->is_uturn = input->is_uturn;
  // gear
  output->gear = input->gear;
  // decision
  if (!hv_planning_msgs__msg__DecisionResult__copy(
      &(input->decision), &(output->decision)))
  {
    return false;
  }
  // latency_stats
  if (!hv_planning_msgs__msg__LatencyStats__copy(
      &(input->latency_stats), &(output->latency_stats)))
  {
    return false;
  }
  // routing_header
  if (!hv_planning_msgs__msg__PlanningHeader__copy(
      &(input->routing_header), &(output->routing_header)))
  {
    return false;
  }
  // right_of_way_status
  output->right_of_way_status = input->right_of_way_status;
  // lane_id
  if (!rosidl_runtime_c__String__Sequence__copy(
      &(input->lane_id), &(output->lane_id)))
  {
    return false;
  }
  // engage_advice
  if (!hv_planning_msgs__msg__EngageAdvice__copy(
      &(input->engage_advice), &(output->engage_advice)))
  {
    return false;
  }
  // critical_region
  if (!hv_planning_msgs__msg__CriticalRegion__copy(
      &(input->critical_region), &(output->critical_region)))
  {
    return false;
  }
  // trajectory_type
  output->trajectory_type = input->trajectory_type;
  // target_lane_id
  if (!rosidl_runtime_c__String__Sequence__copy(
      &(input->target_lane_id), &(output->target_lane_id)))
  {
    return false;
  }
  // ego_intent
  if (!hv_planning_msgs__msg__EgoIntent__copy(
      &(input->ego_intent), &(output->ego_intent)))
  {
    return false;
  }
  return true;
}

hv_planning_msgs__msg__Trajectory *
hv_planning_msgs__msg__Trajectory__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__Trajectory * msg = (hv_planning_msgs__msg__Trajectory *)allocator.allocate(sizeof(hv_planning_msgs__msg__Trajectory), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(hv_planning_msgs__msg__Trajectory));
  bool success = hv_planning_msgs__msg__Trajectory__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
hv_planning_msgs__msg__Trajectory__destroy(hv_planning_msgs__msg__Trajectory * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    hv_planning_msgs__msg__Trajectory__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
hv_planning_msgs__msg__Trajectory__Sequence__init(hv_planning_msgs__msg__Trajectory__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__Trajectory * data = NULL;

  if (size) {
    data = (hv_planning_msgs__msg__Trajectory *)allocator.zero_allocate(size, sizeof(hv_planning_msgs__msg__Trajectory), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = hv_planning_msgs__msg__Trajectory__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        hv_planning_msgs__msg__Trajectory__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
hv_planning_msgs__msg__Trajectory__Sequence__fini(hv_planning_msgs__msg__Trajectory__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      hv_planning_msgs__msg__Trajectory__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

hv_planning_msgs__msg__Trajectory__Sequence *
hv_planning_msgs__msg__Trajectory__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__Trajectory__Sequence * array = (hv_planning_msgs__msg__Trajectory__Sequence *)allocator.allocate(sizeof(hv_planning_msgs__msg__Trajectory__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = hv_planning_msgs__msg__Trajectory__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
hv_planning_msgs__msg__Trajectory__Sequence__destroy(hv_planning_msgs__msg__Trajectory__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    hv_planning_msgs__msg__Trajectory__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
hv_planning_msgs__msg__Trajectory__Sequence__are_equal(const hv_planning_msgs__msg__Trajectory__Sequence * lhs, const hv_planning_msgs__msg__Trajectory__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!hv_planning_msgs__msg__Trajectory__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
hv_planning_msgs__msg__Trajectory__Sequence__copy(
  const hv_planning_msgs__msg__Trajectory__Sequence * input,
  hv_planning_msgs__msg__Trajectory__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(hv_planning_msgs__msg__Trajectory);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    hv_planning_msgs__msg__Trajectory * data =
      (hv_planning_msgs__msg__Trajectory *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!hv_planning_msgs__msg__Trajectory__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          hv_planning_msgs__msg__Trajectory__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!hv_planning_msgs__msg__Trajectory__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
