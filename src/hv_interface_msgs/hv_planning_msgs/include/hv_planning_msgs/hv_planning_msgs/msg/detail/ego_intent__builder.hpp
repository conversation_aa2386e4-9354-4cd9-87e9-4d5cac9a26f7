// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__BUILDER_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hv_planning_msgs/msg/detail/ego_intent__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hv_planning_msgs
{

namespace msg
{

namespace builder
{

class Init_EgoIntent_longitudinal_index
{
public:
  explicit Init_EgoIntent_longitudinal_index(::hv_planning_msgs::msg::EgoIntent & msg)
  : msg_(msg)
  {}
  ::hv_planning_msgs::msg::EgoIntent longitudinal_index(::hv_planning_msgs::msg::EgoIntent::_longitudinal_index_type arg)
  {
    msg_.longitudinal_index = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hv_planning_msgs::msg::EgoIntent msg_;
};

class Init_EgoIntent_longitudinal_intens
{
public:
  explicit Init_EgoIntent_longitudinal_intens(::hv_planning_msgs::msg::EgoIntent & msg)
  : msg_(msg)
  {}
  Init_EgoIntent_longitudinal_index longitudinal_intens(::hv_planning_msgs::msg::EgoIntent::_longitudinal_intens_type arg)
  {
    msg_.longitudinal_intens = std::move(arg);
    return Init_EgoIntent_longitudinal_index(msg_);
  }

private:
  ::hv_planning_msgs::msg::EgoIntent msg_;
};

class Init_EgoIntent_lateral_index
{
public:
  explicit Init_EgoIntent_lateral_index(::hv_planning_msgs::msg::EgoIntent & msg)
  : msg_(msg)
  {}
  Init_EgoIntent_longitudinal_intens lateral_index(::hv_planning_msgs::msg::EgoIntent::_lateral_index_type arg)
  {
    msg_.lateral_index = std::move(arg);
    return Init_EgoIntent_longitudinal_intens(msg_);
  }

private:
  ::hv_planning_msgs::msg::EgoIntent msg_;
};

class Init_EgoIntent_lateral_intents
{
public:
  Init_EgoIntent_lateral_intents()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_EgoIntent_lateral_index lateral_intents(::hv_planning_msgs::msg::EgoIntent::_lateral_intents_type arg)
  {
    msg_.lateral_intents = std::move(arg);
    return Init_EgoIntent_lateral_index(msg_);
  }

private:
  ::hv_planning_msgs::msg::EgoIntent msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hv_planning_msgs::msg::EgoIntent>()
{
  return hv_planning_msgs::msg::builder::Init_EgoIntent_lateral_intents();
}

}  // namespace hv_planning_msgs

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__BUILDER_HPP_
