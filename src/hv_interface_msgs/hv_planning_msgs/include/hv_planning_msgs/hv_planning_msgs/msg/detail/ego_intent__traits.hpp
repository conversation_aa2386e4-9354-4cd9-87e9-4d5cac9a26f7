// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__TRAITS_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hv_planning_msgs/msg/detail/ego_intent__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace hv_planning_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const EgoIntent & msg,
  std::ostream & out)
{
  out << "{";
  // member: lateral_intents
  {
    if (msg.lateral_intents.size() == 0) {
      out << "lateral_intents: []";
    } else {
      out << "lateral_intents: [";
      size_t pending_items = msg.lateral_intents.size();
      for (auto item : msg.lateral_intents) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: lateral_index
  {
    if (msg.lateral_index.size() == 0) {
      out << "lateral_index: []";
    } else {
      out << "lateral_index: [";
      size_t pending_items = msg.lateral_index.size();
      for (auto item : msg.lateral_index) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: longitudinal_intens
  {
    if (msg.longitudinal_intens.size() == 0) {
      out << "longitudinal_intens: []";
    } else {
      out << "longitudinal_intens: [";
      size_t pending_items = msg.longitudinal_intens.size();
      for (auto item : msg.longitudinal_intens) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: longitudinal_index
  {
    if (msg.longitudinal_index.size() == 0) {
      out << "longitudinal_index: []";
    } else {
      out << "longitudinal_index: [";
      size_t pending_items = msg.longitudinal_index.size();
      for (auto item : msg.longitudinal_index) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const EgoIntent & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: lateral_intents
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.lateral_intents.size() == 0) {
      out << "lateral_intents: []\n";
    } else {
      out << "lateral_intents:\n";
      for (auto item : msg.lateral_intents) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: lateral_index
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.lateral_index.size() == 0) {
      out << "lateral_index: []\n";
    } else {
      out << "lateral_index:\n";
      for (auto item : msg.lateral_index) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: longitudinal_intens
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.longitudinal_intens.size() == 0) {
      out << "longitudinal_intens: []\n";
    } else {
      out << "longitudinal_intens:\n";
      for (auto item : msg.longitudinal_intens) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: longitudinal_index
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.longitudinal_index.size() == 0) {
      out << "longitudinal_index: []\n";
    } else {
      out << "longitudinal_index:\n";
      for (auto item : msg.longitudinal_index) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const EgoIntent & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hv_planning_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hv_planning_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hv_planning_msgs::msg::EgoIntent & msg,
  std::ostream & out, size_t indentation = 0)
{
  hv_planning_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hv_planning_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hv_planning_msgs::msg::EgoIntent & msg)
{
  return hv_planning_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hv_planning_msgs::msg::EgoIntent>()
{
  return "hv_planning_msgs::msg::EgoIntent";
}

template<>
inline const char * name<hv_planning_msgs::msg::EgoIntent>()
{
  return "hv_planning_msgs/msg/EgoIntent";
}

template<>
struct has_fixed_size<hv_planning_msgs::msg::EgoIntent>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<hv_planning_msgs::msg::EgoIntent>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<hv_planning_msgs::msg::EgoIntent>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__TRAITS_HPP_
