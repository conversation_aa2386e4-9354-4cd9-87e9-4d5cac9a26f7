// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "hv_planning_msgs/msg/detail/ego_intent__rosidl_typesupport_introspection_c.h"
#include "hv_planning_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "hv_planning_msgs/msg/detail/ego_intent__functions.h"
#include "hv_planning_msgs/msg/detail/ego_intent__struct.h"


// Include directives for member types
// Member `lateral_intents`
// Member `lateral_index`
// Member `longitudinal_intens`
// Member `longitudinal_index`
#include "rosidl_runtime_c/primitives_sequence_functions.h"

#ifdef __cplusplus
extern "C"
{
#endif

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  hv_planning_msgs__msg__EgoIntent__init(message_memory);
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_fini_function(void * message_memory)
{
  hv_planning_msgs__msg__EgoIntent__fini(message_memory);
}

size_t hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__lateral_intents(
  const void * untyped_member)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__lateral_intents(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__lateral_intents(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__lateral_intents(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint8_t * item =
    ((const uint8_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__lateral_intents(untyped_member, index));
  uint8_t * value =
    (uint8_t *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__lateral_intents(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint8_t * item =
    ((uint8_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__lateral_intents(untyped_member, index));
  const uint8_t * value =
    (const uint8_t *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__lateral_intents(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  rosidl_runtime_c__uint8__Sequence__fini(member);
  return rosidl_runtime_c__uint8__Sequence__init(member, size);
}

size_t hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__lateral_index(
  const void * untyped_member)
{
  const rosidl_runtime_c__uint32__Sequence * member =
    (const rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__lateral_index(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__uint32__Sequence * member =
    (const rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__lateral_index(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__uint32__Sequence * member =
    (rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__lateral_index(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint32_t * item =
    ((const uint32_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__lateral_index(untyped_member, index));
  uint32_t * value =
    (uint32_t *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__lateral_index(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint32_t * item =
    ((uint32_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__lateral_index(untyped_member, index));
  const uint32_t * value =
    (const uint32_t *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__lateral_index(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__uint32__Sequence * member =
    (rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  rosidl_runtime_c__uint32__Sequence__fini(member);
  return rosidl_runtime_c__uint32__Sequence__init(member, size);
}

size_t hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__longitudinal_intens(
  const void * untyped_member)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__longitudinal_intens(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__longitudinal_intens(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__longitudinal_intens(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint8_t * item =
    ((const uint8_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__longitudinal_intens(untyped_member, index));
  uint8_t * value =
    (uint8_t *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__longitudinal_intens(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint8_t * item =
    ((uint8_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__longitudinal_intens(untyped_member, index));
  const uint8_t * value =
    (const uint8_t *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__longitudinal_intens(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  rosidl_runtime_c__uint8__Sequence__fini(member);
  return rosidl_runtime_c__uint8__Sequence__init(member, size);
}

size_t hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__longitudinal_index(
  const void * untyped_member)
{
  const rosidl_runtime_c__uint32__Sequence * member =
    (const rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__longitudinal_index(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__uint32__Sequence * member =
    (const rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__longitudinal_index(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__uint32__Sequence * member =
    (rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__longitudinal_index(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint32_t * item =
    ((const uint32_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__longitudinal_index(untyped_member, index));
  uint32_t * value =
    (uint32_t *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__longitudinal_index(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint32_t * item =
    ((uint32_t *)
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__longitudinal_index(untyped_member, index));
  const uint32_t * value =
    (const uint32_t *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__longitudinal_index(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__uint32__Sequence * member =
    (rosidl_runtime_c__uint32__Sequence *)(untyped_member);
  rosidl_runtime_c__uint32__Sequence__fini(member);
  return rosidl_runtime_c__uint32__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_member_array[4] = {
  {
    "lateral_intents",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__EgoIntent, lateral_intents),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__lateral_intents,  // size() function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__lateral_intents,  // get_const(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__lateral_intents,  // get(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__lateral_intents,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__lateral_intents,  // assign(index, value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__lateral_intents  // resize(index) function pointer
  },
  {
    "lateral_index",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__EgoIntent, lateral_index),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__lateral_index,  // size() function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__lateral_index,  // get_const(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__lateral_index,  // get(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__lateral_index,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__lateral_index,  // assign(index, value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__lateral_index  // resize(index) function pointer
  },
  {
    "longitudinal_intens",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__EgoIntent, longitudinal_intens),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__longitudinal_intens,  // size() function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__longitudinal_intens,  // get_const(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__longitudinal_intens,  // get(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__longitudinal_intens,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__longitudinal_intens,  // assign(index, value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__longitudinal_intens  // resize(index) function pointer
  },
  {
    "longitudinal_index",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__EgoIntent, longitudinal_index),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__size_function__EgoIntent__longitudinal_index,  // size() function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_const_function__EgoIntent__longitudinal_index,  // get_const(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__get_function__EgoIntent__longitudinal_index,  // get(index) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__fetch_function__EgoIntent__longitudinal_index,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__assign_function__EgoIntent__longitudinal_index,  // assign(index, value) function pointer
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__resize_function__EgoIntent__longitudinal_index  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_members = {
  "hv_planning_msgs__msg",  // message namespace
  "EgoIntent",  // message name
  4,  // number of fields
  sizeof(hv_planning_msgs__msg__EgoIntent),
  hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_member_array,  // message members
  hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_init_function,  // function to initialize message memory (memory has to be allocated)
  hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_type_support_handle = {
  0,
  &hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_hv_planning_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, EgoIntent)() {
  if (!hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_type_support_handle.typesupport_identifier) {
    hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &hv_planning_msgs__msg__EgoIntent__rosidl_typesupport_introspection_c__EgoIntent_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
