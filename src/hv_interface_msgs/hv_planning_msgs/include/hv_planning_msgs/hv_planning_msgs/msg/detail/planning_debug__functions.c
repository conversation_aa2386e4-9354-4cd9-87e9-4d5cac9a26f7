// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from hv_planning_msgs:msg/PlanningDebug.idl
// generated code does not contain a copyright notice
#include "hv_planning_msgs/msg/detail/planning_debug__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "hv_common_msgs/msg/detail/header__functions.h"
// Member `data`
#include "rosidl_runtime_c/primitives_sequence_functions.h"

bool
hv_planning_msgs__msg__PlanningDebug__init(hv_planning_msgs__msg__PlanningDebug * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__init(&msg->header)) {
    hv_planning_msgs__msg__PlanningDebug__fini(msg);
    return false;
  }
  // data
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->data, 0)) {
    hv_planning_msgs__msg__PlanningDebug__fini(msg);
    return false;
  }
  return true;
}

void
hv_planning_msgs__msg__PlanningDebug__fini(hv_planning_msgs__msg__PlanningDebug * msg)
{
  if (!msg) {
    return;
  }
  // header
  hv_common_msgs__msg__Header__fini(&msg->header);
  // data
  rosidl_runtime_c__uint8__Sequence__fini(&msg->data);
}

bool
hv_planning_msgs__msg__PlanningDebug__are_equal(const hv_planning_msgs__msg__PlanningDebug * lhs, const hv_planning_msgs__msg__PlanningDebug * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // data
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->data), &(rhs->data)))
  {
    return false;
  }
  return true;
}

bool
hv_planning_msgs__msg__PlanningDebug__copy(
  const hv_planning_msgs__msg__PlanningDebug * input,
  hv_planning_msgs__msg__PlanningDebug * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // data
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->data), &(output->data)))
  {
    return false;
  }
  return true;
}

hv_planning_msgs__msg__PlanningDebug *
hv_planning_msgs__msg__PlanningDebug__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__PlanningDebug * msg = (hv_planning_msgs__msg__PlanningDebug *)allocator.allocate(sizeof(hv_planning_msgs__msg__PlanningDebug), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(hv_planning_msgs__msg__PlanningDebug));
  bool success = hv_planning_msgs__msg__PlanningDebug__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
hv_planning_msgs__msg__PlanningDebug__destroy(hv_planning_msgs__msg__PlanningDebug * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    hv_planning_msgs__msg__PlanningDebug__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
hv_planning_msgs__msg__PlanningDebug__Sequence__init(hv_planning_msgs__msg__PlanningDebug__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__PlanningDebug * data = NULL;

  if (size) {
    data = (hv_planning_msgs__msg__PlanningDebug *)allocator.zero_allocate(size, sizeof(hv_planning_msgs__msg__PlanningDebug), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = hv_planning_msgs__msg__PlanningDebug__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        hv_planning_msgs__msg__PlanningDebug__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
hv_planning_msgs__msg__PlanningDebug__Sequence__fini(hv_planning_msgs__msg__PlanningDebug__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      hv_planning_msgs__msg__PlanningDebug__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

hv_planning_msgs__msg__PlanningDebug__Sequence *
hv_planning_msgs__msg__PlanningDebug__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__PlanningDebug__Sequence * array = (hv_planning_msgs__msg__PlanningDebug__Sequence *)allocator.allocate(sizeof(hv_planning_msgs__msg__PlanningDebug__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = hv_planning_msgs__msg__PlanningDebug__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
hv_planning_msgs__msg__PlanningDebug__Sequence__destroy(hv_planning_msgs__msg__PlanningDebug__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    hv_planning_msgs__msg__PlanningDebug__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
hv_planning_msgs__msg__PlanningDebug__Sequence__are_equal(const hv_planning_msgs__msg__PlanningDebug__Sequence * lhs, const hv_planning_msgs__msg__PlanningDebug__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!hv_planning_msgs__msg__PlanningDebug__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
hv_planning_msgs__msg__PlanningDebug__Sequence__copy(
  const hv_planning_msgs__msg__PlanningDebug__Sequence * input,
  hv_planning_msgs__msg__PlanningDebug__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(hv_planning_msgs__msg__PlanningDebug);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    hv_planning_msgs__msg__PlanningDebug * data =
      (hv_planning_msgs__msg__PlanningDebug *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!hv_planning_msgs__msg__PlanningDebug__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          hv_planning_msgs__msg__PlanningDebug__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!hv_planning_msgs__msg__PlanningDebug__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
