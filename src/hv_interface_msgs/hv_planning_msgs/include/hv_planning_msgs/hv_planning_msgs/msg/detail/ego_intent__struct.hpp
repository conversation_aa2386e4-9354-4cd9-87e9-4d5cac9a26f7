// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__STRUCT_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__hv_planning_msgs__msg__EgoIntent __attribute__((deprecated))
#else
# define DEPRECATED__hv_planning_msgs__msg__EgoIntent __declspec(deprecated)
#endif

namespace hv_planning_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct EgoIntent_
{
  using Type = EgoIntent_<ContainerAllocator>;

  explicit EgoIntent_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_init;
  }

  explicit EgoIntent_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_init;
    (void)_alloc;
  }

  // field types and members
  using _lateral_intents_type =
    std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>>;
  _lateral_intents_type lateral_intents;
  using _lateral_index_type =
    std::vector<uint32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint32_t>>;
  _lateral_index_type lateral_index;
  using _longitudinal_intens_type =
    std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>>;
  _longitudinal_intens_type longitudinal_intens;
  using _longitudinal_index_type =
    std::vector<uint32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint32_t>>;
  _longitudinal_index_type longitudinal_index;

  // setters for named parameter idiom
  Type & set__lateral_intents(
    const std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> & _arg)
  {
    this->lateral_intents = _arg;
    return *this;
  }
  Type & set__lateral_index(
    const std::vector<uint32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint32_t>> & _arg)
  {
    this->lateral_index = _arg;
    return *this;
  }
  Type & set__longitudinal_intens(
    const std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> & _arg)
  {
    this->longitudinal_intens = _arg;
    return *this;
  }
  Type & set__longitudinal_index(
    const std::vector<uint32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint32_t>> & _arg)
  {
    this->longitudinal_index = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hv_planning_msgs::msg::EgoIntent_<ContainerAllocator> *;
  using ConstRawPtr =
    const hv_planning_msgs::msg::EgoIntent_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hv_planning_msgs::msg::EgoIntent_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hv_planning_msgs::msg::EgoIntent_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hv_planning_msgs__msg__EgoIntent
    std::shared_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hv_planning_msgs__msg__EgoIntent
    std::shared_ptr<hv_planning_msgs::msg::EgoIntent_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const EgoIntent_ & other) const
  {
    if (this->lateral_intents != other.lateral_intents) {
      return false;
    }
    if (this->lateral_index != other.lateral_index) {
      return false;
    }
    if (this->longitudinal_intens != other.longitudinal_intens) {
      return false;
    }
    if (this->longitudinal_index != other.longitudinal_index) {
      return false;
    }
    return true;
  }
  bool operator!=(const EgoIntent_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct EgoIntent_

// alias to use template instance with default allocator
using EgoIntent =
  hv_planning_msgs::msg::EgoIntent_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hv_planning_msgs

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__STRUCT_HPP_
