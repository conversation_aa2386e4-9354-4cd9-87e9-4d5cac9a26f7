// generated from rosidl_generator_c/resource/idl__functions.h.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__FUNCTIONS_H_
#define HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__FUNCTIONS_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stdlib.h>

#include "rosidl_runtime_c/visibility_control.h"
#include "hv_planning_msgs/msg/rosidl_generator_c__visibility_control.h"

#include "hv_planning_msgs/msg/detail/ego_intent__struct.h"

/// Initialize msg/EgoIntent message.
/**
 * If the init function is called twice for the same message without
 * calling fini inbetween previously allocated memory will be leaked.
 * \param[in,out] msg The previously allocated message pointer.
 * Fields without a default value will not be initialized by this function.
 * You might want to call memset(msg, 0, sizeof(
 * hv_planning_msgs__msg__EgoIntent
 * )) before or use
 * hv_planning_msgs__msg__EgoIntent__create()
 * to allocate and initialize the message.
 * \return true if initialization was successful, otherwise false
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
bool
hv_planning_msgs__msg__EgoIntent__init(hv_planning_msgs__msg__EgoIntent * msg);

/// Finalize msg/EgoIntent message.
/**
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
void
hv_planning_msgs__msg__EgoIntent__fini(hv_planning_msgs__msg__EgoIntent * msg);

/// Create msg/EgoIntent message.
/**
 * It allocates the memory for the message, sets the memory to zero, and
 * calls
 * hv_planning_msgs__msg__EgoIntent__init().
 * \return The pointer to the initialized message if successful,
 * otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
hv_planning_msgs__msg__EgoIntent *
hv_planning_msgs__msg__EgoIntent__create();

/// Destroy msg/EgoIntent message.
/**
 * It calls
 * hv_planning_msgs__msg__EgoIntent__fini()
 * and frees the memory of the message.
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
void
hv_planning_msgs__msg__EgoIntent__destroy(hv_planning_msgs__msg__EgoIntent * msg);

/// Check for msg/EgoIntent message equality.
/**
 * \param[in] lhs The message on the left hand size of the equality operator.
 * \param[in] rhs The message on the right hand size of the equality operator.
 * \return true if messages are equal, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
bool
hv_planning_msgs__msg__EgoIntent__are_equal(const hv_planning_msgs__msg__EgoIntent * lhs, const hv_planning_msgs__msg__EgoIntent * rhs);

/// Copy a msg/EgoIntent message.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source message pointer.
 * \param[out] output The target message pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer is null
 *   or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
bool
hv_planning_msgs__msg__EgoIntent__copy(
  const hv_planning_msgs__msg__EgoIntent * input,
  hv_planning_msgs__msg__EgoIntent * output);

/// Initialize array of msg/EgoIntent messages.
/**
 * It allocates the memory for the number of elements and calls
 * hv_planning_msgs__msg__EgoIntent__init()
 * for each element of the array.
 * \param[in,out] array The allocated array pointer.
 * \param[in] size The size / capacity of the array.
 * \return true if initialization was successful, otherwise false
 * If the array pointer is valid and the size is zero it is guaranteed
 # to return true.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
bool
hv_planning_msgs__msg__EgoIntent__Sequence__init(hv_planning_msgs__msg__EgoIntent__Sequence * array, size_t size);

/// Finalize array of msg/EgoIntent messages.
/**
 * It calls
 * hv_planning_msgs__msg__EgoIntent__fini()
 * for each element of the array and frees the memory for the number of
 * elements.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
void
hv_planning_msgs__msg__EgoIntent__Sequence__fini(hv_planning_msgs__msg__EgoIntent__Sequence * array);

/// Create array of msg/EgoIntent messages.
/**
 * It allocates the memory for the array and calls
 * hv_planning_msgs__msg__EgoIntent__Sequence__init().
 * \param[in] size The size / capacity of the array.
 * \return The pointer to the initialized array if successful, otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
hv_planning_msgs__msg__EgoIntent__Sequence *
hv_planning_msgs__msg__EgoIntent__Sequence__create(size_t size);

/// Destroy array of msg/EgoIntent messages.
/**
 * It calls
 * hv_planning_msgs__msg__EgoIntent__Sequence__fini()
 * on the array,
 * and frees the memory of the array.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
void
hv_planning_msgs__msg__EgoIntent__Sequence__destroy(hv_planning_msgs__msg__EgoIntent__Sequence * array);

/// Check for msg/EgoIntent message array equality.
/**
 * \param[in] lhs The message array on the left hand size of the equality operator.
 * \param[in] rhs The message array on the right hand size of the equality operator.
 * \return true if message arrays are equal in size and content, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
bool
hv_planning_msgs__msg__EgoIntent__Sequence__are_equal(const hv_planning_msgs__msg__EgoIntent__Sequence * lhs, const hv_planning_msgs__msg__EgoIntent__Sequence * rhs);

/// Copy an array of msg/EgoIntent messages.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source array pointer.
 * \param[out] output The target array pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer
 *   is null or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
bool
hv_planning_msgs__msg__EgoIntent__Sequence__copy(
  const hv_planning_msgs__msg__EgoIntent__Sequence * input,
  hv_planning_msgs__msg__EgoIntent__Sequence * output);

#ifdef __cplusplus
}
#endif

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__FUNCTIONS_H_
