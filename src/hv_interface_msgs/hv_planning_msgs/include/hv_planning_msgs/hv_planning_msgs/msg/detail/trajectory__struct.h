// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hv_planning_msgs:msg/Trajectory.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__STRUCT_H_
#define HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__struct.h"
// Member 'planning_header'
// Member 'routing_header'
#include "hv_planning_msgs/msg/detail/planning_header__struct.h"
// Member 'trajectory_point'
#include "hv_planning_msgs/msg/detail/trajectory_point__struct.h"
// Member 'estop'
#include "hv_planning_msgs/msg/detail/estop__struct.h"
// Member 'path_point'
#include "hv_planning_msgs/msg/detail/path_point__struct.h"
// Member 'replan_reason'
// Member 'lane_id'
// Member 'target_lane_id'
#include "rosidl_runtime_c/string.h"
// Member 'decision'
#include "hv_planning_msgs/msg/detail/decision_result__struct.h"
// Member 'latency_stats'
#include "hv_planning_msgs/msg/detail/latency_stats__struct.h"
// Member 'engage_advice'
#include "hv_planning_msgs/msg/detail/engage_advice__struct.h"
// Member 'critical_region'
#include "hv_planning_msgs/msg/detail/critical_region__struct.h"
// Member 'ego_intent'
#include "hv_planning_msgs/msg/detail/ego_intent__struct.h"

/// Struct defined in msg/Trajectory in the package hv_planning_msgs.
/**
  * Generated from /home/<USER>/Workspace/1_Repo/0_Baseline/hv_interface/interface/planning/trajectory.h
  * Original struct: Trajectory
 */
typedef struct hv_planning_msgs__msg__Trajectory
{
  hv_common_msgs__msg__Header header;
  hv_planning_msgs__msg__PlanningHeader planning_header;
  /// default: 0.0
  double total_path_length;
  /// default: 0.0
  double total_path_time;
  hv_planning_msgs__msg__TrajectoryPoint__Sequence trajectory_point;
  hv_planning_msgs__msg__Estop estop;
  hv_planning_msgs__msg__PathPoint__Sequence path_point;
  /// default: false
  bool is_replan;
  rosidl_runtime_c__String replan_reason;
  /// default: false
  bool is_uturn;
  int8_t gear;
  hv_planning_msgs__msg__DecisionResult decision;
  hv_planning_msgs__msg__LatencyStats latency_stats;
  hv_planning_msgs__msg__PlanningHeader routing_header;
  int8_t right_of_way_status;
  rosidl_runtime_c__String__Sequence lane_id;
  hv_planning_msgs__msg__EngageAdvice engage_advice;
  hv_planning_msgs__msg__CriticalRegion critical_region;
  int8_t trajectory_type;
  rosidl_runtime_c__String__Sequence target_lane_id;
  hv_planning_msgs__msg__EgoIntent ego_intent;
} hv_planning_msgs__msg__Trajectory;

// Struct for a sequence of hv_planning_msgs__msg__Trajectory.
typedef struct hv_planning_msgs__msg__Trajectory__Sequence
{
  hv_planning_msgs__msg__Trajectory * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hv_planning_msgs__msg__Trajectory__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__STRUCT_H_
