// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hv_planning_msgs:msg/PlanningDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__STRUCT_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__hv_planning_msgs__msg__PlanningDebug __attribute__((deprecated))
#else
# define DEPRECATED__hv_planning_msgs__msg__PlanningDebug __declspec(deprecated)
#endif

namespace hv_planning_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct PlanningDebug_
{
  using Type = PlanningDebug_<ContainerAllocator>;

  explicit PlanningDebug_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    (void)_init;
  }

  explicit PlanningDebug_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _header_type =
    hv_common_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _data_type =
    std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>>;
  _data_type data;

  // setters for named parameter idiom
  Type & set__header(
    const hv_common_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__data(
    const std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> & _arg)
  {
    this->data = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator> *;
  using ConstRawPtr =
    const hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hv_planning_msgs__msg__PlanningDebug
    std::shared_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hv_planning_msgs__msg__PlanningDebug
    std::shared_ptr<hv_planning_msgs::msg::PlanningDebug_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const PlanningDebug_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->data != other.data) {
      return false;
    }
    return true;
  }
  bool operator!=(const PlanningDebug_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct PlanningDebug_

// alias to use template instance with default allocator
using PlanningDebug =
  hv_planning_msgs::msg::PlanningDebug_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hv_planning_msgs

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__STRUCT_HPP_
