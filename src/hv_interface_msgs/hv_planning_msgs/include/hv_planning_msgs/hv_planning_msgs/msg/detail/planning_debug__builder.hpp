// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hv_planning_msgs:msg/PlanningDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__BUILDER_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hv_planning_msgs/msg/detail/planning_debug__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hv_planning_msgs
{

namespace msg
{

namespace builder
{

class Init_PlanningDebug_data
{
public:
  explicit Init_PlanningDebug_data(::hv_planning_msgs::msg::PlanningDebug & msg)
  : msg_(msg)
  {}
  ::hv_planning_msgs::msg::PlanningDebug data(::hv_planning_msgs::msg::PlanningDebug::_data_type arg)
  {
    msg_.data = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hv_planning_msgs::msg::PlanningDebug msg_;
};

class Init_PlanningDebug_header
{
public:
  Init_PlanningDebug_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_PlanningDebug_data header(::hv_planning_msgs::msg::PlanningDebug::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_PlanningDebug_data(msg_);
  }

private:
  ::hv_planning_msgs::msg::PlanningDebug msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hv_planning_msgs::msg::PlanningDebug>()
{
  return hv_planning_msgs::msg::builder::Init_PlanningDebug_header();
}

}  // namespace hv_planning_msgs

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__BUILDER_HPP_
