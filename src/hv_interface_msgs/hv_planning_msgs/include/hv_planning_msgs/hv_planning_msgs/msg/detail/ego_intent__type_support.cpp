// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "hv_planning_msgs/msg/detail/ego_intent__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace hv_planning_msgs
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void EgoIntent_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) hv_planning_msgs::msg::EgoIntent(_init);
}

void EgoIntent_fini_function(void * message_memory)
{
  auto typed_message = static_cast<hv_planning_msgs::msg::EgoIntent *>(message_memory);
  typed_message->~EgoIntent();
}

size_t size_function__EgoIntent__lateral_intents(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<uint8_t> *>(untyped_member);
  return member->size();
}

const void * get_const_function__EgoIntent__lateral_intents(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<uint8_t> *>(untyped_member);
  return &member[index];
}

void * get_function__EgoIntent__lateral_intents(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<uint8_t> *>(untyped_member);
  return &member[index];
}

void fetch_function__EgoIntent__lateral_intents(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const uint8_t *>(
    get_const_function__EgoIntent__lateral_intents(untyped_member, index));
  auto & value = *reinterpret_cast<uint8_t *>(untyped_value);
  value = item;
}

void assign_function__EgoIntent__lateral_intents(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<uint8_t *>(
    get_function__EgoIntent__lateral_intents(untyped_member, index));
  const auto & value = *reinterpret_cast<const uint8_t *>(untyped_value);
  item = value;
}

void resize_function__EgoIntent__lateral_intents(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<uint8_t> *>(untyped_member);
  member->resize(size);
}

size_t size_function__EgoIntent__lateral_index(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<uint32_t> *>(untyped_member);
  return member->size();
}

const void * get_const_function__EgoIntent__lateral_index(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<uint32_t> *>(untyped_member);
  return &member[index];
}

void * get_function__EgoIntent__lateral_index(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<uint32_t> *>(untyped_member);
  return &member[index];
}

void fetch_function__EgoIntent__lateral_index(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const uint32_t *>(
    get_const_function__EgoIntent__lateral_index(untyped_member, index));
  auto & value = *reinterpret_cast<uint32_t *>(untyped_value);
  value = item;
}

void assign_function__EgoIntent__lateral_index(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<uint32_t *>(
    get_function__EgoIntent__lateral_index(untyped_member, index));
  const auto & value = *reinterpret_cast<const uint32_t *>(untyped_value);
  item = value;
}

void resize_function__EgoIntent__lateral_index(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<uint32_t> *>(untyped_member);
  member->resize(size);
}

size_t size_function__EgoIntent__longitudinal_intens(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<uint8_t> *>(untyped_member);
  return member->size();
}

const void * get_const_function__EgoIntent__longitudinal_intens(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<uint8_t> *>(untyped_member);
  return &member[index];
}

void * get_function__EgoIntent__longitudinal_intens(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<uint8_t> *>(untyped_member);
  return &member[index];
}

void fetch_function__EgoIntent__longitudinal_intens(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const uint8_t *>(
    get_const_function__EgoIntent__longitudinal_intens(untyped_member, index));
  auto & value = *reinterpret_cast<uint8_t *>(untyped_value);
  value = item;
}

void assign_function__EgoIntent__longitudinal_intens(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<uint8_t *>(
    get_function__EgoIntent__longitudinal_intens(untyped_member, index));
  const auto & value = *reinterpret_cast<const uint8_t *>(untyped_value);
  item = value;
}

void resize_function__EgoIntent__longitudinal_intens(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<uint8_t> *>(untyped_member);
  member->resize(size);
}

size_t size_function__EgoIntent__longitudinal_index(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<uint32_t> *>(untyped_member);
  return member->size();
}

const void * get_const_function__EgoIntent__longitudinal_index(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<uint32_t> *>(untyped_member);
  return &member[index];
}

void * get_function__EgoIntent__longitudinal_index(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<uint32_t> *>(untyped_member);
  return &member[index];
}

void fetch_function__EgoIntent__longitudinal_index(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const uint32_t *>(
    get_const_function__EgoIntent__longitudinal_index(untyped_member, index));
  auto & value = *reinterpret_cast<uint32_t *>(untyped_value);
  value = item;
}

void assign_function__EgoIntent__longitudinal_index(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<uint32_t *>(
    get_function__EgoIntent__longitudinal_index(untyped_member, index));
  const auto & value = *reinterpret_cast<const uint32_t *>(untyped_value);
  item = value;
}

void resize_function__EgoIntent__longitudinal_index(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<uint32_t> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember EgoIntent_message_member_array[4] = {
  {
    "lateral_intents",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs::msg::EgoIntent, lateral_intents),  // bytes offset in struct
    nullptr,  // default value
    size_function__EgoIntent__lateral_intents,  // size() function pointer
    get_const_function__EgoIntent__lateral_intents,  // get_const(index) function pointer
    get_function__EgoIntent__lateral_intents,  // get(index) function pointer
    fetch_function__EgoIntent__lateral_intents,  // fetch(index, &value) function pointer
    assign_function__EgoIntent__lateral_intents,  // assign(index, value) function pointer
    resize_function__EgoIntent__lateral_intents  // resize(index) function pointer
  },
  {
    "lateral_index",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs::msg::EgoIntent, lateral_index),  // bytes offset in struct
    nullptr,  // default value
    size_function__EgoIntent__lateral_index,  // size() function pointer
    get_const_function__EgoIntent__lateral_index,  // get_const(index) function pointer
    get_function__EgoIntent__lateral_index,  // get(index) function pointer
    fetch_function__EgoIntent__lateral_index,  // fetch(index, &value) function pointer
    assign_function__EgoIntent__lateral_index,  // assign(index, value) function pointer
    resize_function__EgoIntent__lateral_index  // resize(index) function pointer
  },
  {
    "longitudinal_intens",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs::msg::EgoIntent, longitudinal_intens),  // bytes offset in struct
    nullptr,  // default value
    size_function__EgoIntent__longitudinal_intens,  // size() function pointer
    get_const_function__EgoIntent__longitudinal_intens,  // get_const(index) function pointer
    get_function__EgoIntent__longitudinal_intens,  // get(index) function pointer
    fetch_function__EgoIntent__longitudinal_intens,  // fetch(index, &value) function pointer
    assign_function__EgoIntent__longitudinal_intens,  // assign(index, value) function pointer
    resize_function__EgoIntent__longitudinal_intens  // resize(index) function pointer
  },
  {
    "longitudinal_index",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs::msg::EgoIntent, longitudinal_index),  // bytes offset in struct
    nullptr,  // default value
    size_function__EgoIntent__longitudinal_index,  // size() function pointer
    get_const_function__EgoIntent__longitudinal_index,  // get_const(index) function pointer
    get_function__EgoIntent__longitudinal_index,  // get(index) function pointer
    fetch_function__EgoIntent__longitudinal_index,  // fetch(index, &value) function pointer
    assign_function__EgoIntent__longitudinal_index,  // assign(index, value) function pointer
    resize_function__EgoIntent__longitudinal_index  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers EgoIntent_message_members = {
  "hv_planning_msgs::msg",  // message namespace
  "EgoIntent",  // message name
  4,  // number of fields
  sizeof(hv_planning_msgs::msg::EgoIntent),
  EgoIntent_message_member_array,  // message members
  EgoIntent_init_function,  // function to initialize message memory (memory has to be allocated)
  EgoIntent_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t EgoIntent_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &EgoIntent_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace hv_planning_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<hv_planning_msgs::msg::EgoIntent>()
{
  return &::hv_planning_msgs::msg::rosidl_typesupport_introspection_cpp::EgoIntent_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, hv_planning_msgs, msg, EgoIntent)() {
  return &::hv_planning_msgs::msg::rosidl_typesupport_introspection_cpp::EgoIntent_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
