// generated from rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice
#ifndef HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
#define HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_


#include <stddef.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "hv_planning_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_hv_planning_msgs
size_t get_serialized_size_hv_planning_msgs__msg__EgoIntent(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_hv_planning_msgs
size_t max_serialized_size_hv_planning_msgs__msg__EgoIntent(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_hv_planning_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, hv_planning_msgs, msg, EgoIntent)();

#ifdef __cplusplus
}
#endif

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__EGO_INTENT__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
