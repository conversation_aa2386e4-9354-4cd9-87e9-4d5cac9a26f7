// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hv_planning_msgs:msg/Trajectory.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__STRUCT_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__struct.hpp"
// Member 'planning_header'
// Member 'routing_header'
#include "hv_planning_msgs/msg/detail/planning_header__struct.hpp"
// Member 'trajectory_point'
#include "hv_planning_msgs/msg/detail/trajectory_point__struct.hpp"
// Member 'estop'
#include "hv_planning_msgs/msg/detail/estop__struct.hpp"
// Member 'path_point'
#include "hv_planning_msgs/msg/detail/path_point__struct.hpp"
// Member 'decision'
#include "hv_planning_msgs/msg/detail/decision_result__struct.hpp"
// Member 'latency_stats'
#include "hv_planning_msgs/msg/detail/latency_stats__struct.hpp"
// Member 'engage_advice'
#include "hv_planning_msgs/msg/detail/engage_advice__struct.hpp"
// Member 'critical_region'
#include "hv_planning_msgs/msg/detail/critical_region__struct.hpp"
// Member 'ego_intent'
#include "hv_planning_msgs/msg/detail/ego_intent__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__hv_planning_msgs__msg__Trajectory __attribute__((deprecated))
#else
# define DEPRECATED__hv_planning_msgs__msg__Trajectory __declspec(deprecated)
#endif

namespace hv_planning_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Trajectory_
{
  using Type = Trajectory_<ContainerAllocator>;

  explicit Trajectory_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init),
    planning_header(_init),
    estop(_init),
    decision(_init),
    latency_stats(_init),
    routing_header(_init),
    engage_advice(_init),
    critical_region(_init),
    ego_intent(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->total_path_length = 0.0;
      this->total_path_time = 0.0;
      this->is_replan = false;
      this->replan_reason = "";
      this->is_uturn = false;
      this->gear = 0;
      this->right_of_way_status = 0;
      this->trajectory_type = 0;
    }
  }

  explicit Trajectory_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init),
    planning_header(_alloc, _init),
    estop(_alloc, _init),
    replan_reason(_alloc),
    decision(_alloc, _init),
    latency_stats(_alloc, _init),
    routing_header(_alloc, _init),
    engage_advice(_alloc, _init),
    critical_region(_alloc, _init),
    ego_intent(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->total_path_length = 0.0;
      this->total_path_time = 0.0;
      this->is_replan = false;
      this->replan_reason = "";
      this->is_uturn = false;
      this->gear = 0;
      this->right_of_way_status = 0;
      this->trajectory_type = 0;
    }
  }

  // field types and members
  using _header_type =
    hv_common_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _planning_header_type =
    hv_planning_msgs::msg::PlanningHeader_<ContainerAllocator>;
  _planning_header_type planning_header;
  using _total_path_length_type =
    double;
  _total_path_length_type total_path_length;
  using _total_path_time_type =
    double;
  _total_path_time_type total_path_time;
  using _trajectory_point_type =
    std::vector<hv_planning_msgs::msg::TrajectoryPoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<hv_planning_msgs::msg::TrajectoryPoint_<ContainerAllocator>>>;
  _trajectory_point_type trajectory_point;
  using _estop_type =
    hv_planning_msgs::msg::Estop_<ContainerAllocator>;
  _estop_type estop;
  using _path_point_type =
    std::vector<hv_planning_msgs::msg::PathPoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<hv_planning_msgs::msg::PathPoint_<ContainerAllocator>>>;
  _path_point_type path_point;
  using _is_replan_type =
    bool;
  _is_replan_type is_replan;
  using _replan_reason_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _replan_reason_type replan_reason;
  using _is_uturn_type =
    bool;
  _is_uturn_type is_uturn;
  using _gear_type =
    int8_t;
  _gear_type gear;
  using _decision_type =
    hv_planning_msgs::msg::DecisionResult_<ContainerAllocator>;
  _decision_type decision;
  using _latency_stats_type =
    hv_planning_msgs::msg::LatencyStats_<ContainerAllocator>;
  _latency_stats_type latency_stats;
  using _routing_header_type =
    hv_planning_msgs::msg::PlanningHeader_<ContainerAllocator>;
  _routing_header_type routing_header;
  using _right_of_way_status_type =
    int8_t;
  _right_of_way_status_type right_of_way_status;
  using _lane_id_type =
    std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>>;
  _lane_id_type lane_id;
  using _engage_advice_type =
    hv_planning_msgs::msg::EngageAdvice_<ContainerAllocator>;
  _engage_advice_type engage_advice;
  using _critical_region_type =
    hv_planning_msgs::msg::CriticalRegion_<ContainerAllocator>;
  _critical_region_type critical_region;
  using _trajectory_type_type =
    int8_t;
  _trajectory_type_type trajectory_type;
  using _target_lane_id_type =
    std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>>;
  _target_lane_id_type target_lane_id;
  using _ego_intent_type =
    hv_planning_msgs::msg::EgoIntent_<ContainerAllocator>;
  _ego_intent_type ego_intent;

  // setters for named parameter idiom
  Type & set__header(
    const hv_common_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__planning_header(
    const hv_planning_msgs::msg::PlanningHeader_<ContainerAllocator> & _arg)
  {
    this->planning_header = _arg;
    return *this;
  }
  Type & set__total_path_length(
    const double & _arg)
  {
    this->total_path_length = _arg;
    return *this;
  }
  Type & set__total_path_time(
    const double & _arg)
  {
    this->total_path_time = _arg;
    return *this;
  }
  Type & set__trajectory_point(
    const std::vector<hv_planning_msgs::msg::TrajectoryPoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<hv_planning_msgs::msg::TrajectoryPoint_<ContainerAllocator>>> & _arg)
  {
    this->trajectory_point = _arg;
    return *this;
  }
  Type & set__estop(
    const hv_planning_msgs::msg::Estop_<ContainerAllocator> & _arg)
  {
    this->estop = _arg;
    return *this;
  }
  Type & set__path_point(
    const std::vector<hv_planning_msgs::msg::PathPoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<hv_planning_msgs::msg::PathPoint_<ContainerAllocator>>> & _arg)
  {
    this->path_point = _arg;
    return *this;
  }
  Type & set__is_replan(
    const bool & _arg)
  {
    this->is_replan = _arg;
    return *this;
  }
  Type & set__replan_reason(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->replan_reason = _arg;
    return *this;
  }
  Type & set__is_uturn(
    const bool & _arg)
  {
    this->is_uturn = _arg;
    return *this;
  }
  Type & set__gear(
    const int8_t & _arg)
  {
    this->gear = _arg;
    return *this;
  }
  Type & set__decision(
    const hv_planning_msgs::msg::DecisionResult_<ContainerAllocator> & _arg)
  {
    this->decision = _arg;
    return *this;
  }
  Type & set__latency_stats(
    const hv_planning_msgs::msg::LatencyStats_<ContainerAllocator> & _arg)
  {
    this->latency_stats = _arg;
    return *this;
  }
  Type & set__routing_header(
    const hv_planning_msgs::msg::PlanningHeader_<ContainerAllocator> & _arg)
  {
    this->routing_header = _arg;
    return *this;
  }
  Type & set__right_of_way_status(
    const int8_t & _arg)
  {
    this->right_of_way_status = _arg;
    return *this;
  }
  Type & set__lane_id(
    const std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> & _arg)
  {
    this->lane_id = _arg;
    return *this;
  }
  Type & set__engage_advice(
    const hv_planning_msgs::msg::EngageAdvice_<ContainerAllocator> & _arg)
  {
    this->engage_advice = _arg;
    return *this;
  }
  Type & set__critical_region(
    const hv_planning_msgs::msg::CriticalRegion_<ContainerAllocator> & _arg)
  {
    this->critical_region = _arg;
    return *this;
  }
  Type & set__trajectory_type(
    const int8_t & _arg)
  {
    this->trajectory_type = _arg;
    return *this;
  }
  Type & set__target_lane_id(
    const std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> & _arg)
  {
    this->target_lane_id = _arg;
    return *this;
  }
  Type & set__ego_intent(
    const hv_planning_msgs::msg::EgoIntent_<ContainerAllocator> & _arg)
  {
    this->ego_intent = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hv_planning_msgs::msg::Trajectory_<ContainerAllocator> *;
  using ConstRawPtr =
    const hv_planning_msgs::msg::Trajectory_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hv_planning_msgs::msg::Trajectory_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hv_planning_msgs::msg::Trajectory_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hv_planning_msgs__msg__Trajectory
    std::shared_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hv_planning_msgs__msg__Trajectory
    std::shared_ptr<hv_planning_msgs::msg::Trajectory_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Trajectory_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->planning_header != other.planning_header) {
      return false;
    }
    if (this->total_path_length != other.total_path_length) {
      return false;
    }
    if (this->total_path_time != other.total_path_time) {
      return false;
    }
    if (this->trajectory_point != other.trajectory_point) {
      return false;
    }
    if (this->estop != other.estop) {
      return false;
    }
    if (this->path_point != other.path_point) {
      return false;
    }
    if (this->is_replan != other.is_replan) {
      return false;
    }
    if (this->replan_reason != other.replan_reason) {
      return false;
    }
    if (this->is_uturn != other.is_uturn) {
      return false;
    }
    if (this->gear != other.gear) {
      return false;
    }
    if (this->decision != other.decision) {
      return false;
    }
    if (this->latency_stats != other.latency_stats) {
      return false;
    }
    if (this->routing_header != other.routing_header) {
      return false;
    }
    if (this->right_of_way_status != other.right_of_way_status) {
      return false;
    }
    if (this->lane_id != other.lane_id) {
      return false;
    }
    if (this->engage_advice != other.engage_advice) {
      return false;
    }
    if (this->critical_region != other.critical_region) {
      return false;
    }
    if (this->trajectory_type != other.trajectory_type) {
      return false;
    }
    if (this->target_lane_id != other.target_lane_id) {
      return false;
    }
    if (this->ego_intent != other.ego_intent) {
      return false;
    }
    return true;
  }
  bool operator!=(const Trajectory_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Trajectory_

// alias to use template instance with default allocator
using Trajectory =
  hv_planning_msgs::msg::Trajectory_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hv_planning_msgs

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__STRUCT_HPP_
