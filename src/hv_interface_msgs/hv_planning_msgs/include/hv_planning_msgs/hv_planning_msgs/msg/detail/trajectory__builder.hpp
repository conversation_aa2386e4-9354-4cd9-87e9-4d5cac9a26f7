// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hv_planning_msgs:msg/Trajectory.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__BUILDER_HPP_
#define HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hv_planning_msgs/msg/detail/trajectory__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hv_planning_msgs
{

namespace msg
{

namespace builder
{

class Init_Trajectory_ego_intent
{
public:
  explicit Init_Trajectory_ego_intent(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  ::hv_planning_msgs::msg::Trajectory ego_intent(::hv_planning_msgs::msg::Trajectory::_ego_intent_type arg)
  {
    msg_.ego_intent = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_target_lane_id
{
public:
  explicit Init_Trajectory_target_lane_id(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_ego_intent target_lane_id(::hv_planning_msgs::msg::Trajectory::_target_lane_id_type arg)
  {
    msg_.target_lane_id = std::move(arg);
    return Init_Trajectory_ego_intent(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_trajectory_type
{
public:
  explicit Init_Trajectory_trajectory_type(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_target_lane_id trajectory_type(::hv_planning_msgs::msg::Trajectory::_trajectory_type_type arg)
  {
    msg_.trajectory_type = std::move(arg);
    return Init_Trajectory_target_lane_id(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_critical_region
{
public:
  explicit Init_Trajectory_critical_region(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_trajectory_type critical_region(::hv_planning_msgs::msg::Trajectory::_critical_region_type arg)
  {
    msg_.critical_region = std::move(arg);
    return Init_Trajectory_trajectory_type(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_engage_advice
{
public:
  explicit Init_Trajectory_engage_advice(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_critical_region engage_advice(::hv_planning_msgs::msg::Trajectory::_engage_advice_type arg)
  {
    msg_.engage_advice = std::move(arg);
    return Init_Trajectory_critical_region(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_lane_id
{
public:
  explicit Init_Trajectory_lane_id(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_engage_advice lane_id(::hv_planning_msgs::msg::Trajectory::_lane_id_type arg)
  {
    msg_.lane_id = std::move(arg);
    return Init_Trajectory_engage_advice(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_right_of_way_status
{
public:
  explicit Init_Trajectory_right_of_way_status(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_lane_id right_of_way_status(::hv_planning_msgs::msg::Trajectory::_right_of_way_status_type arg)
  {
    msg_.right_of_way_status = std::move(arg);
    return Init_Trajectory_lane_id(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_routing_header
{
public:
  explicit Init_Trajectory_routing_header(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_right_of_way_status routing_header(::hv_planning_msgs::msg::Trajectory::_routing_header_type arg)
  {
    msg_.routing_header = std::move(arg);
    return Init_Trajectory_right_of_way_status(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_latency_stats
{
public:
  explicit Init_Trajectory_latency_stats(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_routing_header latency_stats(::hv_planning_msgs::msg::Trajectory::_latency_stats_type arg)
  {
    msg_.latency_stats = std::move(arg);
    return Init_Trajectory_routing_header(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_decision
{
public:
  explicit Init_Trajectory_decision(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_latency_stats decision(::hv_planning_msgs::msg::Trajectory::_decision_type arg)
  {
    msg_.decision = std::move(arg);
    return Init_Trajectory_latency_stats(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_gear
{
public:
  explicit Init_Trajectory_gear(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_decision gear(::hv_planning_msgs::msg::Trajectory::_gear_type arg)
  {
    msg_.gear = std::move(arg);
    return Init_Trajectory_decision(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_is_uturn
{
public:
  explicit Init_Trajectory_is_uturn(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_gear is_uturn(::hv_planning_msgs::msg::Trajectory::_is_uturn_type arg)
  {
    msg_.is_uturn = std::move(arg);
    return Init_Trajectory_gear(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_replan_reason
{
public:
  explicit Init_Trajectory_replan_reason(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_is_uturn replan_reason(::hv_planning_msgs::msg::Trajectory::_replan_reason_type arg)
  {
    msg_.replan_reason = std::move(arg);
    return Init_Trajectory_is_uturn(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_is_replan
{
public:
  explicit Init_Trajectory_is_replan(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_replan_reason is_replan(::hv_planning_msgs::msg::Trajectory::_is_replan_type arg)
  {
    msg_.is_replan = std::move(arg);
    return Init_Trajectory_replan_reason(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_path_point
{
public:
  explicit Init_Trajectory_path_point(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_is_replan path_point(::hv_planning_msgs::msg::Trajectory::_path_point_type arg)
  {
    msg_.path_point = std::move(arg);
    return Init_Trajectory_is_replan(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_estop
{
public:
  explicit Init_Trajectory_estop(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_path_point estop(::hv_planning_msgs::msg::Trajectory::_estop_type arg)
  {
    msg_.estop = std::move(arg);
    return Init_Trajectory_path_point(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_trajectory_point
{
public:
  explicit Init_Trajectory_trajectory_point(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_estop trajectory_point(::hv_planning_msgs::msg::Trajectory::_trajectory_point_type arg)
  {
    msg_.trajectory_point = std::move(arg);
    return Init_Trajectory_estop(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_total_path_time
{
public:
  explicit Init_Trajectory_total_path_time(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_trajectory_point total_path_time(::hv_planning_msgs::msg::Trajectory::_total_path_time_type arg)
  {
    msg_.total_path_time = std::move(arg);
    return Init_Trajectory_trajectory_point(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_total_path_length
{
public:
  explicit Init_Trajectory_total_path_length(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_total_path_time total_path_length(::hv_planning_msgs::msg::Trajectory::_total_path_length_type arg)
  {
    msg_.total_path_length = std::move(arg);
    return Init_Trajectory_total_path_time(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_planning_header
{
public:
  explicit Init_Trajectory_planning_header(::hv_planning_msgs::msg::Trajectory & msg)
  : msg_(msg)
  {}
  Init_Trajectory_total_path_length planning_header(::hv_planning_msgs::msg::Trajectory::_planning_header_type arg)
  {
    msg_.planning_header = std::move(arg);
    return Init_Trajectory_total_path_length(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

class Init_Trajectory_header
{
public:
  Init_Trajectory_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Trajectory_planning_header header(::hv_planning_msgs::msg::Trajectory::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_Trajectory_planning_header(msg_);
  }

private:
  ::hv_planning_msgs::msg::Trajectory msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hv_planning_msgs::msg::Trajectory>()
{
  return hv_planning_msgs::msg::builder::Init_Trajectory_header();
}

}  // namespace hv_planning_msgs

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__TRAJECTORY__BUILDER_HPP_
