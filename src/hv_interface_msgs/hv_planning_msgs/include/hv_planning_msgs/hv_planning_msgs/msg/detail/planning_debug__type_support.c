// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from hv_planning_msgs:msg/PlanningDebug.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "hv_planning_msgs/msg/detail/planning_debug__rosidl_typesupport_introspection_c.h"
#include "hv_planning_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "hv_planning_msgs/msg/detail/planning_debug__functions.h"
#include "hv_planning_msgs/msg/detail/planning_debug__struct.h"


// Include directives for member types
// Member `header`
#include "hv_common_msgs/msg/header.h"
// Member `header`
#include "hv_common_msgs/msg/detail/header__rosidl_typesupport_introspection_c.h"
// Member `data`
#include "rosidl_runtime_c/primitives_sequence_functions.h"

#ifdef __cplusplus
extern "C"
{
#endif

void hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  hv_planning_msgs__msg__PlanningDebug__init(message_memory);
}

void hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_fini_function(void * message_memory)
{
  hv_planning_msgs__msg__PlanningDebug__fini(message_memory);
}

size_t hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__size_function__PlanningDebug__data(
  const void * untyped_member)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__get_const_function__PlanningDebug__data(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__get_function__PlanningDebug__data(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__fetch_function__PlanningDebug__data(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint8_t * item =
    ((const uint8_t *)
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__get_const_function__PlanningDebug__data(untyped_member, index));
  uint8_t * value =
    (uint8_t *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__assign_function__PlanningDebug__data(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint8_t * item =
    ((uint8_t *)
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__get_function__PlanningDebug__data(untyped_member, index));
  const uint8_t * value =
    (const uint8_t *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__resize_function__PlanningDebug__data(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  rosidl_runtime_c__uint8__Sequence__fini(member);
  return rosidl_runtime_c__uint8__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_member_array[2] = {
  {
    "header",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__PlanningDebug, header),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "data",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__PlanningDebug, data),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__size_function__PlanningDebug__data,  // size() function pointer
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__get_const_function__PlanningDebug__data,  // get_const(index) function pointer
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__get_function__PlanningDebug__data,  // get(index) function pointer
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__fetch_function__PlanningDebug__data,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__assign_function__PlanningDebug__data,  // assign(index, value) function pointer
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__resize_function__PlanningDebug__data  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_members = {
  "hv_planning_msgs__msg",  // message namespace
  "PlanningDebug",  // message name
  2,  // number of fields
  sizeof(hv_planning_msgs__msg__PlanningDebug),
  hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_member_array,  // message members
  hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_init_function,  // function to initialize message memory (memory has to be allocated)
  hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_type_support_handle = {
  0,
  &hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_hv_planning_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, PlanningDebug)() {
  hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_common_msgs, msg, Header)();
  if (!hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_type_support_handle.typesupport_identifier) {
    hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &hv_planning_msgs__msg__PlanningDebug__rosidl_typesupport_introspection_c__PlanningDebug_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
