// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hv_planning_msgs:msg/PlanningDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__STRUCT_H_
#define HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__struct.h"
// Member 'data'
#include "rosidl_runtime_c/primitives_sequence.h"

/// Struct defined in msg/PlanningDebug in the package hv_planning_msgs.
/**
  * Generated from /home/<USER>/Workspace/1_Repo/0_Baseline/hv_interface/interface/planning/planning_debug.h
  * Original struct: PlanningDebug
 */
typedef struct hv_planning_msgs__msg__PlanningDebug
{
  hv_common_msgs__msg__Header header;
  rosidl_runtime_c__uint8__Sequence data;
} hv_planning_msgs__msg__PlanningDebug;

// Struct for a sequence of hv_planning_msgs__msg__PlanningDebug.
typedef struct hv_planning_msgs__msg__PlanningDebug__Sequence
{
  hv_planning_msgs__msg__PlanningDebug * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hv_planning_msgs__msg__PlanningDebug__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__STRUCT_H_
