// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from hv_planning_msgs:msg/PlanningDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__TYPE_SUPPORT_H_
#define HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "hv_planning_msgs/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_hv_planning_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  hv_planning_msgs,
  msg,
  PlanningDebug
)();

#ifdef __cplusplus
}
#endif

#endif  // HV_PLANNING_MSGS__MSG__DETAIL__PLANNING_DEBUG__TYPE_SUPPORT_H_
