// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from hv_planning_msgs:msg/EgoIntent.idl
// generated code does not contain a copyright notice
#include "hv_planning_msgs/msg/detail/ego_intent__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `lateral_intents`
// Member `lateral_index`
// Member `longitudinal_intens`
// Member `longitudinal_index`
#include "rosidl_runtime_c/primitives_sequence_functions.h"

bool
hv_planning_msgs__msg__EgoIntent__init(hv_planning_msgs__msg__EgoIntent * msg)
{
  if (!msg) {
    return false;
  }
  // lateral_intents
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->lateral_intents, 0)) {
    hv_planning_msgs__msg__EgoIntent__fini(msg);
    return false;
  }
  // lateral_index
  if (!rosidl_runtime_c__uint32__Sequence__init(&msg->lateral_index, 0)) {
    hv_planning_msgs__msg__EgoIntent__fini(msg);
    return false;
  }
  // longitudinal_intens
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->longitudinal_intens, 0)) {
    hv_planning_msgs__msg__EgoIntent__fini(msg);
    return false;
  }
  // longitudinal_index
  if (!rosidl_runtime_c__uint32__Sequence__init(&msg->longitudinal_index, 0)) {
    hv_planning_msgs__msg__EgoIntent__fini(msg);
    return false;
  }
  return true;
}

void
hv_planning_msgs__msg__EgoIntent__fini(hv_planning_msgs__msg__EgoIntent * msg)
{
  if (!msg) {
    return;
  }
  // lateral_intents
  rosidl_runtime_c__uint8__Sequence__fini(&msg->lateral_intents);
  // lateral_index
  rosidl_runtime_c__uint32__Sequence__fini(&msg->lateral_index);
  // longitudinal_intens
  rosidl_runtime_c__uint8__Sequence__fini(&msg->longitudinal_intens);
  // longitudinal_index
  rosidl_runtime_c__uint32__Sequence__fini(&msg->longitudinal_index);
}

bool
hv_planning_msgs__msg__EgoIntent__are_equal(const hv_planning_msgs__msg__EgoIntent * lhs, const hv_planning_msgs__msg__EgoIntent * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // lateral_intents
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->lateral_intents), &(rhs->lateral_intents)))
  {
    return false;
  }
  // lateral_index
  if (!rosidl_runtime_c__uint32__Sequence__are_equal(
      &(lhs->lateral_index), &(rhs->lateral_index)))
  {
    return false;
  }
  // longitudinal_intens
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->longitudinal_intens), &(rhs->longitudinal_intens)))
  {
    return false;
  }
  // longitudinal_index
  if (!rosidl_runtime_c__uint32__Sequence__are_equal(
      &(lhs->longitudinal_index), &(rhs->longitudinal_index)))
  {
    return false;
  }
  return true;
}

bool
hv_planning_msgs__msg__EgoIntent__copy(
  const hv_planning_msgs__msg__EgoIntent * input,
  hv_planning_msgs__msg__EgoIntent * output)
{
  if (!input || !output) {
    return false;
  }
  // lateral_intents
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->lateral_intents), &(output->lateral_intents)))
  {
    return false;
  }
  // lateral_index
  if (!rosidl_runtime_c__uint32__Sequence__copy(
      &(input->lateral_index), &(output->lateral_index)))
  {
    return false;
  }
  // longitudinal_intens
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->longitudinal_intens), &(output->longitudinal_intens)))
  {
    return false;
  }
  // longitudinal_index
  if (!rosidl_runtime_c__uint32__Sequence__copy(
      &(input->longitudinal_index), &(output->longitudinal_index)))
  {
    return false;
  }
  return true;
}

hv_planning_msgs__msg__EgoIntent *
hv_planning_msgs__msg__EgoIntent__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__EgoIntent * msg = (hv_planning_msgs__msg__EgoIntent *)allocator.allocate(sizeof(hv_planning_msgs__msg__EgoIntent), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(hv_planning_msgs__msg__EgoIntent));
  bool success = hv_planning_msgs__msg__EgoIntent__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
hv_planning_msgs__msg__EgoIntent__destroy(hv_planning_msgs__msg__EgoIntent * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    hv_planning_msgs__msg__EgoIntent__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
hv_planning_msgs__msg__EgoIntent__Sequence__init(hv_planning_msgs__msg__EgoIntent__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__EgoIntent * data = NULL;

  if (size) {
    data = (hv_planning_msgs__msg__EgoIntent *)allocator.zero_allocate(size, sizeof(hv_planning_msgs__msg__EgoIntent), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = hv_planning_msgs__msg__EgoIntent__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        hv_planning_msgs__msg__EgoIntent__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
hv_planning_msgs__msg__EgoIntent__Sequence__fini(hv_planning_msgs__msg__EgoIntent__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      hv_planning_msgs__msg__EgoIntent__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

hv_planning_msgs__msg__EgoIntent__Sequence *
hv_planning_msgs__msg__EgoIntent__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_planning_msgs__msg__EgoIntent__Sequence * array = (hv_planning_msgs__msg__EgoIntent__Sequence *)allocator.allocate(sizeof(hv_planning_msgs__msg__EgoIntent__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = hv_planning_msgs__msg__EgoIntent__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
hv_planning_msgs__msg__EgoIntent__Sequence__destroy(hv_planning_msgs__msg__EgoIntent__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    hv_planning_msgs__msg__EgoIntent__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
hv_planning_msgs__msg__EgoIntent__Sequence__are_equal(const hv_planning_msgs__msg__EgoIntent__Sequence * lhs, const hv_planning_msgs__msg__EgoIntent__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!hv_planning_msgs__msg__EgoIntent__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
hv_planning_msgs__msg__EgoIntent__Sequence__copy(
  const hv_planning_msgs__msg__EgoIntent__Sequence * input,
  hv_planning_msgs__msg__EgoIntent__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(hv_planning_msgs__msg__EgoIntent);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    hv_planning_msgs__msg__EgoIntent * data =
      (hv_planning_msgs__msg__EgoIntent *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!hv_planning_msgs__msg__EgoIntent__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          hv_planning_msgs__msg__EgoIntent__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!hv_planning_msgs__msg__EgoIntent__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
