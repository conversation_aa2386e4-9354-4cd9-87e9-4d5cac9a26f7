// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from hv_planning_msgs:msg/Trajectory.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "hv_planning_msgs/msg/detail/trajectory__rosidl_typesupport_introspection_c.h"
#include "hv_planning_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "hv_planning_msgs/msg/detail/trajectory__functions.h"
#include "hv_planning_msgs/msg/detail/trajectory__struct.h"


// Include directives for member types
// Member `header`
#include "hv_common_msgs/msg/header.h"
// Member `header`
#include "hv_common_msgs/msg/detail/header__rosidl_typesupport_introspection_c.h"
// Member `planning_header`
// Member `routing_header`
#include "hv_planning_msgs/msg/planning_header.h"
// Member `planning_header`
// Member `routing_header`
#include "hv_planning_msgs/msg/detail/planning_header__rosidl_typesupport_introspection_c.h"
// Member `trajectory_point`
#include "hv_planning_msgs/msg/trajectory_point.h"
// Member `trajectory_point`
#include "hv_planning_msgs/msg/detail/trajectory_point__rosidl_typesupport_introspection_c.h"
// Member `estop`
#include "hv_planning_msgs/msg/estop.h"
// Member `estop`
#include "hv_planning_msgs/msg/detail/estop__rosidl_typesupport_introspection_c.h"
// Member `path_point`
#include "hv_planning_msgs/msg/path_point.h"
// Member `path_point`
#include "hv_planning_msgs/msg/detail/path_point__rosidl_typesupport_introspection_c.h"
// Member `replan_reason`
// Member `lane_id`
// Member `target_lane_id`
#include "rosidl_runtime_c/string_functions.h"
// Member `decision`
#include "hv_planning_msgs/msg/decision_result.h"
// Member `decision`
#include "hv_planning_msgs/msg/detail/decision_result__rosidl_typesupport_introspection_c.h"
// Member `latency_stats`
#include "hv_planning_msgs/msg/latency_stats.h"
// Member `latency_stats`
#include "hv_planning_msgs/msg/detail/latency_stats__rosidl_typesupport_introspection_c.h"
// Member `engage_advice`
#include "hv_planning_msgs/msg/engage_advice.h"
// Member `engage_advice`
#include "hv_planning_msgs/msg/detail/engage_advice__rosidl_typesupport_introspection_c.h"
// Member `critical_region`
#include "hv_planning_msgs/msg/critical_region.h"
// Member `critical_region`
#include "hv_planning_msgs/msg/detail/critical_region__rosidl_typesupport_introspection_c.h"
// Member `ego_intent`
#include "hv_planning_msgs/msg/ego_intent.h"
// Member `ego_intent`
#include "hv_planning_msgs/msg/detail/ego_intent__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  hv_planning_msgs__msg__Trajectory__init(message_memory);
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_fini_function(void * message_memory)
{
  hv_planning_msgs__msg__Trajectory__fini(message_memory);
}

size_t hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__trajectory_point(
  const void * untyped_member)
{
  const hv_planning_msgs__msg__TrajectoryPoint__Sequence * member =
    (const hv_planning_msgs__msg__TrajectoryPoint__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__trajectory_point(
  const void * untyped_member, size_t index)
{
  const hv_planning_msgs__msg__TrajectoryPoint__Sequence * member =
    (const hv_planning_msgs__msg__TrajectoryPoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__trajectory_point(
  void * untyped_member, size_t index)
{
  hv_planning_msgs__msg__TrajectoryPoint__Sequence * member =
    (hv_planning_msgs__msg__TrajectoryPoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__trajectory_point(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const hv_planning_msgs__msg__TrajectoryPoint * item =
    ((const hv_planning_msgs__msg__TrajectoryPoint *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__trajectory_point(untyped_member, index));
  hv_planning_msgs__msg__TrajectoryPoint * value =
    (hv_planning_msgs__msg__TrajectoryPoint *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__trajectory_point(
  void * untyped_member, size_t index, const void * untyped_value)
{
  hv_planning_msgs__msg__TrajectoryPoint * item =
    ((hv_planning_msgs__msg__TrajectoryPoint *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__trajectory_point(untyped_member, index));
  const hv_planning_msgs__msg__TrajectoryPoint * value =
    (const hv_planning_msgs__msg__TrajectoryPoint *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__trajectory_point(
  void * untyped_member, size_t size)
{
  hv_planning_msgs__msg__TrajectoryPoint__Sequence * member =
    (hv_planning_msgs__msg__TrajectoryPoint__Sequence *)(untyped_member);
  hv_planning_msgs__msg__TrajectoryPoint__Sequence__fini(member);
  return hv_planning_msgs__msg__TrajectoryPoint__Sequence__init(member, size);
}

size_t hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__path_point(
  const void * untyped_member)
{
  const hv_planning_msgs__msg__PathPoint__Sequence * member =
    (const hv_planning_msgs__msg__PathPoint__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__path_point(
  const void * untyped_member, size_t index)
{
  const hv_planning_msgs__msg__PathPoint__Sequence * member =
    (const hv_planning_msgs__msg__PathPoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__path_point(
  void * untyped_member, size_t index)
{
  hv_planning_msgs__msg__PathPoint__Sequence * member =
    (hv_planning_msgs__msg__PathPoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__path_point(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const hv_planning_msgs__msg__PathPoint * item =
    ((const hv_planning_msgs__msg__PathPoint *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__path_point(untyped_member, index));
  hv_planning_msgs__msg__PathPoint * value =
    (hv_planning_msgs__msg__PathPoint *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__path_point(
  void * untyped_member, size_t index, const void * untyped_value)
{
  hv_planning_msgs__msg__PathPoint * item =
    ((hv_planning_msgs__msg__PathPoint *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__path_point(untyped_member, index));
  const hv_planning_msgs__msg__PathPoint * value =
    (const hv_planning_msgs__msg__PathPoint *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__path_point(
  void * untyped_member, size_t size)
{
  hv_planning_msgs__msg__PathPoint__Sequence * member =
    (hv_planning_msgs__msg__PathPoint__Sequence *)(untyped_member);
  hv_planning_msgs__msg__PathPoint__Sequence__fini(member);
  return hv_planning_msgs__msg__PathPoint__Sequence__init(member, size);
}

size_t hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__lane_id(
  const void * untyped_member)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__lane_id(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__lane_id(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__lane_id(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const rosidl_runtime_c__String * item =
    ((const rosidl_runtime_c__String *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__lane_id(untyped_member, index));
  rosidl_runtime_c__String * value =
    (rosidl_runtime_c__String *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__lane_id(
  void * untyped_member, size_t index, const void * untyped_value)
{
  rosidl_runtime_c__String * item =
    ((rosidl_runtime_c__String *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__lane_id(untyped_member, index));
  const rosidl_runtime_c__String * value =
    (const rosidl_runtime_c__String *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__lane_id(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  rosidl_runtime_c__String__Sequence__fini(member);
  return rosidl_runtime_c__String__Sequence__init(member, size);
}

size_t hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__target_lane_id(
  const void * untyped_member)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return member->size;
}

const void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__target_lane_id(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__target_lane_id(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__target_lane_id(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const rosidl_runtime_c__String * item =
    ((const rosidl_runtime_c__String *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__target_lane_id(untyped_member, index));
  rosidl_runtime_c__String * value =
    (rosidl_runtime_c__String *)(untyped_value);
  *value = *item;
}

void hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__target_lane_id(
  void * untyped_member, size_t index, const void * untyped_value)
{
  rosidl_runtime_c__String * item =
    ((rosidl_runtime_c__String *)
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__target_lane_id(untyped_member, index));
  const rosidl_runtime_c__String * value =
    (const rosidl_runtime_c__String *)(untyped_value);
  *item = *value;
}

bool hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__target_lane_id(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  rosidl_runtime_c__String__Sequence__fini(member);
  return rosidl_runtime_c__String__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[21] = {
  {
    "header",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, header),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "planning_header",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, planning_header),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "total_path_length",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, total_path_length),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "total_path_time",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, total_path_time),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "trajectory_point",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, trajectory_point),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__trajectory_point,  // size() function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__trajectory_point,  // get_const(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__trajectory_point,  // get(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__trajectory_point,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__trajectory_point,  // assign(index, value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__trajectory_point  // resize(index) function pointer
  },
  {
    "estop",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, estop),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "path_point",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, path_point),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__path_point,  // size() function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__path_point,  // get_const(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__path_point,  // get(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__path_point,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__path_point,  // assign(index, value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__path_point  // resize(index) function pointer
  },
  {
    "is_replan",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_BOOLEAN,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, is_replan),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "replan_reason",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, replan_reason),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "is_uturn",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_BOOLEAN,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, is_uturn),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "gear",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, gear),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "decision",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, decision),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "latency_stats",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, latency_stats),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "routing_header",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, routing_header),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "right_of_way_status",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, right_of_way_status),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "lane_id",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, lane_id),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__lane_id,  // size() function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__lane_id,  // get_const(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__lane_id,  // get(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__lane_id,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__lane_id,  // assign(index, value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__lane_id  // resize(index) function pointer
  },
  {
    "engage_advice",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, engage_advice),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "critical_region",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, critical_region),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "trajectory_type",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, trajectory_type),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "target_lane_id",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, target_lane_id),  // bytes offset in struct
    NULL,  // default value
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__size_function__Trajectory__target_lane_id,  // size() function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_const_function__Trajectory__target_lane_id,  // get_const(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__get_function__Trajectory__target_lane_id,  // get(index) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__fetch_function__Trajectory__target_lane_id,  // fetch(index, &value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__assign_function__Trajectory__target_lane_id,  // assign(index, value) function pointer
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__resize_function__Trajectory__target_lane_id  // resize(index) function pointer
  },
  {
    "ego_intent",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hv_planning_msgs__msg__Trajectory, ego_intent),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_members = {
  "hv_planning_msgs__msg",  // message namespace
  "Trajectory",  // message name
  21,  // number of fields
  sizeof(hv_planning_msgs__msg__Trajectory),
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array,  // message members
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_init_function,  // function to initialize message memory (memory has to be allocated)
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_type_support_handle = {
  0,
  &hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_hv_planning_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, Trajectory)() {
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_common_msgs, msg, Header)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[1].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, PlanningHeader)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[4].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, TrajectoryPoint)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[5].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, Estop)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[6].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, PathPoint)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[11].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, DecisionResult)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[12].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, LatencyStats)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[13].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, PlanningHeader)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[16].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, EngageAdvice)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[17].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, CriticalRegion)();
  hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_member_array[20].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hv_planning_msgs, msg, EgoIntent)();
  if (!hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_type_support_handle.typesupport_identifier) {
    hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &hv_planning_msgs__msg__Trajectory__rosidl_typesupport_introspection_c__Trajectory_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
