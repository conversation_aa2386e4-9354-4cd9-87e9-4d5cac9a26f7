# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_planning_msgs:msg/Trajectory.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Trajectory(type):
    """Metaclass of message 'Trajectory'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_planning_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_planning_msgs.msg.Trajectory')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__trajectory
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__trajectory
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__trajectory
            cls._TYPE_SUPPORT = module.type_support_msg__msg__trajectory
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__trajectory

            from hv_common_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

            from hv_planning_msgs.msg import CriticalRegion
            if CriticalRegion.__class__._TYPE_SUPPORT is None:
                CriticalRegion.__class__.__import_type_support__()

            from hv_planning_msgs.msg import DecisionResult
            if DecisionResult.__class__._TYPE_SUPPORT is None:
                DecisionResult.__class__.__import_type_support__()

            from hv_planning_msgs.msg import EgoIntent
            if EgoIntent.__class__._TYPE_SUPPORT is None:
                EgoIntent.__class__.__import_type_support__()

            from hv_planning_msgs.msg import EngageAdvice
            if EngageAdvice.__class__._TYPE_SUPPORT is None:
                EngageAdvice.__class__.__import_type_support__()

            from hv_planning_msgs.msg import Estop
            if Estop.__class__._TYPE_SUPPORT is None:
                Estop.__class__.__import_type_support__()

            from hv_planning_msgs.msg import LatencyStats
            if LatencyStats.__class__._TYPE_SUPPORT is None:
                LatencyStats.__class__.__import_type_support__()

            from hv_planning_msgs.msg import PathPoint
            if PathPoint.__class__._TYPE_SUPPORT is None:
                PathPoint.__class__.__import_type_support__()

            from hv_planning_msgs.msg import PlanningHeader
            if PlanningHeader.__class__._TYPE_SUPPORT is None:
                PlanningHeader.__class__.__import_type_support__()

            from hv_planning_msgs.msg import TrajectoryPoint
            if TrajectoryPoint.__class__._TYPE_SUPPORT is None:
                TrajectoryPoint.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Trajectory(metaclass=Metaclass_Trajectory):
    """Message class 'Trajectory'."""

    __slots__ = [
        '_header',
        '_planning_header',
        '_total_path_length',
        '_total_path_time',
        '_trajectory_point',
        '_estop',
        '_path_point',
        '_is_replan',
        '_replan_reason',
        '_is_uturn',
        '_gear',
        '_decision',
        '_latency_stats',
        '_routing_header',
        '_right_of_way_status',
        '_lane_id',
        '_engage_advice',
        '_critical_region',
        '_trajectory_type',
        '_target_lane_id',
        '_ego_intent',
    ]

    _fields_and_field_types = {
        'header': 'hv_common_msgs/Header',
        'planning_header': 'hv_planning_msgs/PlanningHeader',
        'total_path_length': 'double',
        'total_path_time': 'double',
        'trajectory_point': 'sequence<hv_planning_msgs/TrajectoryPoint>',
        'estop': 'hv_planning_msgs/Estop',
        'path_point': 'sequence<hv_planning_msgs/PathPoint>',
        'is_replan': 'boolean',
        'replan_reason': 'string',
        'is_uturn': 'boolean',
        'gear': 'int8',
        'decision': 'hv_planning_msgs/DecisionResult',
        'latency_stats': 'hv_planning_msgs/LatencyStats',
        'routing_header': 'hv_planning_msgs/PlanningHeader',
        'right_of_way_status': 'int8',
        'lane_id': 'sequence<string>',
        'engage_advice': 'hv_planning_msgs/EngageAdvice',
        'critical_region': 'hv_planning_msgs/CriticalRegion',
        'trajectory_type': 'int8',
        'target_lane_id': 'sequence<string>',
        'ego_intent': 'hv_planning_msgs/EgoIntent',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'PlanningHeader'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'TrajectoryPoint')),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'Estop'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'PathPoint')),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'DecisionResult'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'LatencyStats'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'PlanningHeader'),  # noqa: E501
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.UnboundedString()),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'EngageAdvice'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'CriticalRegion'),  # noqa: E501
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.UnboundedString()),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hv_planning_msgs', 'msg'], 'EgoIntent'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from hv_common_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        from hv_planning_msgs.msg import PlanningHeader
        self.planning_header = kwargs.get('planning_header', PlanningHeader())
        self.total_path_length = kwargs.get('total_path_length', float())
        self.total_path_time = kwargs.get('total_path_time', float())
        self.trajectory_point = kwargs.get('trajectory_point', [])
        from hv_planning_msgs.msg import Estop
        self.estop = kwargs.get('estop', Estop())
        self.path_point = kwargs.get('path_point', [])
        self.is_replan = kwargs.get('is_replan', bool())
        self.replan_reason = kwargs.get('replan_reason', str())
        self.is_uturn = kwargs.get('is_uturn', bool())
        self.gear = kwargs.get('gear', int())
        from hv_planning_msgs.msg import DecisionResult
        self.decision = kwargs.get('decision', DecisionResult())
        from hv_planning_msgs.msg import LatencyStats
        self.latency_stats = kwargs.get('latency_stats', LatencyStats())
        from hv_planning_msgs.msg import PlanningHeader
        self.routing_header = kwargs.get('routing_header', PlanningHeader())
        self.right_of_way_status = kwargs.get('right_of_way_status', int())
        self.lane_id = kwargs.get('lane_id', [])
        from hv_planning_msgs.msg import EngageAdvice
        self.engage_advice = kwargs.get('engage_advice', EngageAdvice())
        from hv_planning_msgs.msg import CriticalRegion
        self.critical_region = kwargs.get('critical_region', CriticalRegion())
        self.trajectory_type = kwargs.get('trajectory_type', int())
        self.target_lane_id = kwargs.get('target_lane_id', [])
        from hv_planning_msgs.msg import EgoIntent
        self.ego_intent = kwargs.get('ego_intent', EgoIntent())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.planning_header != other.planning_header:
            return False
        if self.total_path_length != other.total_path_length:
            return False
        if self.total_path_time != other.total_path_time:
            return False
        if self.trajectory_point != other.trajectory_point:
            return False
        if self.estop != other.estop:
            return False
        if self.path_point != other.path_point:
            return False
        if self.is_replan != other.is_replan:
            return False
        if self.replan_reason != other.replan_reason:
            return False
        if self.is_uturn != other.is_uturn:
            return False
        if self.gear != other.gear:
            return False
        if self.decision != other.decision:
            return False
        if self.latency_stats != other.latency_stats:
            return False
        if self.routing_header != other.routing_header:
            return False
        if self.right_of_way_status != other.right_of_way_status:
            return False
        if self.lane_id != other.lane_id:
            return False
        if self.engage_advice != other.engage_advice:
            return False
        if self.critical_region != other.critical_region:
            return False
        if self.trajectory_type != other.trajectory_type:
            return False
        if self.target_lane_id != other.target_lane_id:
            return False
        if self.ego_intent != other.ego_intent:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if __debug__:
            from hv_common_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def planning_header(self):
        """Message field 'planning_header'."""
        return self._planning_header

    @planning_header.setter
    def planning_header(self, value):
        if __debug__:
            from hv_planning_msgs.msg import PlanningHeader
            assert \
                isinstance(value, PlanningHeader), \
                "The 'planning_header' field must be a sub message of type 'PlanningHeader'"
        self._planning_header = value

    @builtins.property
    def total_path_length(self):
        """Message field 'total_path_length'."""
        return self._total_path_length

    @total_path_length.setter
    def total_path_length(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'total_path_length' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'total_path_length' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._total_path_length = value

    @builtins.property
    def total_path_time(self):
        """Message field 'total_path_time'."""
        return self._total_path_time

    @total_path_time.setter
    def total_path_time(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'total_path_time' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'total_path_time' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._total_path_time = value

    @builtins.property
    def trajectory_point(self):
        """Message field 'trajectory_point'."""
        return self._trajectory_point

    @trajectory_point.setter
    def trajectory_point(self, value):
        if __debug__:
            from hv_planning_msgs.msg import TrajectoryPoint
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, TrajectoryPoint) for v in value) and
                 True), \
                "The 'trajectory_point' field must be a set or sequence and each value of type 'TrajectoryPoint'"
        self._trajectory_point = value

    @builtins.property
    def estop(self):
        """Message field 'estop'."""
        return self._estop

    @estop.setter
    def estop(self, value):
        if __debug__:
            from hv_planning_msgs.msg import Estop
            assert \
                isinstance(value, Estop), \
                "The 'estop' field must be a sub message of type 'Estop'"
        self._estop = value

    @builtins.property
    def path_point(self):
        """Message field 'path_point'."""
        return self._path_point

    @path_point.setter
    def path_point(self, value):
        if __debug__:
            from hv_planning_msgs.msg import PathPoint
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, PathPoint) for v in value) and
                 True), \
                "The 'path_point' field must be a set or sequence and each value of type 'PathPoint'"
        self._path_point = value

    @builtins.property
    def is_replan(self):
        """Message field 'is_replan'."""
        return self._is_replan

    @is_replan.setter
    def is_replan(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_replan' field must be of type 'bool'"
        self._is_replan = value

    @builtins.property
    def replan_reason(self):
        """Message field 'replan_reason'."""
        return self._replan_reason

    @replan_reason.setter
    def replan_reason(self, value):
        if __debug__:
            assert \
                isinstance(value, str), \
                "The 'replan_reason' field must be of type 'str'"
        self._replan_reason = value

    @builtins.property
    def is_uturn(self):
        """Message field 'is_uturn'."""
        return self._is_uturn

    @is_uturn.setter
    def is_uturn(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'is_uturn' field must be of type 'bool'"
        self._is_uturn = value

    @builtins.property
    def gear(self):
        """Message field 'gear'."""
        return self._gear

    @gear.setter
    def gear(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gear' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'gear' field must be an integer in [-128, 127]"
        self._gear = value

    @builtins.property
    def decision(self):
        """Message field 'decision'."""
        return self._decision

    @decision.setter
    def decision(self, value):
        if __debug__:
            from hv_planning_msgs.msg import DecisionResult
            assert \
                isinstance(value, DecisionResult), \
                "The 'decision' field must be a sub message of type 'DecisionResult'"
        self._decision = value

    @builtins.property
    def latency_stats(self):
        """Message field 'latency_stats'."""
        return self._latency_stats

    @latency_stats.setter
    def latency_stats(self, value):
        if __debug__:
            from hv_planning_msgs.msg import LatencyStats
            assert \
                isinstance(value, LatencyStats), \
                "The 'latency_stats' field must be a sub message of type 'LatencyStats'"
        self._latency_stats = value

    @builtins.property
    def routing_header(self):
        """Message field 'routing_header'."""
        return self._routing_header

    @routing_header.setter
    def routing_header(self, value):
        if __debug__:
            from hv_planning_msgs.msg import PlanningHeader
            assert \
                isinstance(value, PlanningHeader), \
                "The 'routing_header' field must be a sub message of type 'PlanningHeader'"
        self._routing_header = value

    @builtins.property
    def right_of_way_status(self):
        """Message field 'right_of_way_status'."""
        return self._right_of_way_status

    @right_of_way_status.setter
    def right_of_way_status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'right_of_way_status' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'right_of_way_status' field must be an integer in [-128, 127]"
        self._right_of_way_status = value

    @builtins.property
    def lane_id(self):
        """Message field 'lane_id'."""
        return self._lane_id

    @lane_id.setter
    def lane_id(self, value):
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, str) for v in value) and
                 True), \
                "The 'lane_id' field must be a set or sequence and each value of type 'str'"
        self._lane_id = value

    @builtins.property
    def engage_advice(self):
        """Message field 'engage_advice'."""
        return self._engage_advice

    @engage_advice.setter
    def engage_advice(self, value):
        if __debug__:
            from hv_planning_msgs.msg import EngageAdvice
            assert \
                isinstance(value, EngageAdvice), \
                "The 'engage_advice' field must be a sub message of type 'EngageAdvice'"
        self._engage_advice = value

    @builtins.property
    def critical_region(self):
        """Message field 'critical_region'."""
        return self._critical_region

    @critical_region.setter
    def critical_region(self, value):
        if __debug__:
            from hv_planning_msgs.msg import CriticalRegion
            assert \
                isinstance(value, CriticalRegion), \
                "The 'critical_region' field must be a sub message of type 'CriticalRegion'"
        self._critical_region = value

    @builtins.property
    def trajectory_type(self):
        """Message field 'trajectory_type'."""
        return self._trajectory_type

    @trajectory_type.setter
    def trajectory_type(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'trajectory_type' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'trajectory_type' field must be an integer in [-128, 127]"
        self._trajectory_type = value

    @builtins.property
    def target_lane_id(self):
        """Message field 'target_lane_id'."""
        return self._target_lane_id

    @target_lane_id.setter
    def target_lane_id(self, value):
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, str) for v in value) and
                 True), \
                "The 'target_lane_id' field must be a set or sequence and each value of type 'str'"
        self._target_lane_id = value

    @builtins.property
    def ego_intent(self):
        """Message field 'ego_intent'."""
        return self._ego_intent

    @ego_intent.setter
    def ego_intent(self, value):
        if __debug__:
            from hv_planning_msgs.msg import EgoIntent
            assert \
                isinstance(value, EgoIntent), \
                "The 'ego_intent' field must be a sub message of type 'EgoIntent'"
        self._ego_intent = value
