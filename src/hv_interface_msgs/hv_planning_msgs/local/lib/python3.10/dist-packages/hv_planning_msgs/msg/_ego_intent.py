# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_planning_msgs:msg/EgoIntent.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'lateral_intents'
# Member 'lateral_index'
# Member 'longitudinal_intens'
# Member 'longitudinal_index'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_EgoIntent(type):
    """Metaclass of message 'EgoIntent'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_planning_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_planning_msgs.msg.EgoIntent')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__ego_intent
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__ego_intent
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__ego_intent
            cls._TYPE_SUPPORT = module.type_support_msg__msg__ego_intent
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__ego_intent

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class EgoIntent(metaclass=Metaclass_EgoIntent):
    """Message class 'EgoIntent'."""

    __slots__ = [
        '_lateral_intents',
        '_lateral_index',
        '_longitudinal_intens',
        '_longitudinal_index',
    ]

    _fields_and_field_types = {
        'lateral_intents': 'sequence<uint8>',
        'lateral_index': 'sequence<uint32>',
        'longitudinal_intens': 'sequence<uint8>',
        'longitudinal_index': 'sequence<uint32>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint8')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint8')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint32')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.lateral_intents = array.array('B', kwargs.get('lateral_intents', []))
        self.lateral_index = array.array('I', kwargs.get('lateral_index', []))
        self.longitudinal_intens = array.array('B', kwargs.get('longitudinal_intens', []))
        self.longitudinal_index = array.array('I', kwargs.get('longitudinal_index', []))

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.lateral_intents != other.lateral_intents:
            return False
        if self.lateral_index != other.lateral_index:
            return False
        if self.longitudinal_intens != other.longitudinal_intens:
            return False
        if self.longitudinal_index != other.longitudinal_index:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def lateral_intents(self):
        """Message field 'lateral_intents'."""
        return self._lateral_intents

    @lateral_intents.setter
    def lateral_intents(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'B', \
                "The 'lateral_intents' array.array() must have the type code of 'B'"
            self._lateral_intents = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 256 for val in value)), \
                "The 'lateral_intents' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 255]"
        self._lateral_intents = array.array('B', value)

    @builtins.property
    def lateral_index(self):
        """Message field 'lateral_index'."""
        return self._lateral_index

    @lateral_index.setter
    def lateral_index(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'lateral_index' array.array() must have the type code of 'I'"
            self._lateral_index = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'lateral_index' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._lateral_index = array.array('I', value)

    @builtins.property
    def longitudinal_intens(self):
        """Message field 'longitudinal_intens'."""
        return self._longitudinal_intens

    @longitudinal_intens.setter
    def longitudinal_intens(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'B', \
                "The 'longitudinal_intens' array.array() must have the type code of 'B'"
            self._longitudinal_intens = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 256 for val in value)), \
                "The 'longitudinal_intens' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 255]"
        self._longitudinal_intens = array.array('B', value)

    @builtins.property
    def longitudinal_index(self):
        """Message field 'longitudinal_index'."""
        return self._longitudinal_index

    @longitudinal_index.setter
    def longitudinal_index(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'I', \
                "The 'longitudinal_index' array.array() must have the type code of 'I'"
            self._longitudinal_index = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 4294967296 for val in value)), \
                "The 'longitudinal_index' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 4294967295]"
        self._longitudinal_index = array.array('I', value)
