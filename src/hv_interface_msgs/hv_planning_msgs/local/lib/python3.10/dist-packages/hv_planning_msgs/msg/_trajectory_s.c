// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_planning_msgs:msg/Trajectory.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_planning_msgs/msg/detail/trajectory__struct.h"
#include "hv_planning_msgs/msg/detail/trajectory__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

#include "rosidl_runtime_c/string.h"
#include "rosidl_runtime_c/string_functions.h"

// Nested array functions includes
#include "hv_planning_msgs/msg/detail/path_point__functions.h"
#include "hv_planning_msgs/msg/detail/trajectory_point__functions.h"
// end nested array functions include
ROSIDL_GENERATOR_C_IMPORT
bool hv_common_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * hv_common_msgs__msg__header__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__planning_header__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__planning_header__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__trajectory_point__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__trajectory_point__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__estop__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__estop__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__path_point__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__path_point__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__decision_result__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__decision_result__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__latency_stats__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__latency_stats__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__planning_header__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__planning_header__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__engage_advice__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__engage_advice__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__critical_region__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__critical_region__convert_to_py(void * raw_ros_message);
bool hv_planning_msgs__msg__ego_intent__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hv_planning_msgs__msg__ego_intent__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hv_planning_msgs__msg__trajectory__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[44];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_planning_msgs.msg._trajectory.Trajectory", full_classname_dest, 43) == 0);
  }
  hv_planning_msgs__msg__Trajectory * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!hv_common_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // planning_header
    PyObject * field = PyObject_GetAttrString(_pymsg, "planning_header");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__planning_header__convert_from_py(field, &ros_message->planning_header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // total_path_length
    PyObject * field = PyObject_GetAttrString(_pymsg, "total_path_length");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->total_path_length = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // total_path_time
    PyObject * field = PyObject_GetAttrString(_pymsg, "total_path_time");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->total_path_time = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // trajectory_point
    PyObject * field = PyObject_GetAttrString(_pymsg, "trajectory_point");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'trajectory_point'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_planning_msgs__msg__TrajectoryPoint__Sequence__init(&(ros_message->trajectory_point), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_planning_msgs__msg__TrajectoryPoint__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_planning_msgs__msg__TrajectoryPoint * dest = ros_message->trajectory_point.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_planning_msgs__msg__trajectory_point__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // estop
    PyObject * field = PyObject_GetAttrString(_pymsg, "estop");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__estop__convert_from_py(field, &ros_message->estop)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // path_point
    PyObject * field = PyObject_GetAttrString(_pymsg, "path_point");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'path_point'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hv_planning_msgs__msg__PathPoint__Sequence__init(&(ros_message->path_point), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hv_planning_msgs__msg__PathPoint__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hv_planning_msgs__msg__PathPoint * dest = ros_message->path_point.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hv_planning_msgs__msg__path_point__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // is_replan
    PyObject * field = PyObject_GetAttrString(_pymsg, "is_replan");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->is_replan = (Py_True == field);
    Py_DECREF(field);
  }
  {  // replan_reason
    PyObject * field = PyObject_GetAttrString(_pymsg, "replan_reason");
    if (!field) {
      return false;
    }
    assert(PyUnicode_Check(field));
    PyObject * encoded_field = PyUnicode_AsUTF8String(field);
    if (!encoded_field) {
      Py_DECREF(field);
      return false;
    }
    rosidl_runtime_c__String__assign(&ros_message->replan_reason, PyBytes_AS_STRING(encoded_field));
    Py_DECREF(encoded_field);
    Py_DECREF(field);
  }
  {  // is_uturn
    PyObject * field = PyObject_GetAttrString(_pymsg, "is_uturn");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->is_uturn = (Py_True == field);
    Py_DECREF(field);
  }
  {  // gear
    PyObject * field = PyObject_GetAttrString(_pymsg, "gear");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->gear = (int8_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // decision
    PyObject * field = PyObject_GetAttrString(_pymsg, "decision");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__decision_result__convert_from_py(field, &ros_message->decision)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // latency_stats
    PyObject * field = PyObject_GetAttrString(_pymsg, "latency_stats");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__latency_stats__convert_from_py(field, &ros_message->latency_stats)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // routing_header
    PyObject * field = PyObject_GetAttrString(_pymsg, "routing_header");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__planning_header__convert_from_py(field, &ros_message->routing_header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // right_of_way_status
    PyObject * field = PyObject_GetAttrString(_pymsg, "right_of_way_status");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->right_of_way_status = (int8_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // lane_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_id");
    if (!field) {
      return false;
    }
    {
      PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'lane_id'");
      if (!seq_field) {
        Py_DECREF(field);
        return false;
      }
      Py_ssize_t size = PySequence_Size(field);
      if (-1 == size) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      if (!rosidl_runtime_c__String__Sequence__init(&(ros_message->lane_id), size)) {
        PyErr_SetString(PyExc_RuntimeError, "unable to create String__Sequence ros_message");
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      rosidl_runtime_c__String * dest = ros_message->lane_id.data;
      for (Py_ssize_t i = 0; i < size; ++i) {
        PyObject * item = PySequence_Fast_GET_ITEM(seq_field, i);
        if (!item) {
          Py_DECREF(seq_field);
          Py_DECREF(field);
          return false;
        }
        assert(PyUnicode_Check(item));
        PyObject * encoded_item = PyUnicode_AsUTF8String(item);
        if (!encoded_item) {
          Py_DECREF(seq_field);
          Py_DECREF(field);
          return false;
        }
        rosidl_runtime_c__String__assign(&dest[i], PyBytes_AS_STRING(encoded_item));
        Py_DECREF(encoded_item);
      }
      Py_DECREF(seq_field);
    }
    Py_DECREF(field);
  }
  {  // engage_advice
    PyObject * field = PyObject_GetAttrString(_pymsg, "engage_advice");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__engage_advice__convert_from_py(field, &ros_message->engage_advice)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // critical_region
    PyObject * field = PyObject_GetAttrString(_pymsg, "critical_region");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__critical_region__convert_from_py(field, &ros_message->critical_region)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // trajectory_type
    PyObject * field = PyObject_GetAttrString(_pymsg, "trajectory_type");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->trajectory_type = (int8_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // target_lane_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "target_lane_id");
    if (!field) {
      return false;
    }
    {
      PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'target_lane_id'");
      if (!seq_field) {
        Py_DECREF(field);
        return false;
      }
      Py_ssize_t size = PySequence_Size(field);
      if (-1 == size) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      if (!rosidl_runtime_c__String__Sequence__init(&(ros_message->target_lane_id), size)) {
        PyErr_SetString(PyExc_RuntimeError, "unable to create String__Sequence ros_message");
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      rosidl_runtime_c__String * dest = ros_message->target_lane_id.data;
      for (Py_ssize_t i = 0; i < size; ++i) {
        PyObject * item = PySequence_Fast_GET_ITEM(seq_field, i);
        if (!item) {
          Py_DECREF(seq_field);
          Py_DECREF(field);
          return false;
        }
        assert(PyUnicode_Check(item));
        PyObject * encoded_item = PyUnicode_AsUTF8String(item);
        if (!encoded_item) {
          Py_DECREF(seq_field);
          Py_DECREF(field);
          return false;
        }
        rosidl_runtime_c__String__assign(&dest[i], PyBytes_AS_STRING(encoded_item));
        Py_DECREF(encoded_item);
      }
      Py_DECREF(seq_field);
    }
    Py_DECREF(field);
  }
  {  // ego_intent
    PyObject * field = PyObject_GetAttrString(_pymsg, "ego_intent");
    if (!field) {
      return false;
    }
    if (!hv_planning_msgs__msg__ego_intent__convert_from_py(field, &ros_message->ego_intent)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_planning_msgs__msg__trajectory__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Trajectory */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_planning_msgs.msg._trajectory");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Trajectory");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_planning_msgs__msg__Trajectory * ros_message = (hv_planning_msgs__msg__Trajectory *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = hv_common_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // planning_header
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__planning_header__convert_to_py(&ros_message->planning_header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "planning_header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // total_path_length
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->total_path_length);
    {
      int rc = PyObject_SetAttrString(_pymessage, "total_path_length", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // total_path_time
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->total_path_time);
    {
      int rc = PyObject_SetAttrString(_pymessage, "total_path_time", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // trajectory_point
    PyObject * field = NULL;
    size_t size = ros_message->trajectory_point.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_planning_msgs__msg__TrajectoryPoint * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->trajectory_point.data[i]);
      PyObject * pyitem = hv_planning_msgs__msg__trajectory_point__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "trajectory_point", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // estop
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__estop__convert_to_py(&ros_message->estop);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "estop", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // path_point
    PyObject * field = NULL;
    size_t size = ros_message->path_point.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hv_planning_msgs__msg__PathPoint * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->path_point.data[i]);
      PyObject * pyitem = hv_planning_msgs__msg__path_point__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "path_point", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // is_replan
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->is_replan ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "is_replan", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // replan_reason
    PyObject * field = NULL;
    field = PyUnicode_DecodeUTF8(
      ros_message->replan_reason.data,
      strlen(ros_message->replan_reason.data),
      "replace");
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "replan_reason", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // is_uturn
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->is_uturn ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "is_uturn", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gear
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->gear);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gear", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // decision
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__decision_result__convert_to_py(&ros_message->decision);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "decision", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // latency_stats
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__latency_stats__convert_to_py(&ros_message->latency_stats);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "latency_stats", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // routing_header
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__planning_header__convert_to_py(&ros_message->routing_header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "routing_header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // right_of_way_status
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->right_of_way_status);
    {
      int rc = PyObject_SetAttrString(_pymessage, "right_of_way_status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_id
    PyObject * field = NULL;
    size_t size = ros_message->lane_id.size;
    rosidl_runtime_c__String * src = ros_message->lane_id.data;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    for (size_t i = 0; i < size; ++i) {
      PyObject * decoded_item = PyUnicode_DecodeUTF8(src[i].data, strlen(src[i].data), "replace");
      if (!decoded_item) {
        return NULL;
      }
      int rc = PyList_SetItem(field, i, decoded_item);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engage_advice
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__engage_advice__convert_to_py(&ros_message->engage_advice);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "engage_advice", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // critical_region
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__critical_region__convert_to_py(&ros_message->critical_region);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "critical_region", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // trajectory_type
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->trajectory_type);
    {
      int rc = PyObject_SetAttrString(_pymessage, "trajectory_type", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // target_lane_id
    PyObject * field = NULL;
    size_t size = ros_message->target_lane_id.size;
    rosidl_runtime_c__String * src = ros_message->target_lane_id.data;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    for (size_t i = 0; i < size; ++i) {
      PyObject * decoded_item = PyUnicode_DecodeUTF8(src[i].data, strlen(src[i].data), "replace");
      if (!decoded_item) {
        return NULL;
      }
      int rc = PyList_SetItem(field, i, decoded_item);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "target_lane_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // ego_intent
    PyObject * field = NULL;
    field = hv_planning_msgs__msg__ego_intent__convert_to_py(&ros_message->ego_intent);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "ego_intent", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
