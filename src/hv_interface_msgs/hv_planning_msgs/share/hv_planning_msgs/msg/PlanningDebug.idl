// generated from rosidl_adapter/resource/msg.idl.em
// with input from hv_planning_msgs/msg/PlanningDebug.msg
// generated code does not contain a copyright notice

#include "hv_common_msgs/msg/Header.idl"

module hv_planning_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Generated from /home/<USER>/Workspace/1_Repo/0_Baseline/hv_interface/interface/planning/planning_debug.h" "\n"
      "Original struct: PlanningDebug")
    struct PlanningDebug {
      hv_common_msgs::msg::Header header;

      sequence<uint8> data;
    };
  };
};
