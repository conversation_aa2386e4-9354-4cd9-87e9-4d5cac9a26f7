// generated from rosidl_adapter/resource/msg.idl.em
// with input from hv_planning_msgs/msg/Trajectory.msg
// generated code does not contain a copyright notice

#include "hv_common_msgs/msg/Header.idl"
#include "hv_planning_msgs/msg/CriticalRegion.idl"
#include "hv_planning_msgs/msg/DecisionResult.idl"
#include "hv_planning_msgs/msg/EgoIntent.idl"
#include "hv_planning_msgs/msg/EngageAdvice.idl"
#include "hv_planning_msgs/msg/Estop.idl"
#include "hv_planning_msgs/msg/LatencyStats.idl"
#include "hv_planning_msgs/msg/PathPoint.idl"
#include "hv_planning_msgs/msg/PlanningHeader.idl"
#include "hv_planning_msgs/msg/TrajectoryPoint.idl"

module hv_planning_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Generated from /home/<USER>/Workspace/1_Repo/0_Baseline/hv_interface/interface/planning/trajectory.h" "\n"
      "Original struct: Trajectory")
    struct Trajectory {
      hv_common_msgs::msg::Header header;

      hv_planning_msgs::msg::PlanningHeader planning_header;

      @verbatim (language="comment", text=
        "default: 0.0")
      double total_path_length;

      @verbatim (language="comment", text=
        "default: 0.0")
      double total_path_time;

      sequence<hv_planning_msgs::msg::TrajectoryPoint> trajectory_point;

      hv_planning_msgs::msg::Estop estop;

      sequence<hv_planning_msgs::msg::PathPoint> path_point;

      @verbatim (language="comment", text=
        "default: false")
      boolean is_replan;

      string replan_reason;

      @verbatim (language="comment", text=
        "default: false")
      boolean is_uturn;

      int8 gear;

      hv_planning_msgs::msg::DecisionResult decision;

      hv_planning_msgs::msg::LatencyStats latency_stats;

      hv_planning_msgs::msg::PlanningHeader routing_header;

      int8 right_of_way_status;

      sequence<string> lane_id;

      hv_planning_msgs::msg::EngageAdvice engage_advice;

      hv_planning_msgs::msg::CriticalRegion critical_region;

      int8 trajectory_type;

      sequence<string> target_lane_id;

      hv_planning_msgs::msg::EgoIntent ego_intent;
    };
  };
};
