# Generated from /home/<USER>/Workspace/1_Repo/0_Baseline/hv_interface/interface/planning/trajectory.h
# Original struct: Trajectory

hv_common_msgs/Header header
PlanningHeader planning_header
float64 total_path_length  # default: 0.0
float64 total_path_time  # default: 0.0
TrajectoryPoint[] trajectory_point
Estop estop
PathPoint[] path_point
bool is_replan  # default: false
string replan_reason
bool is_uturn  # default: false
int8 gear
DecisionResult decision
LatencyStats latency_stats
PlanningHeader routing_header
int8 right_of_way_status
string[] lane_id
EngageAdvice engage_advice
CriticalRegion critical_region
int8 trajectory_type
string[] target_lane_id
EgoIntent ego_intent