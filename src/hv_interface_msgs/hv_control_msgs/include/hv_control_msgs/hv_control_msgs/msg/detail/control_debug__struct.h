// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hv_control_msgs:msg/ControlDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__STRUCT_H_
#define HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__struct.h"
// Member 'reserved0'
// Member 'reserved1'
#include "rosidl_runtime_c/primitives_sequence.h"

/// Struct defined in msg/ControlDebug in the package hv_control_msgs.
/**
  * Generated from /home/<USER>/Workspace/1_Repo/0_Baseline/hv_interface/interface/control/control_debug.h
  * Original struct: ControlDebug
 */
typedef struct hv_control_msgs__msg__ControlDebug
{
  hv_common_msgs__msg__Header header;
  double lat_error;
  double lon_error;
  double speed_error;
  double head_error_deg;
  double steer_frontfeed;
  double steer_feedback;
  double steer_item_lat;
  double steer_item_head;
  double steer_item_head_rate;
  double steer_item_total;
  double steer_angle_cmd;
  double steer_angle_real;
  int32_t state_received_loc;
  int32_t state_received_traj;
  int32_t state_received_chassis;
  int32_t state_send_control;
  double curvature;
  double vehicle_yawrate;
  double loc_current_x;
  double loc_current_y;
  double loc_matched_x;
  double loc_matched_y;
  double equal_k1;
  double equal_k2;
  double equal_k3;
  double longitude_target_speed;
  double longitude_acc_req;
  double frontfeed_normal;
  double frontfeed_kv;
  double frontfeed_e2ss;
  double preview_acceleration_reference;
  double acceleration_cmd_closeloop;
  rosidl_runtime_c__int32__Sequence reserved0;
  int32_t reserved0_size;
  rosidl_runtime_c__double__Sequence reserved1;
  int32_t reserved1_size;
} hv_control_msgs__msg__ControlDebug;

// Struct for a sequence of hv_control_msgs__msg__ControlDebug.
typedef struct hv_control_msgs__msg__ControlDebug__Sequence
{
  hv_control_msgs__msg__ControlDebug * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hv_control_msgs__msg__ControlDebug__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__STRUCT_H_
