// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from hv_control_msgs:msg/ControlDebug.idl
// generated code does not contain a copyright notice
#include "hv_control_msgs/msg/detail/control_debug__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "hv_common_msgs/msg/detail/header__functions.h"
// Member `reserved0`
// Member `reserved1`
#include "rosidl_runtime_c/primitives_sequence_functions.h"

bool
hv_control_msgs__msg__ControlDebug__init(hv_control_msgs__msg__ControlDebug * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__init(&msg->header)) {
    hv_control_msgs__msg__ControlDebug__fini(msg);
    return false;
  }
  // lat_error
  // lon_error
  // speed_error
  // head_error_deg
  // steer_frontfeed
  // steer_feedback
  // steer_item_lat
  // steer_item_head
  // steer_item_head_rate
  // steer_item_total
  // steer_angle_cmd
  // steer_angle_real
  // state_received_loc
  // state_received_traj
  // state_received_chassis
  // state_send_control
  // curvature
  // vehicle_yawrate
  // loc_current_x
  // loc_current_y
  // loc_matched_x
  // loc_matched_y
  // equal_k1
  // equal_k2
  // equal_k3
  // longitude_target_speed
  // longitude_acc_req
  // frontfeed_normal
  // frontfeed_kv
  // frontfeed_e2ss
  // preview_acceleration_reference
  // acceleration_cmd_closeloop
  // reserved0
  if (!rosidl_runtime_c__int32__Sequence__init(&msg->reserved0, 0)) {
    hv_control_msgs__msg__ControlDebug__fini(msg);
    return false;
  }
  // reserved0_size
  // reserved1
  if (!rosidl_runtime_c__double__Sequence__init(&msg->reserved1, 0)) {
    hv_control_msgs__msg__ControlDebug__fini(msg);
    return false;
  }
  // reserved1_size
  return true;
}

void
hv_control_msgs__msg__ControlDebug__fini(hv_control_msgs__msg__ControlDebug * msg)
{
  if (!msg) {
    return;
  }
  // header
  hv_common_msgs__msg__Header__fini(&msg->header);
  // lat_error
  // lon_error
  // speed_error
  // head_error_deg
  // steer_frontfeed
  // steer_feedback
  // steer_item_lat
  // steer_item_head
  // steer_item_head_rate
  // steer_item_total
  // steer_angle_cmd
  // steer_angle_real
  // state_received_loc
  // state_received_traj
  // state_received_chassis
  // state_send_control
  // curvature
  // vehicle_yawrate
  // loc_current_x
  // loc_current_y
  // loc_matched_x
  // loc_matched_y
  // equal_k1
  // equal_k2
  // equal_k3
  // longitude_target_speed
  // longitude_acc_req
  // frontfeed_normal
  // frontfeed_kv
  // frontfeed_e2ss
  // preview_acceleration_reference
  // acceleration_cmd_closeloop
  // reserved0
  rosidl_runtime_c__int32__Sequence__fini(&msg->reserved0);
  // reserved0_size
  // reserved1
  rosidl_runtime_c__double__Sequence__fini(&msg->reserved1);
  // reserved1_size
}

bool
hv_control_msgs__msg__ControlDebug__are_equal(const hv_control_msgs__msg__ControlDebug * lhs, const hv_control_msgs__msg__ControlDebug * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // lat_error
  if (lhs->lat_error != rhs->lat_error) {
    return false;
  }
  // lon_error
  if (lhs->lon_error != rhs->lon_error) {
    return false;
  }
  // speed_error
  if (lhs->speed_error != rhs->speed_error) {
    return false;
  }
  // head_error_deg
  if (lhs->head_error_deg != rhs->head_error_deg) {
    return false;
  }
  // steer_frontfeed
  if (lhs->steer_frontfeed != rhs->steer_frontfeed) {
    return false;
  }
  // steer_feedback
  if (lhs->steer_feedback != rhs->steer_feedback) {
    return false;
  }
  // steer_item_lat
  if (lhs->steer_item_lat != rhs->steer_item_lat) {
    return false;
  }
  // steer_item_head
  if (lhs->steer_item_head != rhs->steer_item_head) {
    return false;
  }
  // steer_item_head_rate
  if (lhs->steer_item_head_rate != rhs->steer_item_head_rate) {
    return false;
  }
  // steer_item_total
  if (lhs->steer_item_total != rhs->steer_item_total) {
    return false;
  }
  // steer_angle_cmd
  if (lhs->steer_angle_cmd != rhs->steer_angle_cmd) {
    return false;
  }
  // steer_angle_real
  if (lhs->steer_angle_real != rhs->steer_angle_real) {
    return false;
  }
  // state_received_loc
  if (lhs->state_received_loc != rhs->state_received_loc) {
    return false;
  }
  // state_received_traj
  if (lhs->state_received_traj != rhs->state_received_traj) {
    return false;
  }
  // state_received_chassis
  if (lhs->state_received_chassis != rhs->state_received_chassis) {
    return false;
  }
  // state_send_control
  if (lhs->state_send_control != rhs->state_send_control) {
    return false;
  }
  // curvature
  if (lhs->curvature != rhs->curvature) {
    return false;
  }
  // vehicle_yawrate
  if (lhs->vehicle_yawrate != rhs->vehicle_yawrate) {
    return false;
  }
  // loc_current_x
  if (lhs->loc_current_x != rhs->loc_current_x) {
    return false;
  }
  // loc_current_y
  if (lhs->loc_current_y != rhs->loc_current_y) {
    return false;
  }
  // loc_matched_x
  if (lhs->loc_matched_x != rhs->loc_matched_x) {
    return false;
  }
  // loc_matched_y
  if (lhs->loc_matched_y != rhs->loc_matched_y) {
    return false;
  }
  // equal_k1
  if (lhs->equal_k1 != rhs->equal_k1) {
    return false;
  }
  // equal_k2
  if (lhs->equal_k2 != rhs->equal_k2) {
    return false;
  }
  // equal_k3
  if (lhs->equal_k3 != rhs->equal_k3) {
    return false;
  }
  // longitude_target_speed
  if (lhs->longitude_target_speed != rhs->longitude_target_speed) {
    return false;
  }
  // longitude_acc_req
  if (lhs->longitude_acc_req != rhs->longitude_acc_req) {
    return false;
  }
  // frontfeed_normal
  if (lhs->frontfeed_normal != rhs->frontfeed_normal) {
    return false;
  }
  // frontfeed_kv
  if (lhs->frontfeed_kv != rhs->frontfeed_kv) {
    return false;
  }
  // frontfeed_e2ss
  if (lhs->frontfeed_e2ss != rhs->frontfeed_e2ss) {
    return false;
  }
  // preview_acceleration_reference
  if (lhs->preview_acceleration_reference != rhs->preview_acceleration_reference) {
    return false;
  }
  // acceleration_cmd_closeloop
  if (lhs->acceleration_cmd_closeloop != rhs->acceleration_cmd_closeloop) {
    return false;
  }
  // reserved0
  if (!rosidl_runtime_c__int32__Sequence__are_equal(
      &(lhs->reserved0), &(rhs->reserved0)))
  {
    return false;
  }
  // reserved0_size
  if (lhs->reserved0_size != rhs->reserved0_size) {
    return false;
  }
  // reserved1
  if (!rosidl_runtime_c__double__Sequence__are_equal(
      &(lhs->reserved1), &(rhs->reserved1)))
  {
    return false;
  }
  // reserved1_size
  if (lhs->reserved1_size != rhs->reserved1_size) {
    return false;
  }
  return true;
}

bool
hv_control_msgs__msg__ControlDebug__copy(
  const hv_control_msgs__msg__ControlDebug * input,
  hv_control_msgs__msg__ControlDebug * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!hv_common_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // lat_error
  output->lat_error = input->lat_error;
  // lon_error
  output->lon_error = input->lon_error;
  // speed_error
  output->speed_error = input->speed_error;
  // head_error_deg
  output->head_error_deg = input->head_error_deg;
  // steer_frontfeed
  output->steer_frontfeed = input->steer_frontfeed;
  // steer_feedback
  output->steer_feedback = input->steer_feedback;
  // steer_item_lat
  output->steer_item_lat = input->steer_item_lat;
  // steer_item_head
  output->steer_item_head = input->steer_item_head;
  // steer_item_head_rate
  output->steer_item_head_rate = input->steer_item_head_rate;
  // steer_item_total
  output->steer_item_total = input->steer_item_total;
  // steer_angle_cmd
  output->steer_angle_cmd = input->steer_angle_cmd;
  // steer_angle_real
  output->steer_angle_real = input->steer_angle_real;
  // state_received_loc
  output->state_received_loc = input->state_received_loc;
  // state_received_traj
  output->state_received_traj = input->state_received_traj;
  // state_received_chassis
  output->state_received_chassis = input->state_received_chassis;
  // state_send_control
  output->state_send_control = input->state_send_control;
  // curvature
  output->curvature = input->curvature;
  // vehicle_yawrate
  output->vehicle_yawrate = input->vehicle_yawrate;
  // loc_current_x
  output->loc_current_x = input->loc_current_x;
  // loc_current_y
  output->loc_current_y = input->loc_current_y;
  // loc_matched_x
  output->loc_matched_x = input->loc_matched_x;
  // loc_matched_y
  output->loc_matched_y = input->loc_matched_y;
  // equal_k1
  output->equal_k1 = input->equal_k1;
  // equal_k2
  output->equal_k2 = input->equal_k2;
  // equal_k3
  output->equal_k3 = input->equal_k3;
  // longitude_target_speed
  output->longitude_target_speed = input->longitude_target_speed;
  // longitude_acc_req
  output->longitude_acc_req = input->longitude_acc_req;
  // frontfeed_normal
  output->frontfeed_normal = input->frontfeed_normal;
  // frontfeed_kv
  output->frontfeed_kv = input->frontfeed_kv;
  // frontfeed_e2ss
  output->frontfeed_e2ss = input->frontfeed_e2ss;
  // preview_acceleration_reference
  output->preview_acceleration_reference = input->preview_acceleration_reference;
  // acceleration_cmd_closeloop
  output->acceleration_cmd_closeloop = input->acceleration_cmd_closeloop;
  // reserved0
  if (!rosidl_runtime_c__int32__Sequence__copy(
      &(input->reserved0), &(output->reserved0)))
  {
    return false;
  }
  // reserved0_size
  output->reserved0_size = input->reserved0_size;
  // reserved1
  if (!rosidl_runtime_c__double__Sequence__copy(
      &(input->reserved1), &(output->reserved1)))
  {
    return false;
  }
  // reserved1_size
  output->reserved1_size = input->reserved1_size;
  return true;
}

hv_control_msgs__msg__ControlDebug *
hv_control_msgs__msg__ControlDebug__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_control_msgs__msg__ControlDebug * msg = (hv_control_msgs__msg__ControlDebug *)allocator.allocate(sizeof(hv_control_msgs__msg__ControlDebug), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(hv_control_msgs__msg__ControlDebug));
  bool success = hv_control_msgs__msg__ControlDebug__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
hv_control_msgs__msg__ControlDebug__destroy(hv_control_msgs__msg__ControlDebug * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    hv_control_msgs__msg__ControlDebug__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
hv_control_msgs__msg__ControlDebug__Sequence__init(hv_control_msgs__msg__ControlDebug__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_control_msgs__msg__ControlDebug * data = NULL;

  if (size) {
    data = (hv_control_msgs__msg__ControlDebug *)allocator.zero_allocate(size, sizeof(hv_control_msgs__msg__ControlDebug), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = hv_control_msgs__msg__ControlDebug__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        hv_control_msgs__msg__ControlDebug__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
hv_control_msgs__msg__ControlDebug__Sequence__fini(hv_control_msgs__msg__ControlDebug__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      hv_control_msgs__msg__ControlDebug__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

hv_control_msgs__msg__ControlDebug__Sequence *
hv_control_msgs__msg__ControlDebug__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hv_control_msgs__msg__ControlDebug__Sequence * array = (hv_control_msgs__msg__ControlDebug__Sequence *)allocator.allocate(sizeof(hv_control_msgs__msg__ControlDebug__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = hv_control_msgs__msg__ControlDebug__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
hv_control_msgs__msg__ControlDebug__Sequence__destroy(hv_control_msgs__msg__ControlDebug__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    hv_control_msgs__msg__ControlDebug__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
hv_control_msgs__msg__ControlDebug__Sequence__are_equal(const hv_control_msgs__msg__ControlDebug__Sequence * lhs, const hv_control_msgs__msg__ControlDebug__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!hv_control_msgs__msg__ControlDebug__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
hv_control_msgs__msg__ControlDebug__Sequence__copy(
  const hv_control_msgs__msg__ControlDebug__Sequence * input,
  hv_control_msgs__msg__ControlDebug__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(hv_control_msgs__msg__ControlDebug);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    hv_control_msgs__msg__ControlDebug * data =
      (hv_control_msgs__msg__ControlDebug *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!hv_control_msgs__msg__ControlDebug__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          hv_control_msgs__msg__ControlDebug__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!hv_control_msgs__msg__ControlDebug__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
