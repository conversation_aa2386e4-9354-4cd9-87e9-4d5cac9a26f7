// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hv_control_msgs:msg/ControlDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__STRUCT_HPP_
#define HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__hv_control_msgs__msg__ControlDebug __attribute__((deprecated))
#else
# define DEPRECATED__hv_control_msgs__msg__ControlDebug __declspec(deprecated)
#endif

namespace hv_control_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct ControlDebug_
{
  using Type = ControlDebug_<ContainerAllocator>;

  explicit ControlDebug_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lat_error = 0.0;
      this->lon_error = 0.0;
      this->speed_error = 0.0;
      this->head_error_deg = 0.0;
      this->steer_frontfeed = 0.0;
      this->steer_feedback = 0.0;
      this->steer_item_lat = 0.0;
      this->steer_item_head = 0.0;
      this->steer_item_head_rate = 0.0;
      this->steer_item_total = 0.0;
      this->steer_angle_cmd = 0.0;
      this->steer_angle_real = 0.0;
      this->state_received_loc = 0l;
      this->state_received_traj = 0l;
      this->state_received_chassis = 0l;
      this->state_send_control = 0l;
      this->curvature = 0.0;
      this->vehicle_yawrate = 0.0;
      this->loc_current_x = 0.0;
      this->loc_current_y = 0.0;
      this->loc_matched_x = 0.0;
      this->loc_matched_y = 0.0;
      this->equal_k1 = 0.0;
      this->equal_k2 = 0.0;
      this->equal_k3 = 0.0;
      this->longitude_target_speed = 0.0;
      this->longitude_acc_req = 0.0;
      this->frontfeed_normal = 0.0;
      this->frontfeed_kv = 0.0;
      this->frontfeed_e2ss = 0.0;
      this->preview_acceleration_reference = 0.0;
      this->acceleration_cmd_closeloop = 0.0;
      this->reserved0_size = 0l;
      this->reserved1_size = 0l;
    }
  }

  explicit ControlDebug_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lat_error = 0.0;
      this->lon_error = 0.0;
      this->speed_error = 0.0;
      this->head_error_deg = 0.0;
      this->steer_frontfeed = 0.0;
      this->steer_feedback = 0.0;
      this->steer_item_lat = 0.0;
      this->steer_item_head = 0.0;
      this->steer_item_head_rate = 0.0;
      this->steer_item_total = 0.0;
      this->steer_angle_cmd = 0.0;
      this->steer_angle_real = 0.0;
      this->state_received_loc = 0l;
      this->state_received_traj = 0l;
      this->state_received_chassis = 0l;
      this->state_send_control = 0l;
      this->curvature = 0.0;
      this->vehicle_yawrate = 0.0;
      this->loc_current_x = 0.0;
      this->loc_current_y = 0.0;
      this->loc_matched_x = 0.0;
      this->loc_matched_y = 0.0;
      this->equal_k1 = 0.0;
      this->equal_k2 = 0.0;
      this->equal_k3 = 0.0;
      this->longitude_target_speed = 0.0;
      this->longitude_acc_req = 0.0;
      this->frontfeed_normal = 0.0;
      this->frontfeed_kv = 0.0;
      this->frontfeed_e2ss = 0.0;
      this->preview_acceleration_reference = 0.0;
      this->acceleration_cmd_closeloop = 0.0;
      this->reserved0_size = 0l;
      this->reserved1_size = 0l;
    }
  }

  // field types and members
  using _header_type =
    hv_common_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _lat_error_type =
    double;
  _lat_error_type lat_error;
  using _lon_error_type =
    double;
  _lon_error_type lon_error;
  using _speed_error_type =
    double;
  _speed_error_type speed_error;
  using _head_error_deg_type =
    double;
  _head_error_deg_type head_error_deg;
  using _steer_frontfeed_type =
    double;
  _steer_frontfeed_type steer_frontfeed;
  using _steer_feedback_type =
    double;
  _steer_feedback_type steer_feedback;
  using _steer_item_lat_type =
    double;
  _steer_item_lat_type steer_item_lat;
  using _steer_item_head_type =
    double;
  _steer_item_head_type steer_item_head;
  using _steer_item_head_rate_type =
    double;
  _steer_item_head_rate_type steer_item_head_rate;
  using _steer_item_total_type =
    double;
  _steer_item_total_type steer_item_total;
  using _steer_angle_cmd_type =
    double;
  _steer_angle_cmd_type steer_angle_cmd;
  using _steer_angle_real_type =
    double;
  _steer_angle_real_type steer_angle_real;
  using _state_received_loc_type =
    int32_t;
  _state_received_loc_type state_received_loc;
  using _state_received_traj_type =
    int32_t;
  _state_received_traj_type state_received_traj;
  using _state_received_chassis_type =
    int32_t;
  _state_received_chassis_type state_received_chassis;
  using _state_send_control_type =
    int32_t;
  _state_send_control_type state_send_control;
  using _curvature_type =
    double;
  _curvature_type curvature;
  using _vehicle_yawrate_type =
    double;
  _vehicle_yawrate_type vehicle_yawrate;
  using _loc_current_x_type =
    double;
  _loc_current_x_type loc_current_x;
  using _loc_current_y_type =
    double;
  _loc_current_y_type loc_current_y;
  using _loc_matched_x_type =
    double;
  _loc_matched_x_type loc_matched_x;
  using _loc_matched_y_type =
    double;
  _loc_matched_y_type loc_matched_y;
  using _equal_k1_type =
    double;
  _equal_k1_type equal_k1;
  using _equal_k2_type =
    double;
  _equal_k2_type equal_k2;
  using _equal_k3_type =
    double;
  _equal_k3_type equal_k3;
  using _longitude_target_speed_type =
    double;
  _longitude_target_speed_type longitude_target_speed;
  using _longitude_acc_req_type =
    double;
  _longitude_acc_req_type longitude_acc_req;
  using _frontfeed_normal_type =
    double;
  _frontfeed_normal_type frontfeed_normal;
  using _frontfeed_kv_type =
    double;
  _frontfeed_kv_type frontfeed_kv;
  using _frontfeed_e2ss_type =
    double;
  _frontfeed_e2ss_type frontfeed_e2ss;
  using _preview_acceleration_reference_type =
    double;
  _preview_acceleration_reference_type preview_acceleration_reference;
  using _acceleration_cmd_closeloop_type =
    double;
  _acceleration_cmd_closeloop_type acceleration_cmd_closeloop;
  using _reserved0_type =
    std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>>;
  _reserved0_type reserved0;
  using _reserved0_size_type =
    int32_t;
  _reserved0_size_type reserved0_size;
  using _reserved1_type =
    std::vector<double, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<double>>;
  _reserved1_type reserved1;
  using _reserved1_size_type =
    int32_t;
  _reserved1_size_type reserved1_size;

  // setters for named parameter idiom
  Type & set__header(
    const hv_common_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__lat_error(
    const double & _arg)
  {
    this->lat_error = _arg;
    return *this;
  }
  Type & set__lon_error(
    const double & _arg)
  {
    this->lon_error = _arg;
    return *this;
  }
  Type & set__speed_error(
    const double & _arg)
  {
    this->speed_error = _arg;
    return *this;
  }
  Type & set__head_error_deg(
    const double & _arg)
  {
    this->head_error_deg = _arg;
    return *this;
  }
  Type & set__steer_frontfeed(
    const double & _arg)
  {
    this->steer_frontfeed = _arg;
    return *this;
  }
  Type & set__steer_feedback(
    const double & _arg)
  {
    this->steer_feedback = _arg;
    return *this;
  }
  Type & set__steer_item_lat(
    const double & _arg)
  {
    this->steer_item_lat = _arg;
    return *this;
  }
  Type & set__steer_item_head(
    const double & _arg)
  {
    this->steer_item_head = _arg;
    return *this;
  }
  Type & set__steer_item_head_rate(
    const double & _arg)
  {
    this->steer_item_head_rate = _arg;
    return *this;
  }
  Type & set__steer_item_total(
    const double & _arg)
  {
    this->steer_item_total = _arg;
    return *this;
  }
  Type & set__steer_angle_cmd(
    const double & _arg)
  {
    this->steer_angle_cmd = _arg;
    return *this;
  }
  Type & set__steer_angle_real(
    const double & _arg)
  {
    this->steer_angle_real = _arg;
    return *this;
  }
  Type & set__state_received_loc(
    const int32_t & _arg)
  {
    this->state_received_loc = _arg;
    return *this;
  }
  Type & set__state_received_traj(
    const int32_t & _arg)
  {
    this->state_received_traj = _arg;
    return *this;
  }
  Type & set__state_received_chassis(
    const int32_t & _arg)
  {
    this->state_received_chassis = _arg;
    return *this;
  }
  Type & set__state_send_control(
    const int32_t & _arg)
  {
    this->state_send_control = _arg;
    return *this;
  }
  Type & set__curvature(
    const double & _arg)
  {
    this->curvature = _arg;
    return *this;
  }
  Type & set__vehicle_yawrate(
    const double & _arg)
  {
    this->vehicle_yawrate = _arg;
    return *this;
  }
  Type & set__loc_current_x(
    const double & _arg)
  {
    this->loc_current_x = _arg;
    return *this;
  }
  Type & set__loc_current_y(
    const double & _arg)
  {
    this->loc_current_y = _arg;
    return *this;
  }
  Type & set__loc_matched_x(
    const double & _arg)
  {
    this->loc_matched_x = _arg;
    return *this;
  }
  Type & set__loc_matched_y(
    const double & _arg)
  {
    this->loc_matched_y = _arg;
    return *this;
  }
  Type & set__equal_k1(
    const double & _arg)
  {
    this->equal_k1 = _arg;
    return *this;
  }
  Type & set__equal_k2(
    const double & _arg)
  {
    this->equal_k2 = _arg;
    return *this;
  }
  Type & set__equal_k3(
    const double & _arg)
  {
    this->equal_k3 = _arg;
    return *this;
  }
  Type & set__longitude_target_speed(
    const double & _arg)
  {
    this->longitude_target_speed = _arg;
    return *this;
  }
  Type & set__longitude_acc_req(
    const double & _arg)
  {
    this->longitude_acc_req = _arg;
    return *this;
  }
  Type & set__frontfeed_normal(
    const double & _arg)
  {
    this->frontfeed_normal = _arg;
    return *this;
  }
  Type & set__frontfeed_kv(
    const double & _arg)
  {
    this->frontfeed_kv = _arg;
    return *this;
  }
  Type & set__frontfeed_e2ss(
    const double & _arg)
  {
    this->frontfeed_e2ss = _arg;
    return *this;
  }
  Type & set__preview_acceleration_reference(
    const double & _arg)
  {
    this->preview_acceleration_reference = _arg;
    return *this;
  }
  Type & set__acceleration_cmd_closeloop(
    const double & _arg)
  {
    this->acceleration_cmd_closeloop = _arg;
    return *this;
  }
  Type & set__reserved0(
    const std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> & _arg)
  {
    this->reserved0 = _arg;
    return *this;
  }
  Type & set__reserved0_size(
    const int32_t & _arg)
  {
    this->reserved0_size = _arg;
    return *this;
  }
  Type & set__reserved1(
    const std::vector<double, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<double>> & _arg)
  {
    this->reserved1 = _arg;
    return *this;
  }
  Type & set__reserved1_size(
    const int32_t & _arg)
  {
    this->reserved1_size = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hv_control_msgs::msg::ControlDebug_<ContainerAllocator> *;
  using ConstRawPtr =
    const hv_control_msgs::msg::ControlDebug_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hv_control_msgs::msg::ControlDebug_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hv_control_msgs::msg::ControlDebug_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hv_control_msgs__msg__ControlDebug
    std::shared_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hv_control_msgs__msg__ControlDebug
    std::shared_ptr<hv_control_msgs::msg::ControlDebug_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const ControlDebug_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->lat_error != other.lat_error) {
      return false;
    }
    if (this->lon_error != other.lon_error) {
      return false;
    }
    if (this->speed_error != other.speed_error) {
      return false;
    }
    if (this->head_error_deg != other.head_error_deg) {
      return false;
    }
    if (this->steer_frontfeed != other.steer_frontfeed) {
      return false;
    }
    if (this->steer_feedback != other.steer_feedback) {
      return false;
    }
    if (this->steer_item_lat != other.steer_item_lat) {
      return false;
    }
    if (this->steer_item_head != other.steer_item_head) {
      return false;
    }
    if (this->steer_item_head_rate != other.steer_item_head_rate) {
      return false;
    }
    if (this->steer_item_total != other.steer_item_total) {
      return false;
    }
    if (this->steer_angle_cmd != other.steer_angle_cmd) {
      return false;
    }
    if (this->steer_angle_real != other.steer_angle_real) {
      return false;
    }
    if (this->state_received_loc != other.state_received_loc) {
      return false;
    }
    if (this->state_received_traj != other.state_received_traj) {
      return false;
    }
    if (this->state_received_chassis != other.state_received_chassis) {
      return false;
    }
    if (this->state_send_control != other.state_send_control) {
      return false;
    }
    if (this->curvature != other.curvature) {
      return false;
    }
    if (this->vehicle_yawrate != other.vehicle_yawrate) {
      return false;
    }
    if (this->loc_current_x != other.loc_current_x) {
      return false;
    }
    if (this->loc_current_y != other.loc_current_y) {
      return false;
    }
    if (this->loc_matched_x != other.loc_matched_x) {
      return false;
    }
    if (this->loc_matched_y != other.loc_matched_y) {
      return false;
    }
    if (this->equal_k1 != other.equal_k1) {
      return false;
    }
    if (this->equal_k2 != other.equal_k2) {
      return false;
    }
    if (this->equal_k3 != other.equal_k3) {
      return false;
    }
    if (this->longitude_target_speed != other.longitude_target_speed) {
      return false;
    }
    if (this->longitude_acc_req != other.longitude_acc_req) {
      return false;
    }
    if (this->frontfeed_normal != other.frontfeed_normal) {
      return false;
    }
    if (this->frontfeed_kv != other.frontfeed_kv) {
      return false;
    }
    if (this->frontfeed_e2ss != other.frontfeed_e2ss) {
      return false;
    }
    if (this->preview_acceleration_reference != other.preview_acceleration_reference) {
      return false;
    }
    if (this->acceleration_cmd_closeloop != other.acceleration_cmd_closeloop) {
      return false;
    }
    if (this->reserved0 != other.reserved0) {
      return false;
    }
    if (this->reserved0_size != other.reserved0_size) {
      return false;
    }
    if (this->reserved1 != other.reserved1) {
      return false;
    }
    if (this->reserved1_size != other.reserved1_size) {
      return false;
    }
    return true;
  }
  bool operator!=(const ControlDebug_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct ControlDebug_

// alias to use template instance with default allocator
using ControlDebug =
  hv_control_msgs::msg::ControlDebug_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hv_control_msgs

#endif  // HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__STRUCT_HPP_
