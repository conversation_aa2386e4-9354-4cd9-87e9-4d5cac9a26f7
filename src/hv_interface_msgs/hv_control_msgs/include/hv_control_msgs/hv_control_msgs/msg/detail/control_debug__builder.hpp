// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hv_control_msgs:msg/ControlDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__BUILDER_HPP_
#define HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hv_control_msgs/msg/detail/control_debug__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hv_control_msgs
{

namespace msg
{

namespace builder
{

class Init_ControlDebug_reserved1_size
{
public:
  explicit Init_ControlDebug_reserved1_size(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  ::hv_control_msgs::msg::ControlDebug reserved1_size(::hv_control_msgs::msg::ControlDebug::_reserved1_size_type arg)
  {
    msg_.reserved1_size = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_reserved1
{
public:
  explicit Init_ControlDebug_reserved1(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_reserved1_size reserved1(::hv_control_msgs::msg::ControlDebug::_reserved1_type arg)
  {
    msg_.reserved1 = std::move(arg);
    return Init_ControlDebug_reserved1_size(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_reserved0_size
{
public:
  explicit Init_ControlDebug_reserved0_size(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_reserved1 reserved0_size(::hv_control_msgs::msg::ControlDebug::_reserved0_size_type arg)
  {
    msg_.reserved0_size = std::move(arg);
    return Init_ControlDebug_reserved1(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_reserved0
{
public:
  explicit Init_ControlDebug_reserved0(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_reserved0_size reserved0(::hv_control_msgs::msg::ControlDebug::_reserved0_type arg)
  {
    msg_.reserved0 = std::move(arg);
    return Init_ControlDebug_reserved0_size(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_acceleration_cmd_closeloop
{
public:
  explicit Init_ControlDebug_acceleration_cmd_closeloop(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_reserved0 acceleration_cmd_closeloop(::hv_control_msgs::msg::ControlDebug::_acceleration_cmd_closeloop_type arg)
  {
    msg_.acceleration_cmd_closeloop = std::move(arg);
    return Init_ControlDebug_reserved0(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_preview_acceleration_reference
{
public:
  explicit Init_ControlDebug_preview_acceleration_reference(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_acceleration_cmd_closeloop preview_acceleration_reference(::hv_control_msgs::msg::ControlDebug::_preview_acceleration_reference_type arg)
  {
    msg_.preview_acceleration_reference = std::move(arg);
    return Init_ControlDebug_acceleration_cmd_closeloop(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_frontfeed_e2ss
{
public:
  explicit Init_ControlDebug_frontfeed_e2ss(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_preview_acceleration_reference frontfeed_e2ss(::hv_control_msgs::msg::ControlDebug::_frontfeed_e2ss_type arg)
  {
    msg_.frontfeed_e2ss = std::move(arg);
    return Init_ControlDebug_preview_acceleration_reference(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_frontfeed_kv
{
public:
  explicit Init_ControlDebug_frontfeed_kv(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_frontfeed_e2ss frontfeed_kv(::hv_control_msgs::msg::ControlDebug::_frontfeed_kv_type arg)
  {
    msg_.frontfeed_kv = std::move(arg);
    return Init_ControlDebug_frontfeed_e2ss(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_frontfeed_normal
{
public:
  explicit Init_ControlDebug_frontfeed_normal(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_frontfeed_kv frontfeed_normal(::hv_control_msgs::msg::ControlDebug::_frontfeed_normal_type arg)
  {
    msg_.frontfeed_normal = std::move(arg);
    return Init_ControlDebug_frontfeed_kv(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_longitude_acc_req
{
public:
  explicit Init_ControlDebug_longitude_acc_req(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_frontfeed_normal longitude_acc_req(::hv_control_msgs::msg::ControlDebug::_longitude_acc_req_type arg)
  {
    msg_.longitude_acc_req = std::move(arg);
    return Init_ControlDebug_frontfeed_normal(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_longitude_target_speed
{
public:
  explicit Init_ControlDebug_longitude_target_speed(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_longitude_acc_req longitude_target_speed(::hv_control_msgs::msg::ControlDebug::_longitude_target_speed_type arg)
  {
    msg_.longitude_target_speed = std::move(arg);
    return Init_ControlDebug_longitude_acc_req(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_equal_k3
{
public:
  explicit Init_ControlDebug_equal_k3(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_longitude_target_speed equal_k3(::hv_control_msgs::msg::ControlDebug::_equal_k3_type arg)
  {
    msg_.equal_k3 = std::move(arg);
    return Init_ControlDebug_longitude_target_speed(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_equal_k2
{
public:
  explicit Init_ControlDebug_equal_k2(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_equal_k3 equal_k2(::hv_control_msgs::msg::ControlDebug::_equal_k2_type arg)
  {
    msg_.equal_k2 = std::move(arg);
    return Init_ControlDebug_equal_k3(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_equal_k1
{
public:
  explicit Init_ControlDebug_equal_k1(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_equal_k2 equal_k1(::hv_control_msgs::msg::ControlDebug::_equal_k1_type arg)
  {
    msg_.equal_k1 = std::move(arg);
    return Init_ControlDebug_equal_k2(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_loc_matched_y
{
public:
  explicit Init_ControlDebug_loc_matched_y(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_equal_k1 loc_matched_y(::hv_control_msgs::msg::ControlDebug::_loc_matched_y_type arg)
  {
    msg_.loc_matched_y = std::move(arg);
    return Init_ControlDebug_equal_k1(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_loc_matched_x
{
public:
  explicit Init_ControlDebug_loc_matched_x(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_loc_matched_y loc_matched_x(::hv_control_msgs::msg::ControlDebug::_loc_matched_x_type arg)
  {
    msg_.loc_matched_x = std::move(arg);
    return Init_ControlDebug_loc_matched_y(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_loc_current_y
{
public:
  explicit Init_ControlDebug_loc_current_y(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_loc_matched_x loc_current_y(::hv_control_msgs::msg::ControlDebug::_loc_current_y_type arg)
  {
    msg_.loc_current_y = std::move(arg);
    return Init_ControlDebug_loc_matched_x(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_loc_current_x
{
public:
  explicit Init_ControlDebug_loc_current_x(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_loc_current_y loc_current_x(::hv_control_msgs::msg::ControlDebug::_loc_current_x_type arg)
  {
    msg_.loc_current_x = std::move(arg);
    return Init_ControlDebug_loc_current_y(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_vehicle_yawrate
{
public:
  explicit Init_ControlDebug_vehicle_yawrate(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_loc_current_x vehicle_yawrate(::hv_control_msgs::msg::ControlDebug::_vehicle_yawrate_type arg)
  {
    msg_.vehicle_yawrate = std::move(arg);
    return Init_ControlDebug_loc_current_x(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_curvature
{
public:
  explicit Init_ControlDebug_curvature(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_vehicle_yawrate curvature(::hv_control_msgs::msg::ControlDebug::_curvature_type arg)
  {
    msg_.curvature = std::move(arg);
    return Init_ControlDebug_vehicle_yawrate(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_state_send_control
{
public:
  explicit Init_ControlDebug_state_send_control(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_curvature state_send_control(::hv_control_msgs::msg::ControlDebug::_state_send_control_type arg)
  {
    msg_.state_send_control = std::move(arg);
    return Init_ControlDebug_curvature(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_state_received_chassis
{
public:
  explicit Init_ControlDebug_state_received_chassis(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_state_send_control state_received_chassis(::hv_control_msgs::msg::ControlDebug::_state_received_chassis_type arg)
  {
    msg_.state_received_chassis = std::move(arg);
    return Init_ControlDebug_state_send_control(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_state_received_traj
{
public:
  explicit Init_ControlDebug_state_received_traj(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_state_received_chassis state_received_traj(::hv_control_msgs::msg::ControlDebug::_state_received_traj_type arg)
  {
    msg_.state_received_traj = std::move(arg);
    return Init_ControlDebug_state_received_chassis(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_state_received_loc
{
public:
  explicit Init_ControlDebug_state_received_loc(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_state_received_traj state_received_loc(::hv_control_msgs::msg::ControlDebug::_state_received_loc_type arg)
  {
    msg_.state_received_loc = std::move(arg);
    return Init_ControlDebug_state_received_traj(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_angle_real
{
public:
  explicit Init_ControlDebug_steer_angle_real(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_state_received_loc steer_angle_real(::hv_control_msgs::msg::ControlDebug::_steer_angle_real_type arg)
  {
    msg_.steer_angle_real = std::move(arg);
    return Init_ControlDebug_state_received_loc(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_angle_cmd
{
public:
  explicit Init_ControlDebug_steer_angle_cmd(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_angle_real steer_angle_cmd(::hv_control_msgs::msg::ControlDebug::_steer_angle_cmd_type arg)
  {
    msg_.steer_angle_cmd = std::move(arg);
    return Init_ControlDebug_steer_angle_real(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_item_total
{
public:
  explicit Init_ControlDebug_steer_item_total(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_angle_cmd steer_item_total(::hv_control_msgs::msg::ControlDebug::_steer_item_total_type arg)
  {
    msg_.steer_item_total = std::move(arg);
    return Init_ControlDebug_steer_angle_cmd(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_item_head_rate
{
public:
  explicit Init_ControlDebug_steer_item_head_rate(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_item_total steer_item_head_rate(::hv_control_msgs::msg::ControlDebug::_steer_item_head_rate_type arg)
  {
    msg_.steer_item_head_rate = std::move(arg);
    return Init_ControlDebug_steer_item_total(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_item_head
{
public:
  explicit Init_ControlDebug_steer_item_head(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_item_head_rate steer_item_head(::hv_control_msgs::msg::ControlDebug::_steer_item_head_type arg)
  {
    msg_.steer_item_head = std::move(arg);
    return Init_ControlDebug_steer_item_head_rate(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_item_lat
{
public:
  explicit Init_ControlDebug_steer_item_lat(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_item_head steer_item_lat(::hv_control_msgs::msg::ControlDebug::_steer_item_lat_type arg)
  {
    msg_.steer_item_lat = std::move(arg);
    return Init_ControlDebug_steer_item_head(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_feedback
{
public:
  explicit Init_ControlDebug_steer_feedback(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_item_lat steer_feedback(::hv_control_msgs::msg::ControlDebug::_steer_feedback_type arg)
  {
    msg_.steer_feedback = std::move(arg);
    return Init_ControlDebug_steer_item_lat(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_steer_frontfeed
{
public:
  explicit Init_ControlDebug_steer_frontfeed(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_feedback steer_frontfeed(::hv_control_msgs::msg::ControlDebug::_steer_frontfeed_type arg)
  {
    msg_.steer_frontfeed = std::move(arg);
    return Init_ControlDebug_steer_feedback(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_head_error_deg
{
public:
  explicit Init_ControlDebug_head_error_deg(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_steer_frontfeed head_error_deg(::hv_control_msgs::msg::ControlDebug::_head_error_deg_type arg)
  {
    msg_.head_error_deg = std::move(arg);
    return Init_ControlDebug_steer_frontfeed(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_speed_error
{
public:
  explicit Init_ControlDebug_speed_error(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_head_error_deg speed_error(::hv_control_msgs::msg::ControlDebug::_speed_error_type arg)
  {
    msg_.speed_error = std::move(arg);
    return Init_ControlDebug_head_error_deg(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_lon_error
{
public:
  explicit Init_ControlDebug_lon_error(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_speed_error lon_error(::hv_control_msgs::msg::ControlDebug::_lon_error_type arg)
  {
    msg_.lon_error = std::move(arg);
    return Init_ControlDebug_speed_error(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_lat_error
{
public:
  explicit Init_ControlDebug_lat_error(::hv_control_msgs::msg::ControlDebug & msg)
  : msg_(msg)
  {}
  Init_ControlDebug_lon_error lat_error(::hv_control_msgs::msg::ControlDebug::_lat_error_type arg)
  {
    msg_.lat_error = std::move(arg);
    return Init_ControlDebug_lon_error(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

class Init_ControlDebug_header
{
public:
  Init_ControlDebug_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_ControlDebug_lat_error header(::hv_control_msgs::msg::ControlDebug::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_ControlDebug_lat_error(msg_);
  }

private:
  ::hv_control_msgs::msg::ControlDebug msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hv_control_msgs::msg::ControlDebug>()
{
  return hv_control_msgs::msg::builder::Init_ControlDebug_header();
}

}  // namespace hv_control_msgs

#endif  // HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__BUILDER_HPP_
