// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from hv_control_msgs:msg/ControlDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__TYPE_SUPPORT_HPP_
#define HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "hv_control_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_hv_control_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  hv_control_msgs,
  msg,
  ControlDebug
)();
#ifdef __cplusplus
}
#endif

#endif  // HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__TYPE_SUPPORT_HPP_
