// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hv_control_msgs:msg/ControlDebug.idl
// generated code does not contain a copyright notice

#ifndef HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__TRAITS_HPP_
#define HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hv_control_msgs/msg/detail/control_debug__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "hv_common_msgs/msg/detail/header__traits.hpp"

namespace hv_control_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const ControlDebug & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: lat_error
  {
    out << "lat_error: ";
    rosidl_generator_traits::value_to_yaml(msg.lat_error, out);
    out << ", ";
  }

  // member: lon_error
  {
    out << "lon_error: ";
    rosidl_generator_traits::value_to_yaml(msg.lon_error, out);
    out << ", ";
  }

  // member: speed_error
  {
    out << "speed_error: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_error, out);
    out << ", ";
  }

  // member: head_error_deg
  {
    out << "head_error_deg: ";
    rosidl_generator_traits::value_to_yaml(msg.head_error_deg, out);
    out << ", ";
  }

  // member: steer_frontfeed
  {
    out << "steer_frontfeed: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_frontfeed, out);
    out << ", ";
  }

  // member: steer_feedback
  {
    out << "steer_feedback: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_feedback, out);
    out << ", ";
  }

  // member: steer_item_lat
  {
    out << "steer_item_lat: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_lat, out);
    out << ", ";
  }

  // member: steer_item_head
  {
    out << "steer_item_head: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_head, out);
    out << ", ";
  }

  // member: steer_item_head_rate
  {
    out << "steer_item_head_rate: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_head_rate, out);
    out << ", ";
  }

  // member: steer_item_total
  {
    out << "steer_item_total: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_total, out);
    out << ", ";
  }

  // member: steer_angle_cmd
  {
    out << "steer_angle_cmd: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_angle_cmd, out);
    out << ", ";
  }

  // member: steer_angle_real
  {
    out << "steer_angle_real: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_angle_real, out);
    out << ", ";
  }

  // member: state_received_loc
  {
    out << "state_received_loc: ";
    rosidl_generator_traits::value_to_yaml(msg.state_received_loc, out);
    out << ", ";
  }

  // member: state_received_traj
  {
    out << "state_received_traj: ";
    rosidl_generator_traits::value_to_yaml(msg.state_received_traj, out);
    out << ", ";
  }

  // member: state_received_chassis
  {
    out << "state_received_chassis: ";
    rosidl_generator_traits::value_to_yaml(msg.state_received_chassis, out);
    out << ", ";
  }

  // member: state_send_control
  {
    out << "state_send_control: ";
    rosidl_generator_traits::value_to_yaml(msg.state_send_control, out);
    out << ", ";
  }

  // member: curvature
  {
    out << "curvature: ";
    rosidl_generator_traits::value_to_yaml(msg.curvature, out);
    out << ", ";
  }

  // member: vehicle_yawrate
  {
    out << "vehicle_yawrate: ";
    rosidl_generator_traits::value_to_yaml(msg.vehicle_yawrate, out);
    out << ", ";
  }

  // member: loc_current_x
  {
    out << "loc_current_x: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_current_x, out);
    out << ", ";
  }

  // member: loc_current_y
  {
    out << "loc_current_y: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_current_y, out);
    out << ", ";
  }

  // member: loc_matched_x
  {
    out << "loc_matched_x: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_matched_x, out);
    out << ", ";
  }

  // member: loc_matched_y
  {
    out << "loc_matched_y: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_matched_y, out);
    out << ", ";
  }

  // member: equal_k1
  {
    out << "equal_k1: ";
    rosidl_generator_traits::value_to_yaml(msg.equal_k1, out);
    out << ", ";
  }

  // member: equal_k2
  {
    out << "equal_k2: ";
    rosidl_generator_traits::value_to_yaml(msg.equal_k2, out);
    out << ", ";
  }

  // member: equal_k3
  {
    out << "equal_k3: ";
    rosidl_generator_traits::value_to_yaml(msg.equal_k3, out);
    out << ", ";
  }

  // member: longitude_target_speed
  {
    out << "longitude_target_speed: ";
    rosidl_generator_traits::value_to_yaml(msg.longitude_target_speed, out);
    out << ", ";
  }

  // member: longitude_acc_req
  {
    out << "longitude_acc_req: ";
    rosidl_generator_traits::value_to_yaml(msg.longitude_acc_req, out);
    out << ", ";
  }

  // member: frontfeed_normal
  {
    out << "frontfeed_normal: ";
    rosidl_generator_traits::value_to_yaml(msg.frontfeed_normal, out);
    out << ", ";
  }

  // member: frontfeed_kv
  {
    out << "frontfeed_kv: ";
    rosidl_generator_traits::value_to_yaml(msg.frontfeed_kv, out);
    out << ", ";
  }

  // member: frontfeed_e2ss
  {
    out << "frontfeed_e2ss: ";
    rosidl_generator_traits::value_to_yaml(msg.frontfeed_e2ss, out);
    out << ", ";
  }

  // member: preview_acceleration_reference
  {
    out << "preview_acceleration_reference: ";
    rosidl_generator_traits::value_to_yaml(msg.preview_acceleration_reference, out);
    out << ", ";
  }

  // member: acceleration_cmd_closeloop
  {
    out << "acceleration_cmd_closeloop: ";
    rosidl_generator_traits::value_to_yaml(msg.acceleration_cmd_closeloop, out);
    out << ", ";
  }

  // member: reserved0
  {
    if (msg.reserved0.size() == 0) {
      out << "reserved0: []";
    } else {
      out << "reserved0: [";
      size_t pending_items = msg.reserved0.size();
      for (auto item : msg.reserved0) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: reserved0_size
  {
    out << "reserved0_size: ";
    rosidl_generator_traits::value_to_yaml(msg.reserved0_size, out);
    out << ", ";
  }

  // member: reserved1
  {
    if (msg.reserved1.size() == 0) {
      out << "reserved1: []";
    } else {
      out << "reserved1: [";
      size_t pending_items = msg.reserved1.size();
      for (auto item : msg.reserved1) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: reserved1_size
  {
    out << "reserved1_size: ";
    rosidl_generator_traits::value_to_yaml(msg.reserved1_size, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const ControlDebug & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: lat_error
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lat_error: ";
    rosidl_generator_traits::value_to_yaml(msg.lat_error, out);
    out << "\n";
  }

  // member: lon_error
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lon_error: ";
    rosidl_generator_traits::value_to_yaml(msg.lon_error, out);
    out << "\n";
  }

  // member: speed_error
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed_error: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_error, out);
    out << "\n";
  }

  // member: head_error_deg
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "head_error_deg: ";
    rosidl_generator_traits::value_to_yaml(msg.head_error_deg, out);
    out << "\n";
  }

  // member: steer_frontfeed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_frontfeed: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_frontfeed, out);
    out << "\n";
  }

  // member: steer_feedback
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_feedback: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_feedback, out);
    out << "\n";
  }

  // member: steer_item_lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_item_lat: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_lat, out);
    out << "\n";
  }

  // member: steer_item_head
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_item_head: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_head, out);
    out << "\n";
  }

  // member: steer_item_head_rate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_item_head_rate: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_head_rate, out);
    out << "\n";
  }

  // member: steer_item_total
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_item_total: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_item_total, out);
    out << "\n";
  }

  // member: steer_angle_cmd
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_angle_cmd: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_angle_cmd, out);
    out << "\n";
  }

  // member: steer_angle_real
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steer_angle_real: ";
    rosidl_generator_traits::value_to_yaml(msg.steer_angle_real, out);
    out << "\n";
  }

  // member: state_received_loc
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "state_received_loc: ";
    rosidl_generator_traits::value_to_yaml(msg.state_received_loc, out);
    out << "\n";
  }

  // member: state_received_traj
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "state_received_traj: ";
    rosidl_generator_traits::value_to_yaml(msg.state_received_traj, out);
    out << "\n";
  }

  // member: state_received_chassis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "state_received_chassis: ";
    rosidl_generator_traits::value_to_yaml(msg.state_received_chassis, out);
    out << "\n";
  }

  // member: state_send_control
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "state_send_control: ";
    rosidl_generator_traits::value_to_yaml(msg.state_send_control, out);
    out << "\n";
  }

  // member: curvature
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "curvature: ";
    rosidl_generator_traits::value_to_yaml(msg.curvature, out);
    out << "\n";
  }

  // member: vehicle_yawrate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "vehicle_yawrate: ";
    rosidl_generator_traits::value_to_yaml(msg.vehicle_yawrate, out);
    out << "\n";
  }

  // member: loc_current_x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "loc_current_x: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_current_x, out);
    out << "\n";
  }

  // member: loc_current_y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "loc_current_y: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_current_y, out);
    out << "\n";
  }

  // member: loc_matched_x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "loc_matched_x: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_matched_x, out);
    out << "\n";
  }

  // member: loc_matched_y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "loc_matched_y: ";
    rosidl_generator_traits::value_to_yaml(msg.loc_matched_y, out);
    out << "\n";
  }

  // member: equal_k1
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "equal_k1: ";
    rosidl_generator_traits::value_to_yaml(msg.equal_k1, out);
    out << "\n";
  }

  // member: equal_k2
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "equal_k2: ";
    rosidl_generator_traits::value_to_yaml(msg.equal_k2, out);
    out << "\n";
  }

  // member: equal_k3
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "equal_k3: ";
    rosidl_generator_traits::value_to_yaml(msg.equal_k3, out);
    out << "\n";
  }

  // member: longitude_target_speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "longitude_target_speed: ";
    rosidl_generator_traits::value_to_yaml(msg.longitude_target_speed, out);
    out << "\n";
  }

  // member: longitude_acc_req
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "longitude_acc_req: ";
    rosidl_generator_traits::value_to_yaml(msg.longitude_acc_req, out);
    out << "\n";
  }

  // member: frontfeed_normal
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "frontfeed_normal: ";
    rosidl_generator_traits::value_to_yaml(msg.frontfeed_normal, out);
    out << "\n";
  }

  // member: frontfeed_kv
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "frontfeed_kv: ";
    rosidl_generator_traits::value_to_yaml(msg.frontfeed_kv, out);
    out << "\n";
  }

  // member: frontfeed_e2ss
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "frontfeed_e2ss: ";
    rosidl_generator_traits::value_to_yaml(msg.frontfeed_e2ss, out);
    out << "\n";
  }

  // member: preview_acceleration_reference
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "preview_acceleration_reference: ";
    rosidl_generator_traits::value_to_yaml(msg.preview_acceleration_reference, out);
    out << "\n";
  }

  // member: acceleration_cmd_closeloop
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "acceleration_cmd_closeloop: ";
    rosidl_generator_traits::value_to_yaml(msg.acceleration_cmd_closeloop, out);
    out << "\n";
  }

  // member: reserved0
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.reserved0.size() == 0) {
      out << "reserved0: []\n";
    } else {
      out << "reserved0:\n";
      for (auto item : msg.reserved0) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: reserved0_size
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "reserved0_size: ";
    rosidl_generator_traits::value_to_yaml(msg.reserved0_size, out);
    out << "\n";
  }

  // member: reserved1
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.reserved1.size() == 0) {
      out << "reserved1: []\n";
    } else {
      out << "reserved1:\n";
      for (auto item : msg.reserved1) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: reserved1_size
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "reserved1_size: ";
    rosidl_generator_traits::value_to_yaml(msg.reserved1_size, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const ControlDebug & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hv_control_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hv_control_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hv_control_msgs::msg::ControlDebug & msg,
  std::ostream & out, size_t indentation = 0)
{
  hv_control_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hv_control_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hv_control_msgs::msg::ControlDebug & msg)
{
  return hv_control_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hv_control_msgs::msg::ControlDebug>()
{
  return "hv_control_msgs::msg::ControlDebug";
}

template<>
inline const char * name<hv_control_msgs::msg::ControlDebug>()
{
  return "hv_control_msgs/msg/ControlDebug";
}

template<>
struct has_fixed_size<hv_control_msgs::msg::ControlDebug>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<hv_control_msgs::msg::ControlDebug>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<hv_control_msgs::msg::ControlDebug>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HV_CONTROL_MSGS__MSG__DETAIL__CONTROL_DEBUG__TRAITS_HPP_
