# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hv_control_msgs:msg/ControlDebug.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'reserved0'
# Member 'reserved1'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_ControlDebug(type):
    """Metaclass of message 'ControlDebug'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hv_control_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hv_control_msgs.msg.ControlDebug')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__control_debug
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__control_debug
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__control_debug
            cls._TYPE_SUPPORT = module.type_support_msg__msg__control_debug
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__control_debug

            from hv_common_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class ControlDebug(metaclass=Metaclass_ControlDebug):
    """Message class 'ControlDebug'."""

    __slots__ = [
        '_header',
        '_lat_error',
        '_lon_error',
        '_speed_error',
        '_head_error_deg',
        '_steer_frontfeed',
        '_steer_feedback',
        '_steer_item_lat',
        '_steer_item_head',
        '_steer_item_head_rate',
        '_steer_item_total',
        '_steer_angle_cmd',
        '_steer_angle_real',
        '_state_received_loc',
        '_state_received_traj',
        '_state_received_chassis',
        '_state_send_control',
        '_curvature',
        '_vehicle_yawrate',
        '_loc_current_x',
        '_loc_current_y',
        '_loc_matched_x',
        '_loc_matched_y',
        '_equal_k1',
        '_equal_k2',
        '_equal_k3',
        '_longitude_target_speed',
        '_longitude_acc_req',
        '_frontfeed_normal',
        '_frontfeed_kv',
        '_frontfeed_e2ss',
        '_preview_acceleration_reference',
        '_acceleration_cmd_closeloop',
        '_reserved0',
        '_reserved0_size',
        '_reserved1',
        '_reserved1_size',
    ]

    _fields_and_field_types = {
        'header': 'hv_common_msgs/Header',
        'lat_error': 'double',
        'lon_error': 'double',
        'speed_error': 'double',
        'head_error_deg': 'double',
        'steer_frontfeed': 'double',
        'steer_feedback': 'double',
        'steer_item_lat': 'double',
        'steer_item_head': 'double',
        'steer_item_head_rate': 'double',
        'steer_item_total': 'double',
        'steer_angle_cmd': 'double',
        'steer_angle_real': 'double',
        'state_received_loc': 'int32',
        'state_received_traj': 'int32',
        'state_received_chassis': 'int32',
        'state_send_control': 'int32',
        'curvature': 'double',
        'vehicle_yawrate': 'double',
        'loc_current_x': 'double',
        'loc_current_y': 'double',
        'loc_matched_x': 'double',
        'loc_matched_y': 'double',
        'equal_k1': 'double',
        'equal_k2': 'double',
        'equal_k3': 'double',
        'longitude_target_speed': 'double',
        'longitude_acc_req': 'double',
        'frontfeed_normal': 'double',
        'frontfeed_kv': 'double',
        'frontfeed_e2ss': 'double',
        'preview_acceleration_reference': 'double',
        'acceleration_cmd_closeloop': 'double',
        'reserved0': 'sequence<int32>',
        'reserved0_size': 'int32',
        'reserved1': 'sequence<double>',
        'reserved1_size': 'int32',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['hv_common_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('int32')),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('double')),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from hv_common_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.lat_error = kwargs.get('lat_error', float())
        self.lon_error = kwargs.get('lon_error', float())
        self.speed_error = kwargs.get('speed_error', float())
        self.head_error_deg = kwargs.get('head_error_deg', float())
        self.steer_frontfeed = kwargs.get('steer_frontfeed', float())
        self.steer_feedback = kwargs.get('steer_feedback', float())
        self.steer_item_lat = kwargs.get('steer_item_lat', float())
        self.steer_item_head = kwargs.get('steer_item_head', float())
        self.steer_item_head_rate = kwargs.get('steer_item_head_rate', float())
        self.steer_item_total = kwargs.get('steer_item_total', float())
        self.steer_angle_cmd = kwargs.get('steer_angle_cmd', float())
        self.steer_angle_real = kwargs.get('steer_angle_real', float())
        self.state_received_loc = kwargs.get('state_received_loc', int())
        self.state_received_traj = kwargs.get('state_received_traj', int())
        self.state_received_chassis = kwargs.get('state_received_chassis', int())
        self.state_send_control = kwargs.get('state_send_control', int())
        self.curvature = kwargs.get('curvature', float())
        self.vehicle_yawrate = kwargs.get('vehicle_yawrate', float())
        self.loc_current_x = kwargs.get('loc_current_x', float())
        self.loc_current_y = kwargs.get('loc_current_y', float())
        self.loc_matched_x = kwargs.get('loc_matched_x', float())
        self.loc_matched_y = kwargs.get('loc_matched_y', float())
        self.equal_k1 = kwargs.get('equal_k1', float())
        self.equal_k2 = kwargs.get('equal_k2', float())
        self.equal_k3 = kwargs.get('equal_k3', float())
        self.longitude_target_speed = kwargs.get('longitude_target_speed', float())
        self.longitude_acc_req = kwargs.get('longitude_acc_req', float())
        self.frontfeed_normal = kwargs.get('frontfeed_normal', float())
        self.frontfeed_kv = kwargs.get('frontfeed_kv', float())
        self.frontfeed_e2ss = kwargs.get('frontfeed_e2ss', float())
        self.preview_acceleration_reference = kwargs.get('preview_acceleration_reference', float())
        self.acceleration_cmd_closeloop = kwargs.get('acceleration_cmd_closeloop', float())
        self.reserved0 = array.array('i', kwargs.get('reserved0', []))
        self.reserved0_size = kwargs.get('reserved0_size', int())
        self.reserved1 = array.array('d', kwargs.get('reserved1', []))
        self.reserved1_size = kwargs.get('reserved1_size', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.lat_error != other.lat_error:
            return False
        if self.lon_error != other.lon_error:
            return False
        if self.speed_error != other.speed_error:
            return False
        if self.head_error_deg != other.head_error_deg:
            return False
        if self.steer_frontfeed != other.steer_frontfeed:
            return False
        if self.steer_feedback != other.steer_feedback:
            return False
        if self.steer_item_lat != other.steer_item_lat:
            return False
        if self.steer_item_head != other.steer_item_head:
            return False
        if self.steer_item_head_rate != other.steer_item_head_rate:
            return False
        if self.steer_item_total != other.steer_item_total:
            return False
        if self.steer_angle_cmd != other.steer_angle_cmd:
            return False
        if self.steer_angle_real != other.steer_angle_real:
            return False
        if self.state_received_loc != other.state_received_loc:
            return False
        if self.state_received_traj != other.state_received_traj:
            return False
        if self.state_received_chassis != other.state_received_chassis:
            return False
        if self.state_send_control != other.state_send_control:
            return False
        if self.curvature != other.curvature:
            return False
        if self.vehicle_yawrate != other.vehicle_yawrate:
            return False
        if self.loc_current_x != other.loc_current_x:
            return False
        if self.loc_current_y != other.loc_current_y:
            return False
        if self.loc_matched_x != other.loc_matched_x:
            return False
        if self.loc_matched_y != other.loc_matched_y:
            return False
        if self.equal_k1 != other.equal_k1:
            return False
        if self.equal_k2 != other.equal_k2:
            return False
        if self.equal_k3 != other.equal_k3:
            return False
        if self.longitude_target_speed != other.longitude_target_speed:
            return False
        if self.longitude_acc_req != other.longitude_acc_req:
            return False
        if self.frontfeed_normal != other.frontfeed_normal:
            return False
        if self.frontfeed_kv != other.frontfeed_kv:
            return False
        if self.frontfeed_e2ss != other.frontfeed_e2ss:
            return False
        if self.preview_acceleration_reference != other.preview_acceleration_reference:
            return False
        if self.acceleration_cmd_closeloop != other.acceleration_cmd_closeloop:
            return False
        if self.reserved0 != other.reserved0:
            return False
        if self.reserved0_size != other.reserved0_size:
            return False
        if self.reserved1 != other.reserved1:
            return False
        if self.reserved1_size != other.reserved1_size:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if __debug__:
            from hv_common_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def lat_error(self):
        """Message field 'lat_error'."""
        return self._lat_error

    @lat_error.setter
    def lat_error(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lat_error' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lat_error' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lat_error = value

    @builtins.property
    def lon_error(self):
        """Message field 'lon_error'."""
        return self._lon_error

    @lon_error.setter
    def lon_error(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lon_error' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lon_error' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lon_error = value

    @builtins.property
    def speed_error(self):
        """Message field 'speed_error'."""
        return self._speed_error

    @speed_error.setter
    def speed_error(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed_error' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'speed_error' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._speed_error = value

    @builtins.property
    def head_error_deg(self):
        """Message field 'head_error_deg'."""
        return self._head_error_deg

    @head_error_deg.setter
    def head_error_deg(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'head_error_deg' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'head_error_deg' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._head_error_deg = value

    @builtins.property
    def steer_frontfeed(self):
        """Message field 'steer_frontfeed'."""
        return self._steer_frontfeed

    @steer_frontfeed.setter
    def steer_frontfeed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_frontfeed' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_frontfeed' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_frontfeed = value

    @builtins.property
    def steer_feedback(self):
        """Message field 'steer_feedback'."""
        return self._steer_feedback

    @steer_feedback.setter
    def steer_feedback(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_feedback' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_feedback' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_feedback = value

    @builtins.property
    def steer_item_lat(self):
        """Message field 'steer_item_lat'."""
        return self._steer_item_lat

    @steer_item_lat.setter
    def steer_item_lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_item_lat' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_item_lat' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_item_lat = value

    @builtins.property
    def steer_item_head(self):
        """Message field 'steer_item_head'."""
        return self._steer_item_head

    @steer_item_head.setter
    def steer_item_head(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_item_head' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_item_head' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_item_head = value

    @builtins.property
    def steer_item_head_rate(self):
        """Message field 'steer_item_head_rate'."""
        return self._steer_item_head_rate

    @steer_item_head_rate.setter
    def steer_item_head_rate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_item_head_rate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_item_head_rate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_item_head_rate = value

    @builtins.property
    def steer_item_total(self):
        """Message field 'steer_item_total'."""
        return self._steer_item_total

    @steer_item_total.setter
    def steer_item_total(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_item_total' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_item_total' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_item_total = value

    @builtins.property
    def steer_angle_cmd(self):
        """Message field 'steer_angle_cmd'."""
        return self._steer_angle_cmd

    @steer_angle_cmd.setter
    def steer_angle_cmd(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_angle_cmd' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_angle_cmd' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_angle_cmd = value

    @builtins.property
    def steer_angle_real(self):
        """Message field 'steer_angle_real'."""
        return self._steer_angle_real

    @steer_angle_real.setter
    def steer_angle_real(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'steer_angle_real' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'steer_angle_real' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._steer_angle_real = value

    @builtins.property
    def state_received_loc(self):
        """Message field 'state_received_loc'."""
        return self._state_received_loc

    @state_received_loc.setter
    def state_received_loc(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'state_received_loc' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'state_received_loc' field must be an integer in [-2147483648, 2147483647]"
        self._state_received_loc = value

    @builtins.property
    def state_received_traj(self):
        """Message field 'state_received_traj'."""
        return self._state_received_traj

    @state_received_traj.setter
    def state_received_traj(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'state_received_traj' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'state_received_traj' field must be an integer in [-2147483648, 2147483647]"
        self._state_received_traj = value

    @builtins.property
    def state_received_chassis(self):
        """Message field 'state_received_chassis'."""
        return self._state_received_chassis

    @state_received_chassis.setter
    def state_received_chassis(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'state_received_chassis' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'state_received_chassis' field must be an integer in [-2147483648, 2147483647]"
        self._state_received_chassis = value

    @builtins.property
    def state_send_control(self):
        """Message field 'state_send_control'."""
        return self._state_send_control

    @state_send_control.setter
    def state_send_control(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'state_send_control' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'state_send_control' field must be an integer in [-2147483648, 2147483647]"
        self._state_send_control = value

    @builtins.property
    def curvature(self):
        """Message field 'curvature'."""
        return self._curvature

    @curvature.setter
    def curvature(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'curvature' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'curvature' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._curvature = value

    @builtins.property
    def vehicle_yawrate(self):
        """Message field 'vehicle_yawrate'."""
        return self._vehicle_yawrate

    @vehicle_yawrate.setter
    def vehicle_yawrate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'vehicle_yawrate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'vehicle_yawrate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._vehicle_yawrate = value

    @builtins.property
    def loc_current_x(self):
        """Message field 'loc_current_x'."""
        return self._loc_current_x

    @loc_current_x.setter
    def loc_current_x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'loc_current_x' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'loc_current_x' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._loc_current_x = value

    @builtins.property
    def loc_current_y(self):
        """Message field 'loc_current_y'."""
        return self._loc_current_y

    @loc_current_y.setter
    def loc_current_y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'loc_current_y' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'loc_current_y' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._loc_current_y = value

    @builtins.property
    def loc_matched_x(self):
        """Message field 'loc_matched_x'."""
        return self._loc_matched_x

    @loc_matched_x.setter
    def loc_matched_x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'loc_matched_x' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'loc_matched_x' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._loc_matched_x = value

    @builtins.property
    def loc_matched_y(self):
        """Message field 'loc_matched_y'."""
        return self._loc_matched_y

    @loc_matched_y.setter
    def loc_matched_y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'loc_matched_y' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'loc_matched_y' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._loc_matched_y = value

    @builtins.property
    def equal_k1(self):
        """Message field 'equal_k1'."""
        return self._equal_k1

    @equal_k1.setter
    def equal_k1(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'equal_k1' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'equal_k1' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._equal_k1 = value

    @builtins.property
    def equal_k2(self):
        """Message field 'equal_k2'."""
        return self._equal_k2

    @equal_k2.setter
    def equal_k2(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'equal_k2' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'equal_k2' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._equal_k2 = value

    @builtins.property
    def equal_k3(self):
        """Message field 'equal_k3'."""
        return self._equal_k3

    @equal_k3.setter
    def equal_k3(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'equal_k3' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'equal_k3' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._equal_k3 = value

    @builtins.property
    def longitude_target_speed(self):
        """Message field 'longitude_target_speed'."""
        return self._longitude_target_speed

    @longitude_target_speed.setter
    def longitude_target_speed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'longitude_target_speed' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'longitude_target_speed' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._longitude_target_speed = value

    @builtins.property
    def longitude_acc_req(self):
        """Message field 'longitude_acc_req'."""
        return self._longitude_acc_req

    @longitude_acc_req.setter
    def longitude_acc_req(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'longitude_acc_req' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'longitude_acc_req' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._longitude_acc_req = value

    @builtins.property
    def frontfeed_normal(self):
        """Message field 'frontfeed_normal'."""
        return self._frontfeed_normal

    @frontfeed_normal.setter
    def frontfeed_normal(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'frontfeed_normal' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'frontfeed_normal' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._frontfeed_normal = value

    @builtins.property
    def frontfeed_kv(self):
        """Message field 'frontfeed_kv'."""
        return self._frontfeed_kv

    @frontfeed_kv.setter
    def frontfeed_kv(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'frontfeed_kv' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'frontfeed_kv' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._frontfeed_kv = value

    @builtins.property
    def frontfeed_e2ss(self):
        """Message field 'frontfeed_e2ss'."""
        return self._frontfeed_e2ss

    @frontfeed_e2ss.setter
    def frontfeed_e2ss(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'frontfeed_e2ss' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'frontfeed_e2ss' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._frontfeed_e2ss = value

    @builtins.property
    def preview_acceleration_reference(self):
        """Message field 'preview_acceleration_reference'."""
        return self._preview_acceleration_reference

    @preview_acceleration_reference.setter
    def preview_acceleration_reference(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'preview_acceleration_reference' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'preview_acceleration_reference' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._preview_acceleration_reference = value

    @builtins.property
    def acceleration_cmd_closeloop(self):
        """Message field 'acceleration_cmd_closeloop'."""
        return self._acceleration_cmd_closeloop

    @acceleration_cmd_closeloop.setter
    def acceleration_cmd_closeloop(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'acceleration_cmd_closeloop' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'acceleration_cmd_closeloop' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._acceleration_cmd_closeloop = value

    @builtins.property
    def reserved0(self):
        """Message field 'reserved0'."""
        return self._reserved0

    @reserved0.setter
    def reserved0(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'i', \
                "The 'reserved0' array.array() must have the type code of 'i'"
            self._reserved0 = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= -2147483648 and val < 2147483648 for val in value)), \
                "The 'reserved0' field must be a set or sequence and each value of type 'int' and each integer in [-2147483648, 2147483647]"
        self._reserved0 = array.array('i', value)

    @builtins.property
    def reserved0_size(self):
        """Message field 'reserved0_size'."""
        return self._reserved0_size

    @reserved0_size.setter
    def reserved0_size(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'reserved0_size' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'reserved0_size' field must be an integer in [-2147483648, 2147483647]"
        self._reserved0_size = value

    @builtins.property
    def reserved1(self):
        """Message field 'reserved1'."""
        return self._reserved1

    @reserved1.setter
    def reserved1(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'd', \
                "The 'reserved1' array.array() must have the type code of 'd'"
            self._reserved1 = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, float) for v in value) and
                 all(not (val < -1.7976931348623157e+308 or val > 1.7976931348623157e+308) or math.isinf(val) for val in value)), \
                "The 'reserved1' field must be a set or sequence and each value of type 'float' and each double in [-179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000, 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000]"
        self._reserved1 = array.array('d', value)

    @builtins.property
    def reserved1_size(self):
        """Message field 'reserved1_size'."""
        return self._reserved1_size

    @reserved1_size.setter
    def reserved1_size(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'reserved1_size' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'reserved1_size' field must be an integer in [-2147483648, 2147483647]"
        self._reserved1_size = value
