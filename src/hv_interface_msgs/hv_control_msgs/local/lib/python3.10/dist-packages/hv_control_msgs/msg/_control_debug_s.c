// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hv_control_msgs:msg/ControlDebug.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hv_control_msgs/msg/detail/control_debug__struct.h"
#include "hv_control_msgs/msg/detail/control_debug__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool hv_common_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * hv_common_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hv_control_msgs__msg__control_debug__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[48];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hv_control_msgs.msg._control_debug.ControlDebug", full_classname_dest, 47) == 0);
  }
  hv_control_msgs__msg__ControlDebug * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!hv_common_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // lat_error
    PyObject * field = PyObject_GetAttrString(_pymsg, "lat_error");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lat_error = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lon_error
    PyObject * field = PyObject_GetAttrString(_pymsg, "lon_error");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lon_error = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speed_error
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed_error");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed_error = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // head_error_deg
    PyObject * field = PyObject_GetAttrString(_pymsg, "head_error_deg");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->head_error_deg = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_frontfeed
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_frontfeed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_frontfeed = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_feedback
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_feedback");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_feedback = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_item_lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_item_lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_item_lat = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_item_head
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_item_head");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_item_head = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_item_head_rate
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_item_head_rate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_item_head_rate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_item_total
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_item_total");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_item_total = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_angle_cmd
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_angle_cmd");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_angle_cmd = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // steer_angle_real
    PyObject * field = PyObject_GetAttrString(_pymsg, "steer_angle_real");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->steer_angle_real = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // state_received_loc
    PyObject * field = PyObject_GetAttrString(_pymsg, "state_received_loc");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->state_received_loc = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // state_received_traj
    PyObject * field = PyObject_GetAttrString(_pymsg, "state_received_traj");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->state_received_traj = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // state_received_chassis
    PyObject * field = PyObject_GetAttrString(_pymsg, "state_received_chassis");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->state_received_chassis = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // state_send_control
    PyObject * field = PyObject_GetAttrString(_pymsg, "state_send_control");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->state_send_control = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // curvature
    PyObject * field = PyObject_GetAttrString(_pymsg, "curvature");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->curvature = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // vehicle_yawrate
    PyObject * field = PyObject_GetAttrString(_pymsg, "vehicle_yawrate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->vehicle_yawrate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // loc_current_x
    PyObject * field = PyObject_GetAttrString(_pymsg, "loc_current_x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->loc_current_x = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // loc_current_y
    PyObject * field = PyObject_GetAttrString(_pymsg, "loc_current_y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->loc_current_y = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // loc_matched_x
    PyObject * field = PyObject_GetAttrString(_pymsg, "loc_matched_x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->loc_matched_x = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // loc_matched_y
    PyObject * field = PyObject_GetAttrString(_pymsg, "loc_matched_y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->loc_matched_y = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // equal_k1
    PyObject * field = PyObject_GetAttrString(_pymsg, "equal_k1");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->equal_k1 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // equal_k2
    PyObject * field = PyObject_GetAttrString(_pymsg, "equal_k2");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->equal_k2 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // equal_k3
    PyObject * field = PyObject_GetAttrString(_pymsg, "equal_k3");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->equal_k3 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // longitude_target_speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "longitude_target_speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->longitude_target_speed = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // longitude_acc_req
    PyObject * field = PyObject_GetAttrString(_pymsg, "longitude_acc_req");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->longitude_acc_req = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // frontfeed_normal
    PyObject * field = PyObject_GetAttrString(_pymsg, "frontfeed_normal");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->frontfeed_normal = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // frontfeed_kv
    PyObject * field = PyObject_GetAttrString(_pymsg, "frontfeed_kv");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->frontfeed_kv = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // frontfeed_e2ss
    PyObject * field = PyObject_GetAttrString(_pymsg, "frontfeed_e2ss");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->frontfeed_e2ss = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // preview_acceleration_reference
    PyObject * field = PyObject_GetAttrString(_pymsg, "preview_acceleration_reference");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->preview_acceleration_reference = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // acceleration_cmd_closeloop
    PyObject * field = PyObject_GetAttrString(_pymsg, "acceleration_cmd_closeloop");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->acceleration_cmd_closeloop = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // reserved0
    PyObject * field = PyObject_GetAttrString(_pymsg, "reserved0");
    if (!field) {
      return false;
    }
    if (PyObject_CheckBuffer(field)) {
      // Optimization for converting arrays of primitives
      Py_buffer view;
      int rc = PyObject_GetBuffer(field, &view, PyBUF_SIMPLE);
      if (rc < 0) {
        Py_DECREF(field);
        return false;
      }
      Py_ssize_t size = view.len / sizeof(int32_t);
      if (!rosidl_runtime_c__int32__Sequence__init(&(ros_message->reserved0), size)) {
        PyErr_SetString(PyExc_RuntimeError, "unable to create int32__Sequence ros_message");
        PyBuffer_Release(&view);
        Py_DECREF(field);
        return false;
      }
      int32_t * dest = ros_message->reserved0.data;
      rc = PyBuffer_ToContiguous(dest, &view, view.len, 'C');
      if (rc < 0) {
        PyBuffer_Release(&view);
        Py_DECREF(field);
        return false;
      }
      PyBuffer_Release(&view);
    } else {
      PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'reserved0'");
      if (!seq_field) {
        Py_DECREF(field);
        return false;
      }
      Py_ssize_t size = PySequence_Size(field);
      if (-1 == size) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      if (!rosidl_runtime_c__int32__Sequence__init(&(ros_message->reserved0), size)) {
        PyErr_SetString(PyExc_RuntimeError, "unable to create int32__Sequence ros_message");
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      int32_t * dest = ros_message->reserved0.data;
      for (Py_ssize_t i = 0; i < size; ++i) {
        PyObject * item = PySequence_Fast_GET_ITEM(seq_field, i);
        if (!item) {
          Py_DECREF(seq_field);
          Py_DECREF(field);
          return false;
        }
        assert(PyLong_Check(item));
        int32_t tmp = (int32_t)PyLong_AsLong(item);
        memcpy(&dest[i], &tmp, sizeof(int32_t));
      }
      Py_DECREF(seq_field);
    }
    Py_DECREF(field);
  }
  {  // reserved0_size
    PyObject * field = PyObject_GetAttrString(_pymsg, "reserved0_size");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->reserved0_size = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // reserved1
    PyObject * field = PyObject_GetAttrString(_pymsg, "reserved1");
    if (!field) {
      return false;
    }
    if (PyObject_CheckBuffer(field)) {
      // Optimization for converting arrays of primitives
      Py_buffer view;
      int rc = PyObject_GetBuffer(field, &view, PyBUF_SIMPLE);
      if (rc < 0) {
        Py_DECREF(field);
        return false;
      }
      Py_ssize_t size = view.len / sizeof(double);
      if (!rosidl_runtime_c__double__Sequence__init(&(ros_message->reserved1), size)) {
        PyErr_SetString(PyExc_RuntimeError, "unable to create double__Sequence ros_message");
        PyBuffer_Release(&view);
        Py_DECREF(field);
        return false;
      }
      double * dest = ros_message->reserved1.data;
      rc = PyBuffer_ToContiguous(dest, &view, view.len, 'C');
      if (rc < 0) {
        PyBuffer_Release(&view);
        Py_DECREF(field);
        return false;
      }
      PyBuffer_Release(&view);
    } else {
      PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'reserved1'");
      if (!seq_field) {
        Py_DECREF(field);
        return false;
      }
      Py_ssize_t size = PySequence_Size(field);
      if (-1 == size) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      if (!rosidl_runtime_c__double__Sequence__init(&(ros_message->reserved1), size)) {
        PyErr_SetString(PyExc_RuntimeError, "unable to create double__Sequence ros_message");
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
      double * dest = ros_message->reserved1.data;
      for (Py_ssize_t i = 0; i < size; ++i) {
        PyObject * item = PySequence_Fast_GET_ITEM(seq_field, i);
        if (!item) {
          Py_DECREF(seq_field);
          Py_DECREF(field);
          return false;
        }
        assert(PyFloat_Check(item));
        double tmp = PyFloat_AS_DOUBLE(item);
        memcpy(&dest[i], &tmp, sizeof(double));
      }
      Py_DECREF(seq_field);
    }
    Py_DECREF(field);
  }
  {  // reserved1_size
    PyObject * field = PyObject_GetAttrString(_pymsg, "reserved1_size");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->reserved1_size = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hv_control_msgs__msg__control_debug__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of ControlDebug */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hv_control_msgs.msg._control_debug");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "ControlDebug");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hv_control_msgs__msg__ControlDebug * ros_message = (hv_control_msgs__msg__ControlDebug *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = hv_common_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lat_error
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lat_error);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lat_error", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lon_error
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lon_error);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lon_error", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed_error
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed_error);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed_error", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // head_error_deg
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->head_error_deg);
    {
      int rc = PyObject_SetAttrString(_pymessage, "head_error_deg", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_frontfeed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_frontfeed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_frontfeed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_feedback
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_feedback);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_feedback", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_item_lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_item_lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_item_lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_item_head
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_item_head);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_item_head", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_item_head_rate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_item_head_rate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_item_head_rate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_item_total
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_item_total);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_item_total", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_angle_cmd
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_angle_cmd);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_angle_cmd", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steer_angle_real
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->steer_angle_real);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steer_angle_real", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // state_received_loc
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->state_received_loc);
    {
      int rc = PyObject_SetAttrString(_pymessage, "state_received_loc", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // state_received_traj
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->state_received_traj);
    {
      int rc = PyObject_SetAttrString(_pymessage, "state_received_traj", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // state_received_chassis
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->state_received_chassis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "state_received_chassis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // state_send_control
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->state_send_control);
    {
      int rc = PyObject_SetAttrString(_pymessage, "state_send_control", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // curvature
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->curvature);
    {
      int rc = PyObject_SetAttrString(_pymessage, "curvature", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // vehicle_yawrate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->vehicle_yawrate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "vehicle_yawrate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // loc_current_x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->loc_current_x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "loc_current_x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // loc_current_y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->loc_current_y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "loc_current_y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // loc_matched_x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->loc_matched_x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "loc_matched_x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // loc_matched_y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->loc_matched_y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "loc_matched_y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // equal_k1
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->equal_k1);
    {
      int rc = PyObject_SetAttrString(_pymessage, "equal_k1", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // equal_k2
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->equal_k2);
    {
      int rc = PyObject_SetAttrString(_pymessage, "equal_k2", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // equal_k3
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->equal_k3);
    {
      int rc = PyObject_SetAttrString(_pymessage, "equal_k3", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // longitude_target_speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->longitude_target_speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "longitude_target_speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // longitude_acc_req
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->longitude_acc_req);
    {
      int rc = PyObject_SetAttrString(_pymessage, "longitude_acc_req", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // frontfeed_normal
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->frontfeed_normal);
    {
      int rc = PyObject_SetAttrString(_pymessage, "frontfeed_normal", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // frontfeed_kv
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->frontfeed_kv);
    {
      int rc = PyObject_SetAttrString(_pymessage, "frontfeed_kv", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // frontfeed_e2ss
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->frontfeed_e2ss);
    {
      int rc = PyObject_SetAttrString(_pymessage, "frontfeed_e2ss", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // preview_acceleration_reference
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->preview_acceleration_reference);
    {
      int rc = PyObject_SetAttrString(_pymessage, "preview_acceleration_reference", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // acceleration_cmd_closeloop
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->acceleration_cmd_closeloop);
    {
      int rc = PyObject_SetAttrString(_pymessage, "acceleration_cmd_closeloop", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // reserved0
    PyObject * field = NULL;
    field = PyObject_GetAttrString(_pymessage, "reserved0");
    if (!field) {
      return NULL;
    }
    assert(field->ob_type != NULL);
    assert(field->ob_type->tp_name != NULL);
    assert(strcmp(field->ob_type->tp_name, "array.array") == 0);
    // ensure that itemsize matches the sizeof of the ROS message field
    PyObject * itemsize_attr = PyObject_GetAttrString(field, "itemsize");
    assert(itemsize_attr != NULL);
    size_t itemsize = PyLong_AsSize_t(itemsize_attr);
    Py_DECREF(itemsize_attr);
    if (itemsize != sizeof(int32_t)) {
      PyErr_SetString(PyExc_RuntimeError, "itemsize doesn't match expectation");
      Py_DECREF(field);
      return NULL;
    }
    // clear the array, poor approach to remove potential default values
    Py_ssize_t length = PyObject_Length(field);
    if (-1 == length) {
      Py_DECREF(field);
      return NULL;
    }
    if (length > 0) {
      PyObject * pop = PyObject_GetAttrString(field, "pop");
      assert(pop != NULL);
      for (Py_ssize_t i = 0; i < length; ++i) {
        PyObject * ret = PyObject_CallFunctionObjArgs(pop, NULL);
        if (!ret) {
          Py_DECREF(pop);
          Py_DECREF(field);
          return NULL;
        }
        Py_DECREF(ret);
      }
      Py_DECREF(pop);
    }
    if (ros_message->reserved0.size > 0) {
      // populating the array.array using the frombytes method
      PyObject * frombytes = PyObject_GetAttrString(field, "frombytes");
      assert(frombytes != NULL);
      int32_t * src = &(ros_message->reserved0.data[0]);
      PyObject * data = PyBytes_FromStringAndSize((const char *)src, ros_message->reserved0.size * sizeof(int32_t));
      assert(data != NULL);
      PyObject * ret = PyObject_CallFunctionObjArgs(frombytes, data, NULL);
      Py_DECREF(data);
      Py_DECREF(frombytes);
      if (!ret) {
        Py_DECREF(field);
        return NULL;
      }
      Py_DECREF(ret);
    }
    Py_DECREF(field);
  }
  {  // reserved0_size
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->reserved0_size);
    {
      int rc = PyObject_SetAttrString(_pymessage, "reserved0_size", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // reserved1
    PyObject * field = NULL;
    field = PyObject_GetAttrString(_pymessage, "reserved1");
    if (!field) {
      return NULL;
    }
    assert(field->ob_type != NULL);
    assert(field->ob_type->tp_name != NULL);
    assert(strcmp(field->ob_type->tp_name, "array.array") == 0);
    // ensure that itemsize matches the sizeof of the ROS message field
    PyObject * itemsize_attr = PyObject_GetAttrString(field, "itemsize");
    assert(itemsize_attr != NULL);
    size_t itemsize = PyLong_AsSize_t(itemsize_attr);
    Py_DECREF(itemsize_attr);
    if (itemsize != sizeof(double)) {
      PyErr_SetString(PyExc_RuntimeError, "itemsize doesn't match expectation");
      Py_DECREF(field);
      return NULL;
    }
    // clear the array, poor approach to remove potential default values
    Py_ssize_t length = PyObject_Length(field);
    if (-1 == length) {
      Py_DECREF(field);
      return NULL;
    }
    if (length > 0) {
      PyObject * pop = PyObject_GetAttrString(field, "pop");
      assert(pop != NULL);
      for (Py_ssize_t i = 0; i < length; ++i) {
        PyObject * ret = PyObject_CallFunctionObjArgs(pop, NULL);
        if (!ret) {
          Py_DECREF(pop);
          Py_DECREF(field);
          return NULL;
        }
        Py_DECREF(ret);
      }
      Py_DECREF(pop);
    }
    if (ros_message->reserved1.size > 0) {
      // populating the array.array using the frombytes method
      PyObject * frombytes = PyObject_GetAttrString(field, "frombytes");
      assert(frombytes != NULL);
      double * src = &(ros_message->reserved1.data[0]);
      PyObject * data = PyBytes_FromStringAndSize((const char *)src, ros_message->reserved1.size * sizeof(double));
      assert(data != NULL);
      PyObject * ret = PyObject_CallFunctionObjArgs(frombytes, data, NULL);
      Py_DECREF(data);
      Py_DECREF(frombytes);
      if (!ret) {
        Py_DECREF(field);
        return NULL;
      }
      Py_DECREF(ret);
    }
    Py_DECREF(field);
  }
  {  // reserved1_size
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->reserved1_size);
    {
      int rc = PyObject_SetAttrString(_pymessage, "reserved1_size", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
