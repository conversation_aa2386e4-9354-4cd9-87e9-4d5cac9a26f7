#!/bin/bash
set -e

# --- Color Definitions ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# --- Default Settings ---
BUILD_TYPE="Debug"
TARGET_ARCH="x86_64"
CLEAN_BUILD=false

# --- Usage Function ---
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Workspace initialization script for ROS2 and Conan."
    echo "This script prepares the workspace by installing Conan dependencies."
    echo
    echo "Options:"
    echo "  -t, --type TYPE        Build type (Debug|Release) [default: Debug]"
    echo "  -a, --arch ARCH        Target architecture (x86_64|aarch64) [default: x86_64]"
    echo "  -c, --clean            Clean build, install, and log directories before initializing"
    echo "  -h, --help             Show this help message"
}

# --- Argument Parsing ---
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -a|--arch)
            TARGET_ARCH="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            usage
            exit 1
            ;;
    esac
done

# --- Parameter Validation ---
if [[ "$BUILD_TYPE" != "Debug" && "$BUILD_TYPE" != "Release" ]]; then
    echo -e "${RED}Error: Invalid build type '$BUILD_TYPE'. Must be 'Debug' or 'Release'.${NC}"
    exit 1
fi

if [[ "$TARGET_ARCH" != "x86_64" && "$TARGET_ARCH" != "aarch64" ]]; then
    echo -e "${RED}Error: Invalid architecture '$TARGET_ARCH'. Must be 'x86_64' or 'aarch64'.${NC}"
    exit 1
fi

# --- Print Build Information ---
echo -e "${GREEN}=================================================${NC}"
echo "Initializing workspace for:"
echo -e "  Architecture: ${YELLOW}${TARGET_ARCH}${NC}"
echo -e "  Build Type:   ${YELLOW}${BUILD_TYPE}${NC}"
echo -e "${GREEN}=================================================${NC}"

# --- Clean Build ---
if [[ "$CLEAN_BUILD" == true ]]; then
    echo -e "${YELLOW}Cleaning build, install, and log directories...${NC}"
    rm -rf build install log
fi

# --- Conan Remote Check ---
if ! conan remote list | grep -q "hv-conan-dev"; then
    echo -e "${YELLOW}Adding hv-conan-dev remote...${NC}"
    conan remote add hv-conan-dev https://nexus3.hellobike.cn/repository/hv-conan-dev/
else
    echo -e "${GREEN}hv-conan-dev remote already exists, skipping...${NC}"
fi

# --- Conan Install ---
BUILD_DIR="build"
mkdir -p "$BUILD_DIR"
echo -e "\n${GREEN}--> Running conan install...${NC}"
conan install . --output-folder="$BUILD_DIR" --build=missing \
    -s:h arch="$TARGET_ARCH" -s:h build_type="$BUILD_TYPE" \
    -s:b arch="$TARGET_ARCH" -s:b build_type="$BUILD_TYPE" 

# --- Completion Message ---
TOOLCHAIN_FILE="$(pwd)/${BUILD_DIR}/conan_toolchain.cmake"
CONAN_RUN_SCRIPT="$(pwd)/${BUILD_DIR}/conanrun.sh"
echo -e "\n${GREEN}=================================================${NC}"
echo -e "${GREEN}Workspace initialization complete!${NC}"
echo -e "\nTo build the workspace, run the following commands:"
echo -e "1. Source ROS2 & Conan environments: ${YELLOW}source /opt/ros/humble/setup.bash && source ${CONAN_RUN_SCRIPT}${NC}"
echo -e "2. Build all packages:"
echo -e "   ${YELLOW}colcon build --merge-install --cmake-args \"-DCMAKE_TOOLCHAIN_FILE=${TOOLCHAIN_FILE}\" \"-DCMAKE_BUILD_TYPE=${BUILD_TYPE}\" \"-DBUILD_SHARED_LIBS=ON\"${NC}"
echo -e "   or build a single package (e.g., hv_percep_base):"
echo -e "   ${YELLOW}colcon build --packages-select hv_percep_base --merge-install --cmake-args \"-DCMAKE_TOOLCHAIN_FILE=${TOOLCHAIN_FILE}\" \"-DCMAKE_BUILD_TYPE=${BUILD_TYPE}\" \"-DBUILD_SHARED_LIBS=ON\"${NC}"
echo -e "\nAfter build, to run your nodes, remember to source the full environment:"
echo -e "${YELLOW}source install/setup.bash && source ${CONAN_RUN_SCRIPT}${NC}"
echo -e "${GREEN}=================================================${NC}" 

source ${CONAN_RUN_SCRIPT}