#!/bin/bash
###
 # @Author: <EMAIL> <EMAIL>
 # @Date: 2025-07-24 21:32:53
 # @LastEditors: <EMAIL> <EMAIL>
 # @LastEditTime: 2025-07-24 21:34:37
 # @FilePath: /hv_percep_workspace/setup_run_env.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

curdir=$(pwd)

export LD_LIBRARY_PATH=/opt/TensorRT/10.8.x.x/x86_64/lib:$LD_LIBRARY_PATH
export LD_LIBRARY_PATH=/opt/libspconv/x86_64_cuda12.8/lib:$LD_LIBRARY_PATH
# export LD_LIBRARY_PATH=$curdir/install/x86_64/lib/x86_64:$LD_LIBRARY_PATH
# export LD_LIBRARY_PATH=$curdir/hv_percep_base/install/x86_64/lib/x86_64:$LD_LIBRARY_PATH

export PATH=/opt/TensorRT/10.8.x.x/x86_64/bin:$PATH
export PATH=/opt/libspconv/x86_64_cuda12.8/bin:$PATH
