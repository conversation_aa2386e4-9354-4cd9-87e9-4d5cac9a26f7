<!--
 * @Author: l<PERSON><PERSON><EMAIL> <EMAIL>
 * @Date: 2025-07-25 09:46:43
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-07-28 15:40:37
 * @FilePath: /hv_percep_workspace/README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# HV Perception Workspace

本仓库为高性能感知算法的多包C++/CUDA开发环境，支持 ROS2、Conan、Colcon 多包协同开发与构建。

## 获取代码

主仓库地址：
- hv_percep_workspace: `ssh://*************************:10022/hello_veh/hv-percp/hv_percep_deployment/hv_percep_workspace.git`

推荐拉取命令：
```bash
git clone ssh://*************************:10022/hello_veh/hv-percp/hv_percep_deployment/hv_percep_workspace.git
cd hv_percep_workspace
```

如需单独拉取/更新各软件包源码，可在 `src/` 目录下执行：
```bash
cd src
# 拉取/更新 hv_percep_base
rm -rf hv_percep_base
 git clone ssh://*************************:10022/hello_veh/hv-percp/hv_percep_deployment/hv_percep_base.git
# 拉取/更新 hv_percep_perceptron
rm -rf hv_percep_perceptron
 git clone ssh://*************************:10022/hello_veh/hv-percp/hv_percep_deployment/hv_percep_perceptron.git
# 拉取/更新 hv_percep_nodes
rm -rf hv_percep_nodes
 git clone ssh://*************************:10022/hello_veh/hv-percp/hv_percep_deployment/hv_percep_nodes.git
```
> 说明：如有新包，可按上述方式添加。

## 获取测试数据
```bash
bash ./ci/scripts/pull_data.sh --verbose
```

## 目录结构

- `src/hv_percep_base/`         基础感知算法库（C++/CUDA，含conanfile.py）
- `src/hv_percep_perceptron/`   感知推理库，依赖 hv_percep_base
- `src/hv_percep_nodes/`        ROS2 节点包，依赖 hv_percep_base 和 hv_percep_perceptron
- `conanfile.txt`               全局三方依赖与选项管理
- `setup_workspace.sh`          一键环境初始化脚本
- `build/ install/ log/`        构建输出与日志

## 快速开始

### 1. 初始化依赖环境
```bash
./setup_workspace.sh -a x86_64 -t Debug   # 可选参数 -a 架构, -t 构建类型
```
- 自动安装 Conan 依赖，生成 toolchain，配置 remote。

### 2. 构建全部包
```bash
source /opt/ros/humble/setup.bash
source build/conanrun.sh
source ./tools/setup_run_env.sh
colcon build --merge-install \
  --cmake-args "-DCMAKE_TOOLCHAIN_FILE=$(pwd)/build/conan_toolchain.cmake" "-DCMAKE_BUILD_TYPE=Debug" "-DBUILD_SHARED_LIBS=ON"
# release
colcon build --merge-install \
  --cmake-args "-DCMAKE_TOOLCHAIN_FILE=$(pwd)/build/conan_toolchain.cmake" "-DCMAKE_BUILD_TYPE=Release" "-DBUILD_SHARED_LIBS=ON"
```

### 3. 构建单个包（如 hv_percep_base）
```bash
colcon build --packages-select hv_percep_base --merge-install \
  --cmake-args "-DCMAKE_TOOLCHAIN_FILE=$(pwd)/build/conan_toolchain.cmake" "-DCMAKE_BUILD_TYPE=Debug" "-DBUILD_SHARED_LIBS=ON"

# release
colcon build --packages-select hv_percep_base --merge-install \
  --cmake-args "-DCMAKE_TOOLCHAIN_FILE=$(pwd)/build/conan_toolchain.cmake" "-DCMAKE_BUILD_TYPE=Release" "-DBUILD_SHARED_LIBS=ON"
```

### 4. 运行/测试
```bash
source install/setup.bash
source build/conanrun.sh
# 运行你的节点或测试
```

## 常见问题
- **动态库/静态库切换**：
  - 修改 conanfile.txt 的 `[options]` 部分，如 `hv_percep_base/*:shared=True`，或 conan install 时加 `-o hv_percep_base/*:shared=True`。
  - 确保 CMakeLists.txt 没有写死 SHARED/STATIC，且用 toolchain 构建。
- **找不到 so 文件**：运行前需 `source build/conanrun.sh` 并设置好 LD_LIBRARY_PATH。
- **TensorRT/GPU 依赖**：建议通过 `source tools/setup_run_env.sh` 自动设置运行环境变量（如 LD_LIBRARY_PATH、PATH），确保 TensorRT、spconv 等依赖的 so 路径已加入。

## 运行与测试

### 1. 运行 Example 可执行文件
以 hv_percep_base 和 hv_percep_perceptron 为例，编译后可执行文件位于 `build/<package>/bin/` 目录：

```bash
# 进入工作空间根目录
source install/setup.bash
source build/conanrun.sh
source tools/setup_run_env.sh

# 运行 hv_percep_base 的 example
./build/hv_percep_base/bin/basic_usage_demo
./build/hv_percep_base/bin/advanced_demo
./build/hv_percep_base/bin/performance_demo

# 运行 hv_percep_perceptron 的 example
./build/hv_percep_perceptron/bin/hv_percep_perceptron_centerpoint_preprocess_example
```

### 2. 运行 GTest 单元测试

```bash
# hv_percep_base 测试
./build/hv_percep_base/bin/hv_percep_base_tests

# hv_percep_perceptron 测试
./build/hv_percep_perceptron/bin/hv_percep_perceptron_tests
```

- 运行前请确保已 source 相关环境脚本，避免找不到依赖的 so 文件。

## 参考文档
- 各子包 README.md
- `docs/` 目录下开发、CI、Issue 流程等文档

---
如有问题请提 Issue 或联系维护者。
