rosbag2_bagfile_information:
  version: 5
  storage_identifier: sqlite3
  duration:
    nanoseconds: 34998529490
  starting_time:
    nanoseconds_since_epoch: 1746840605356345088
  message_count: 191030
  topics_with_message_count:
    - topic_metadata:
        name: /vehicle_decision_status
        type: adc_traj_msgs/msg/VehicleDecisionStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 422
    - topic_metadata:
        name: /vehicle/imu/imu_data
        type: can_service_msgs/msg/IMUData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3502
    - topic_metadata:
        name: /vehicle/gps/vel_ned
        type: can_service_msgs/msg/VelNEDData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/gps/ins_pos
        type: can_service_msgs/msg/INSPosData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3501
    - topic_metadata:
        name: /vehicle/gps/gps_pos
        type: can_service_msgs/msg/GPSPosData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 175
    - topic_metadata:
        name: /vehicle/debug/control_input
        type: can_service_msgs/msg/ControlInput
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/debug/can_service_output
        type: can_service_msgs/msg/CanServiceOutput
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/debug/can_input
        type: can_service_msgs/msg/CanInput
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/gps/gps_dop
        type: can_service_msgs/msg/GPSDoP
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1750
    - topic_metadata:
        name: /vehicle/can_ch/vin_code
        type: can_service_msgs/msg/VINCode
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 35
    - topic_metadata:
        name: /vehicle/can_ch/velocity
        type: can_service_msgs/msg/VelocityData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/can_ch/vel_pulse
        type: can_service_msgs/msg/VelPulseData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/can_ch/vehicle_signal
        type: can_service_msgs/msg/VehicleSignal
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /route_horizon
        type: adc_traj_msgs/msg/RouteHorizon
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 422
    - topic_metadata:
        name: /vehicle/can_ch/steering_signal
        type: can_service_msgs/msg/SteeringSignal
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /framework/module_status/smg
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /mapengine/hdmap
        type: mapengine_msgs/msg/Map
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/module_status/prediction
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /recorder/bag_info
        type: recorder_msgs/msg/BagInfo
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1
    - topic_metadata:
        name: /localization/lidar_filtered
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /localization/drposes
        type: localization_msgs/msg/EgoPosesData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1744
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/gear_pos
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /framework/overall_status
        type: framework_status_msgs/msg/OverallStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/gps/vel_ned
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /framework/nodes_errcode_report
        type: framework_status_msgs/msg/ErrCodesReport
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 34
    - topic_metadata:
        name: /apal_worldmodel/obstacles
        type: worldmodel_msgs/msg/Obstacles
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/imu/imu_data
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3502
    - topic_metadata:
        name: /framework/module_status/wm
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 349
    - topic_metadata:
        name: /framework/module_status/tour_task
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/can_ch/driving_mode
        type: can_service_msgs/msg/DrivingModeData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /framework/module_status/sensorservice/rslidar
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/gps/ins_data_info
        type: can_service_msgs/msg/INSDataInfo
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/driving_mode
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/adas/eps_status
        type: can_service_msgs/msg/EPSStatusReport
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/module_status/planning
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 241
    - topic_metadata:
        name: /endpoint/auto_drive_switch
        type: endpoint_msgs/msg/AutoDriveSwitchTrigger
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1167
    - topic_metadata:
        name: /recorder/request
        type: recorder_msgs/msg/RecorderRequest
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1
    - topic_metadata:
        name: /time_monitor/sensor/camera_pano_left/image_raw/compressed
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1050
    - topic_metadata:
        name: /vehicle/can_ch/gear_pos
        type: can_service_msgs/msg/GearData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /recorder/bag_header
        type: recorder_msgs/msg/BagHeader
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1
    - topic_metadata:
        name: /control/lonctrdebug
        type: controller_msgs/msg/LonCtrDebug
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3499
    - topic_metadata:
        name: /predictionmsg_proto
        type: ref_line_msgs/msg/CommonProto
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 349
    - topic_metadata:
        name: /sensor/rslidar_right/pcloud_raw
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/module_status/loc
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/gps/ins_std
        type: can_service_msgs/msg/INSStdData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /framework/module_status/control
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /apal_worldmodel/trafficlights_map
        type: worldmodel_msgs/msg/TrafficLights
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1320
    - topic_metadata:
        name: /apal_worldmodel/egoposes
        type: worldmodel_msgs/msg/EgoPosesData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1575
    - topic_metadata:
        name: /egosc_alert
        type: egosc_msgs/msg/EgoSCAlert
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /ppnc_decision_status
        type: ppnc_status_msgs/msg/Status
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 422
    - topic_metadata:
        name: /apal_percp/obstacles
        type: percp_msgs/msg/Obstacles
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/module_status/canservice/ipc_can0
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /planning_obstacles
        type: worldmodel_msgs/msg/Obstacles
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 422
    - topic_metadata:
        name: /time_monitor/sensor/rslidar_rear/pcloud_raw
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /recorder/request_source
        type: recorder_msgs/msg/RecorderRequest
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 2
    - topic_metadata:
        name: /framework/module_status/node_monitor
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /ppnc_environment_model
        type: environment_model_msgs/msg/EnvironmentModel
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 400
    - topic_metadata:
        name: /framework/module_status/percp_od
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 349
    - topic_metadata:
        name: /framework/module_status/camservice/tcu_m
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /egoposes_smooth
        type: worldmodel_msgs/msg/EgoPosesData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/latency_status
        type: framework_status_msgs/msg/LatencyStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/gps/gps_lock_status
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /framework/module_status/canservice/ipc_can1
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/adas/cruise_switch
        type: can_service_msgs/msg/CruiseSwitchStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1167
    - topic_metadata:
        name: /time_monitor/vehicle/gps/ins_std
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /sensor/rslidar_right/pcloud_distortion
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /adctrajectorymsg
        type: adc_traj_msgs/msg/AdcTrajectory
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 422
    - topic_metadata:
        name: /time_monitor/sensor/rslidar_right/pcloud_raw
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/can_bd/window_status
        type: can_service_msgs/msg/WindowStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /localization/egoposes
        type: localization_msgs/msg/EgoPosesData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1744
    - topic_metadata:
        name: /time_monitor/sensor/camera_pano_right/image_raw/compressed
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1048
    - topic_metadata:
        name: /vehicle/debug/additional_data
        type: can_service_msgs/msg/AdditionalData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/gps/gps_angle
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 175
    - topic_metadata:
        name: /time_monitor/vehicle/gps/ins_angle
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/can_bd/door_status
        type: can_service_msgs/msg/DoorStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /obstacles_smooth
        type: worldmodel_msgs/msg/Obstacles
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/module_status/canservice/tcu_s_can1
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/adas/ficm_status
        type: can_service_msgs/msg/FICMStatusReport
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /lane_speed_limit
        type: adc_traj_msgs/msg/LaneSpeedLimit
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 34
    - topic_metadata:
        name: /control/debugview
        type: controller_msgs/msg/DebugView
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 700
    - topic_metadata:
        name: /framework/module_status/canservice/tcu_s_can0
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/vel_pulse
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /ppnc_control_cmd
        type: controller_msgs/msg/ControllerCmd
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3498
    - topic_metadata:
        name: /vehicle/can_ch/epb_status
        type: can_service_msgs/msg/EPBStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /mapengine/router_source
        type: router_msgs/msg/Routers
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 5
    - topic_metadata:
        name: /vehicle/adas/esc_status
        type: can_service_msgs/msg/ESCStatusReport
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/module_status/percp_tfl
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 349
    - topic_metadata:
        name: /recorder/job_status
        type: recorder_msgs/msg/RecorderJobStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /referencelinemsg
        type: ref_line_msgs/msg/ReferenceLines
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 422
    - topic_metadata:
        name: /vehicle/gps/gps_lock_status
        type: can_service_msgs/msg/GPSLockStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /apal_worldmodel/trafficlights
        type: worldmodel_msgs/msg/TrafficLights
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 440
    - topic_metadata:
        name: /framework/module_status/hw_monitor/ipc
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 63
    - topic_metadata:
        name: /framework/module_status/loc_lidar
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /recorder/sub_request/control
        type: recorder_msgs/msg/RecorderRequest
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3499
    - topic_metadata:
        name: /sensor/camera_cabin/image_raw/compressed
        type: sensor_msgs/msg/CompressedImage
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1050
    - topic_metadata:
        name: /routing_request_source
        type: tourtask_msgs/msg/RoutingRequest
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1
    - topic_metadata:
        name: /sensor/camera_backward/image_raw/compressed
        type: sensor_msgs/msg/CompressedImage
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1045
    - topic_metadata:
        name: /vehicle/debug/can_debug
        type: can_service_msgs/msg/CanDebug
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /time_monitor/sensor/camera_backward/image_raw/compressed
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1045
    - topic_metadata:
        name: /sensor/camera_forward_far/image_raw/compressed
        type: sensor_msgs/msg/CompressedImage
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1051
    - topic_metadata:
        name: /time_monitor/chassis
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /sensor/camera_forward_wide/image_raw/compressed
        type: sensor_msgs/msg/CompressedImage
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1047
    - topic_metadata:
        name: /ego_assist_alert
        type: remote_assist_msgs/msg/EgoAssistAlert
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 400
    - topic_metadata:
        name: /sensor/rslidar_rear/pcloud_distortion
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/sensor/rslidar_left/pcloud_raw
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /sensor/camera_pano_left/image_raw/compressed
        type: sensor_msgs/msg/CompressedImage
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1050
    - topic_metadata:
        name: /vehicle/can_ch/emg_stop
        type: can_service_msgs/msg/EmergencyStopStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /adctrajectorymsgforprediction
        type: ref_line_msgs/msg/CommonProto
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 422
    - topic_metadata:
        name: /sensor/camera_pano_right/image_raw/compressed
        type: sensor_msgs/msg/CompressedImage
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1048
    - topic_metadata:
        name: /sensor/radar_front/frame
        type: sensor_service_msgs/msg/RadarFrame
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 622
    - topic_metadata:
        name: /sensor/rslidar_rear/pcloud_raw
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /sensor/rslidar_front/pcloud_distortion
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/gps/ins_angle
        type: can_service_msgs/msg/INSAngleData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /sensor/rslidar_front/pcloud_raw
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /framework/msg_status
        type: framework_status_msgs/msg/MessageStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /sensor/rslidar_left/pcloud_distortion
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /sensor/rslidar_left/pcloud_raw
        type: sensor_msgs/msg/PointCloud2
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/sensor/camera_forward_far/image_raw/compressed
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1051
    - topic_metadata:
        name: /time_monitor/sensor/camera_forward_wide/image_raw/compressed
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1047
    - topic_metadata:
        name: /time_monitor/sensor/rslidar_front/pcloud_raw
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/acc_chcan
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /time_monitor/vehicle/debug/can_service_output
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/can_bd/wiper_status
        type: can_service_msgs/msg/WiperStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/input_shaft_spd
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/debug/control_input
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /time_monitor/endpoint/auto_drive_switch
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 1167
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/steering_signal
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /chassis
        type: can_service_msgs/msg/VehicleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/vehicle_signal
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /time_monitor/vehicle/can_ch/velocity
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /time_monitor/vehicle/gps/ins_pos
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3501
    - topic_metadata:
        name: /time_monitor/vehicle/debug/can_debug
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3987
    - topic_metadata:
        name: /framework/module_status/logging/ipc
        type: framework_status_msgs/msg/ModuleStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /time_monitor/vehicle/gps/gps_pos
        type: time_monitor_msgs/msg/TimestampDifference
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 175
    - topic_metadata:
        name: /tour_traj_info
        type: tourtask_msgs/msg/TourTrajInfo
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /tour_traj_info_local_routing
        type: tourtask_msgs/msg/TourTrajInfo
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 33
    - topic_metadata:
        name: /vehicle/can_bd/light_status
        type: can_service_msgs/msg/LightStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /predict_obstacles
        type: prediction_msgs/msg/PredictObstacles
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 349
    - topic_metadata:
        name: /vehicle/adas/ems_status
        type: can_service_msgs/msg/EMSStatusReport
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/adas/ipk_status
        type: can_service_msgs/msg/IPKStatusReport
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/adas/tcu_status
        type: can_service_msgs/msg/TCUStatusReport
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/can_bd/seatbelt_status
        type: can_service_msgs/msg/SeatBeltStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/gps/gps_angle
        type: can_service_msgs/msg/GPSAngleData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 175
    - topic_metadata:
        name: /framework/ipc_status
        type: framework_status_msgs/msg/IPCStatus
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 63
    - topic_metadata:
        name: /vehicle/can_ch/acc_chcan
        type: can_service_msgs/msg/AccData
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 3500
    - topic_metadata:
        name: /vehicle/can_ch/ancillary_info
        type: can_service_msgs/msg/AncillaryInfo
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
    - topic_metadata:
        name: /vehicle/can_ch/input_shaft_spd
        type: can_service_msgs/msg/InputShaftSpeed
        serialization_format: cdr
        offered_qos_profiles: ""
      message_count: 350
  compression_format: ""
  compression_mode: ""
  relative_file_paths:
    - rosbag2_20250510-093030_0.db3
  files:
    - path: rosbag2_20250510-093030_0.db3
      starting_time:
        nanoseconds_since_epoch: 1746840605356345088
      duration:
        nanoseconds: 34998529490
      message_count: 191030